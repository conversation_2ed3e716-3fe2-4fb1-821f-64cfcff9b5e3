#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试回调函数完整执行的脚本
"""

import sys
import os
import asyncio
import threading
import time
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_callback_complete():
    """测试回调函数的完整执行"""
    print("=" * 60)
    print("回调函数完整执行测试")
    print("=" * 60)
    
    try:
        # 设置事件循环
        try:
            loop = asyncio.get_event_loop()
            print("[*] 使用现有的事件循环")
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            print("[*] 创建新的事件循环")
        
        # 导入UI模块
        import tkinter as tk
        from ui import RedeemApp, Task
        
        print("[1] 创建Tkinter根窗口...")
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口，只进行逻辑测试
        
        print("[2] 创建RedeemApp实例...")
        app = RedeemApp(root)
        
        print("[3] 创建测试任务...")
        test_params = {
            "origin": "SEA",
            "destination": "HND", 
            "date_str": "20250604",
            "flight_no_str": "NH117",
            "cabin": "Business",
            "auto_book_on_find": True,
            "passengers_info": [{
                "first_name": "QUAN",
                "last_name": "ZHANG", 
                "dob": "1968-02-07",
                "gender": "MALE",
                "title": "MR",
                "email": "<EMAIL>",
                "phone": "0400000000"
            }],
            "payment_info": {
                "card_number": "****************",
                "expiry_month": "03", 
                "expiry_year": "2026",
                "cvv": "888"
            },
            "billing_address": {
                "line1": "Test Address",
                "city": "Test City", 
                "state": "NSW",
                "postal_code": "2000",
                "country_code": "AU"
            }
        }
        
        task = Task(1, test_params)
        app.tasks.append(task)
        
        print("[4] 创建测试match_info...")
        match_info = {
            "flight_signature": {
                "offer_flight_numbers_found": ["NH117"],
                "offer_cabin_classes_found": ["Business"],
                "origin": "SEA",
                "destination": "HND",
                "date": "2025-06-04"
            },
            "original_offer_data": {"shoppingBasketHashCode": "-1146731462"},
            "original_shoppingBasketHashCode": "-1146731462",
            "proxy_used_for_success": {"http": "direct"}
        }
        
        print("[5] 直接测试_handle_flight_found_on_main_thread...")
        try:
            app._handle_flight_found_on_main_thread(match_info, 1)
            print("✅ _handle_flight_found_on_main_thread 执行完成")
        except Exception as e:
            print(f"❌ _handle_flight_found_on_main_thread 执行失败: {e}")
            import traceback
            traceback.print_exc()
        
        print("[6] 检查任务状态...")
        print(f"   任务状态: {task.status}")
        print(f"   任务日志条数: {len(task.log_messages)}")
        if task.log_messages:
            print(f"   最新日志: {task.log_messages[-1]}")
        
        print("[7] 测试auto_book_found_flight_for_task...")
        try:
            # 模拟一些必要的数据
            import redis
            r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
            
            # 存储一些测试认证数据
            auth_data = {
                "token": "test_token",
                "execution_id": "test_execution_id",
                "cookies_list": [
                    {"name": "DCSESSIONID", "value": "test_session_id"}
                ]
            }
            
            import json
            r.setex(f"task_1_auth_data", 900, json.dumps(auth_data))
            
            # 调用自动出票函数
            app.auto_book_found_flight_for_task(match_info, 1, proxy_for_booking={"http": "direct"})
            print("✅ auto_book_found_flight_for_task 调用完成")
            
        except Exception as e:
            print(f"❌ auto_book_found_flight_for_task 执行失败: {e}")
            import traceback
            traceback.print_exc()
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print(f"开始测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行测试
    test_ok = test_callback_complete()
    
    print(f"\n" + "=" * 60)
    print(f"测试结果:")
    print(f"  回调完整执行测试: {'✅ 通过' if test_ok else '❌ 失败'}")
    
    if test_ok:
        print(f"\n🎉 测试通过！")
    else:
        print(f"\n⚠️  测试失败，需要进一步调试。")
