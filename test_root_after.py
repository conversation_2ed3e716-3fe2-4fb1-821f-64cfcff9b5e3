#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 self.root.after 调用是否正常工作
"""

import sys
import os
import time
import tkinter as tk
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_root_after():
    """测试 root.after 调用"""
    print("=" * 60)
    print("root.after 调用测试")
    print("=" * 60)
    
    try:
        print("[1] 创建Tkinter根窗口...")
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        print("[2] 设置测试变量...")
        test_results = {"callback_executed": False, "callback_count": 0}
        
        def test_callback():
            print(f"[CALLBACK] 回调函数被执行! 时间: {datetime.now().strftime('%H:%M:%S.%f')}")
            test_results["callback_executed"] = True
            test_results["callback_count"] += 1
        
        print("[3] 调用 root.after(100, test_callback)...")
        root.after(100, test_callback)
        print("[3] root.after 调用完成")
        
        print("[4] 运行事件循环 2 秒...")
        start_time = time.time()
        while time.time() - start_time < 2:
            root.update()
            time.sleep(0.01)
        
        print(f"[5] 检查结果...")
        print(f"   callback_executed: {test_results['callback_executed']}")
        print(f"   callback_count: {test_results['callback_count']}")
        
        if test_results["callback_executed"]:
            print("✅ root.after 工作正常")
            success = True
        else:
            print("❌ root.after 没有执行回调")
            success = False
        
        # 测试多个回调
        print("[6] 测试多个回调...")
        test_results["callback_count"] = 0
        
        def multi_callback(n):
            print(f"[MULTI-CALLBACK] 回调 {n} 被执行")
            test_results["callback_count"] += 1
        
        for i in range(3):
            root.after(50 + i * 50, lambda n=i: multi_callback(n))
        
        print("[6] 运行事件循环 1 秒...")
        start_time = time.time()
        while time.time() - start_time < 1:
            root.update()
            time.sleep(0.01)
        
        print(f"[6] 多回调结果: {test_results['callback_count']}/3")
        
        root.destroy()
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_root_after():
    """测试UI类中的 root.after 调用"""
    print("\n" + "=" * 60)
    print("UI类 root.after 调用测试")
    print("=" * 60)
    
    try:
        from ui import RedeemApp
        
        print("[1] 创建UI应用...")
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        app = RedeemApp(root)
        
        print("[2] 设置测试变量...")
        test_results = {"callback_executed": False}
        
        def test_ui_callback():
            print(f"[UI-CALLBACK] UI回调函数被执行! 时间: {datetime.now().strftime('%H:%M:%S.%f')}")
            test_results["callback_executed"] = True
        
        print("[3] 调用 app.root.after(100, test_ui_callback)...")
        app.root.after(100, test_ui_callback)
        print("[3] app.root.after 调用完成")
        
        print("[4] 运行事件循环 2 秒...")
        start_time = time.time()
        while time.time() - start_time < 2:
            app.root.update()
            time.sleep(0.01)
        
        print(f"[5] 检查结果...")
        print(f"   callback_executed: {test_results['callback_executed']}")
        
        if test_results["callback_executed"]:
            print("✅ UI类 root.after 工作正常")
            success = True
        else:
            print("❌ UI类 root.after 没有执行回调")
            success = False
        
        root.destroy()
        return success
        
    except Exception as e:
        print(f"❌ UI测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print(f"开始测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行测试
    test1_ok = test_root_after()
    test2_ok = test_ui_root_after()
    
    print(f"\n" + "=" * 60)
    print(f"测试结果:")
    print(f"  基础 root.after 测试: {'✅ 通过' if test1_ok else '❌ 失败'}")
    print(f"  UI类 root.after 测试: {'✅ 通过' if test2_ok else '❌ 失败'}")
    
    if test1_ok and test2_ok:
        print(f"\n🎉 所有测试通过！")
    else:
        print(f"\n⚠️  部分测试失败，需要进一步调试。")
