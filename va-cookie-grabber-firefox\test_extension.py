#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Virgin Australia Cookie Grabber扩展的WebSocket连接和通信
"""

import asyncio
import websockets
import json
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ExtensionTester:
    def __init__(self, host='127.0.0.1', port=8765):
        self.host = host
        self.port = port
        self.websocket = None
        
    async def start_server(self):
        """启动WebSocket服务器"""
        logger.info(f"启动WebSocket服务器 ws://{self.host}:{self.port}")
        
        async def handle_client(websocket, path):
            self.websocket = websocket
            logger.info(f"扩展已连接: {websocket.remote_address}")
            
            try:
                # 发送测试任务
                await self.send_test_task()
                
                # 监听扩展响应
                async for message in websocket:
                    await self.handle_message(message)
                    
            except websockets.exceptions.ConnectionClosed:
                logger.info("扩展连接已关闭")
            except Exception as e:
                logger.error(f"处理扩展消息时出错: {e}")
        
        # 启动服务器
        server = await websockets.serve(handle_client, self.host, self.port)
        logger.info("WebSocket服务器已启动，等待扩展连接...")
        
        # 保持服务器运行
        await server.wait_closed()
    
    async def send_test_task(self):
        """发送测试任务给扩展"""
        if not self.websocket:
            logger.error("WebSocket连接不可用")
            return
            
        test_task = {
            "action": "navigate_and_get_cookies",
            "url": "https://book.virginaustralia.com/dx/VADX/#/flight-selection?ADT=1&class=Business&origin=SYD&destination=MEL&date=12-25-2024&awardBooking=true",
            "source": "test_request"
        }
        
        try:
            await self.websocket.send(json.dumps(test_task))
            logger.info("已发送测试任务给扩展")
        except Exception as e:
            logger.error(f"发送测试任务失败: {e}")
    
    async def handle_message(self, message):
        """处理来自扩展的消息"""
        try:
            data = json.loads(message)
            msg_type = data.get('type', 'unknown')
            
            if msg_type == 'status_update':
                if 'error' in data:
                    logger.error(f"扩展错误: {data['error']}")
                else:
                    logger.info(f"扩展状态: {data.get('message', 'No message')}")
                    
            elif msg_type == 'cookie_data':
                logger.info("收到Cookie数据!")
                logger.info(f"Cookie数量: {len(data.get('payload', []))}")
                logger.info(f"Execution ID: {data.get('execution_id', 'N/A')}")
                logger.info(f"User Agent: {data.get('user_agent', 'N/A')[:50]}...")
                
                # 显示部分Cookie信息
                cookies = data.get('payload', [])
                if cookies:
                    logger.info("部分Cookie信息:")
                    for i, cookie in enumerate(cookies[:5]):  # 只显示前5个
                        logger.info(f"  {i+1}. {cookie.get('name', 'N/A')}: {cookie.get('value', 'N/A')[:20]}...")
                
            else:
                logger.info(f"收到未知类型消息: {msg_type}")
                
        except json.JSONDecodeError:
            logger.error(f"无法解析消息: {message}")
        except Exception as e:
            logger.error(f"处理消息时出错: {e}")

async def main():
    """主函数"""
    tester = ExtensionTester()
    
    try:
        await tester.start_server()
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务器...")
    except Exception as e:
        logger.error(f"服务器运行出错: {e}")

if __name__ == "__main__":
    print("Virgin Australia Cookie Grabber 扩展测试器")
    print("=" * 50)
    print("1. 确保Firefox浏览器已安装并加载了扩展")
    print("2. 启动此测试脚本")
    print("3. 扩展将自动连接并执行测试任务")
    print("=" * 50)
    
    asyncio.run(main())
