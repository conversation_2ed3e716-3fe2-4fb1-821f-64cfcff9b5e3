# Virgin Australia Cookie Grabber - Cookie自动清理功能

## 功能概述

为了支持多账号挂单操作，扩展现在支持在每次获取Cookie后自动清理浏览器中的Virgin Australia相关Cookie，确保下次获取时重新登录，避免账号冲突。

## 主要特性

### 1. 自动Cookie清理
- **默认启用**：每次获取Cookie任务完成后自动清理
- **智能清理**：只清理Virgin Australia相关域名的Cookie
- **多域名支持**：清理所有相关子域名的Cookie

### 2. 可配置清理模式
- **自动清理模式**：`autoClearCookies: true` (默认)
- **保持登录模式**：`autoClearCookies: false`
- **手动清理**：随时可通过命令清理Cookie

### 3. 清理范围
清理以下域名的所有Cookie：
- `.virginaustralia.com`
- `virginaustralia.com`
- `.book.virginaustralia.com`
- `book.virginaustralia.com`
- `.www.virginaustralia.com`
- `www.virginaustralia.com`
- `.velocity.virginaustralia.com`
- `velocity.virginaustralia.com`

## 使用方法

### 1. 自动清理模式（推荐用于多账号）

```json
{
    "action": "navigate_and_get_cookies",
    "url": "https://book.virginaustralia.com/dx/VADX/#/flight-selection?...",
    "autoClearCookies": true
}
```

**适用场景**：
- 多个账号需要轮流挂单
- 不同卡片使用不同账号
- 需要确保每次都是全新登录状态

### 2. 保持登录模式（用于单账号）

```json
{
    "action": "navigate_and_get_cookies",
    "url": "https://book.virginaustralia.com/dx/VADX/#/flight-selection?...",
    "autoClearCookies": false
}
```

**适用场景**：
- 单一账号连续操作
- 需要保持登录状态
- 减少重复登录次数

### 3. 手动清理Cookie

```json
{
    "action": "clear_cookies"
}
```

**适用场景**：
- 需要立即清理Cookie
- 切换账号前的准备
- 解决登录状态异常

## 工作流程

### 自动清理模式流程
1. 接收获取Cookie任务
2. 导航到指定URL
3. 执行登录（如果需要）
4. 提取Cookie和相关数据
5. 发送数据到Python
6. **自动清理所有VA Cookie**
7. 关闭标签页
8. 完成任务

### 手动清理流程
1. 接收清理Cookie命令
2. 扫描所有VA相关域名
3. 删除找到的所有Cookie
4. 返回清理结果统计

## 配置示例

### Python端发送任务
```python
# 多账号模式 - 自动清理
task = {
    "action": "navigate_and_get_cookies",
    "url": "https://book.virginaustralia.com/dx/VADX/#/flight-selection?ADT=1&class=Business&origin=SYD&destination=MEL&date=12-25-2024&awardBooking=true",
    "credentials": {
        "username": "<EMAIL>",
        "password": "password1"
    },
    "autoClearCookies": True  # 自动清理
}

# 单账号模式 - 保持登录
task = {
    "action": "navigate_and_get_cookies",
    "url": "https://book.virginaustralia.com/dx/VADX/#/flight-selection?ADT=1&class=Business&origin=SYD&destination=MEL&date=12-25-2024&awardBooking=true",
    "autoClearCookies": False  # 保持登录
}

# 手动清理
clear_command = {
    "action": "clear_cookies"
}
```

## 日志输出示例

### 自动清理成功
```
[VA BackgroundHost] Cookie数据已发送到Python (源: multi_account)。准备清理Cookie并关闭标签页 123...
[VA BackgroundHost] 开始清理Virgin Australia相关Cookie...
[VA BackgroundHost] 找到 15 个Cookie在域名: .virginaustralia.com
[VA BackgroundHost] 已删除Cookie: DCSESSIONID (域名: .virginaustralia.com)
[VA BackgroundHost] 已删除Cookie: reese84 (域名: .virginaustralia.com)
[VA BackgroundHost] Cookie清理完成，共清理了 23 个Cookie
[VA BackgroundHost] 标签页 123 已自动关闭，Cookie已清理。
```

### 保持登录模式
```
[VA BackgroundHost] Cookie数据已发送到Python (源: single_account)。准备关闭标签页 124...
[VA BackgroundHost] 跳过Cookie清理 (autoClearCookies=false)
[VA BackgroundHost] 标签页 124 已自动关闭。
```

## 测试方法

### 1. 使用测试脚本
```bash
python test_extension.py
```

选择测试选项：
- 选项1：获取Cookie (自动清理)
- 选项2：获取Cookie (不清理)
- 选项3：手动清理Cookie

### 2. 验证清理效果
1. 在Firefox中打开开发者工具
2. 进入Application/Storage标签
3. 查看Cookies部分
4. 确认virginaustralia.com相关Cookie已被清理

## 注意事项

1. **权限要求**：扩展需要`cookies`权限才能清理Cookie
2. **域名匹配**：只清理Virgin Australia相关域名的Cookie
3. **错误处理**：清理失败不会影响主要功能
4. **性能影响**：清理操作很快，通常在1秒内完成
5. **安全性**：只清理指定域名，不影响其他网站Cookie

## 故障排除

### Cookie清理失败
- 检查扩展权限设置
- 确认Firefox版本兼容性
- 查看控制台错误信息

### 登录状态异常
- 手动执行Cookie清理
- 重启浏览器
- 检查账号凭据是否正确

## 更新日志

- **v1.1**: 添加自动Cookie清理功能
- **v1.1**: 支持可配置清理模式
- **v1.1**: 添加手动清理命令
- **v1.1**: 增强错误处理和日志记录
