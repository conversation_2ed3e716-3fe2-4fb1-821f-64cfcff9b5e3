#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试使用完整Cookie的搜索功能
"""

import sys
import os
import json
import redis
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_search_with_cookies():
    """测试使用完整Cookie的搜索功能"""
    print("=" * 60)
    print("使用完整Cookie的搜索功能测试")
    print("=" * 60)
    
    try:
        from high_concurrency_crawler import search_flights_with_demo
        
        # 1. 创建测试用的认证数据
        print("[1] 创建测试认证数据...")
        test_auth_data = {
            "token": "test_token",
            "execution_id": "test_execution_id",
            "cookies_list": [
                {"name": "DCSESSIONID", "value": "test_session_id_12345"},
                {"name": "cookieStartTime", "value": "1748513745190"},
                {"name": "reese84", "value": "test_reese84_token"}
            ]
        }
        
        # 2. 存储到Redis
        print("[2] 存储认证数据到Redis...")
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        r.setex(f"task_999_auth_data", 900, json.dumps(test_auth_data))
        print("✅ 认证数据已存储到Redis")
        
        # 3. 测试不使用Cookie的搜索
        print("[3] 测试基础搜索（仅reese84）...")
        result_basic = search_flights_with_demo(
            origin="SEA",
            destination="HND",
            date="2025-06-04",
            cabin="Business",
            proxy_dict={},
            task_id_for_redis=None  # 不使用Redis Cookie
        )
        
        print(f"[3] 基础搜索结果类型: {type(result_basic)}")
        if isinstance(result_basic, dict):
            if result_basic.get("errors"):
                print(f"[3] 基础搜索错误: {result_basic['errors'][0]['message'][:100]}...")
            elif result_basic.get("data"):
                print(f"[3] 基础搜索成功")
            else:
                print(f"[3] 基础搜索返回空数据")
        
        # 4. 测试使用完整Cookie的搜索
        print("[4] 测试完整Cookie搜索...")
        result_with_cookies = search_flights_with_demo(
            origin="SEA",
            destination="HND",
            date="2025-06-04",
            cabin="Business",
            proxy_dict={},
            task_id_for_redis=999  # 使用Redis Cookie
        )
        
        print(f"[4] 完整Cookie搜索结果类型: {type(result_with_cookies)}")
        if isinstance(result_with_cookies, dict):
            if result_with_cookies.get("errors"):
                print(f"[4] 完整Cookie搜索错误: {result_with_cookies['errors'][0]['message'][:100]}...")
            elif result_with_cookies.get("data"):
                print(f"[4] 完整Cookie搜索成功")
                # 检查航班数据
                offers_data = result_with_cookies.get("data", {}).get("bookingAirSearch", {}).get("originalResponse", {})
                unbundled_offers = offers_data.get("unbundledOffers", [])
                if unbundled_offers and unbundled_offers[0]:
                    offer_count = len(unbundled_offers[0])
                    print(f"[4] 找到 {offer_count} 个航班选项")
                else:
                    print(f"[4] 无航班数据")
            else:
                print(f"[4] 完整Cookie搜索返回空数据")
        
        # 5. 比较结果
        print("[5] 结果比较...")
        basic_has_errors = isinstance(result_basic, dict) and result_basic.get("errors")
        cookies_has_errors = isinstance(result_with_cookies, dict) and result_with_cookies.get("errors")
        
        if basic_has_errors and not cookies_has_errors:
            print("✅ 使用完整Cookie解决了搜索问题！")
            success = True
        elif not basic_has_errors and not cookies_has_errors:
            print("✅ 两种方法都成功，Cookie增强了兼容性")
            success = True
        elif basic_has_errors and cookies_has_errors:
            print("❌ 两种方法都失败，可能是其他问题")
            success = False
        else:
            print("⚠️  基础搜索成功但Cookie搜索失败，需要进一步调试")
            success = False
        
        # 6. 清理Redis数据
        print("[6] 清理测试数据...")
        r.delete(f"task_999_auth_data")
        print("✅ 测试数据已清理")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_crawler_with_cookies():
    """测试爬虫使用完整Cookie"""
    print("\n" + "=" * 60)
    print("爬虫使用完整Cookie测试")
    print("=" * 60)
    
    try:
        from high_concurrency_crawler import HighConcurrencyCrawler
        from api_client import VAApiClient
        
        # 1. 创建测试认证数据
        print("[1] 创建测试认证数据...")
        test_auth_data = {
            "token": "test_token",
            "execution_id": "test_execution_id", 
            "cookies_list": [
                {"name": "DCSESSIONID", "value": "test_session_id_12345"},
                {"name": "cookieStartTime", "value": "1748513745190"}
            ]
        }
        
        # 2. 存储到Redis
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        r.setex(f"task_888_auth_data", 900, json.dumps(test_auth_data))
        
        print("[2] 创建爬虫实例...")
        crawler = HighConcurrencyCrawler(max_workers=1)  # 使用1个工作者进行测试
        
        print("[3] 添加测试目标...")
        crawler.add_target("SEA", "HND", "2025-06-04", "NH117")
        
        print("[4] 设置回调函数...")
        test_results = {"callback_called": False, "match_info": None}
        
        def test_callback(match_info, task_id):
            print(f"[CALLBACK] 收到回调: task_id={task_id}")
            test_results["callback_called"] = True
            test_results["match_info"] = match_info
        
        crawler.set_callback(test_callback)
        
        print("[5] 启动爬虫...")
        api_client = VAApiClient()
        success = crawler.start(task_id_for_redis=888, initial_api_client_template=api_client)
        
        if not success:
            print("❌ 爬虫启动失败")
            return False
        
        print("✅ 爬虫启动成功，监控10秒...")
        
        import time
        for i in range(10):
            time.sleep(1)
            print(f"[{i+1:2d}s] 监控中... (found_match: {crawler.found_match})")
            
            if crawler.found_match or test_results["callback_called"]:
                print("✅ 找到匹配航班或收到回调")
                break
        
        print("[6] 停止爬虫...")
        crawler.stop()
        
        print(f"[7] 测试结果:")
        print(f"   callback_called: {test_results['callback_called']}")
        print(f"   found_match: {crawler.found_match}")
        
        # 清理Redis数据
        r.delete(f"task_888_auth_data")
        
        return test_results["callback_called"] or crawler.found_match
        
    except Exception as e:
        print(f"❌ 爬虫测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print(f"开始测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行测试
    test1_ok = test_search_with_cookies()
    test2_ok = test_crawler_with_cookies()
    
    print(f"\n" + "=" * 60)
    print(f"测试结果:")
    print(f"  搜索Cookie增强测试: {'✅ 通过' if test1_ok else '❌ 失败'}")
    print(f"  爬虫Cookie集成测试: {'✅ 通过' if test2_ok else '❌ 失败'}")
    
    if test1_ok and test2_ok:
        print(f"\n🎉 所有测试通过！Cookie增强功能正常工作。")
    else:
        print(f"\n⚠️  部分测试失败，需要进一步调试。")
