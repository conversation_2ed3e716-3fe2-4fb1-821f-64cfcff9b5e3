<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Virgin Australia API Recorder</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      width: 400px;
      margin: 0;
      padding: 15px;
      background-color: #f8f9fa;
    }
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #dee2e6;
    }
    .title {
      font-size: 18px;
      font-weight: bold;
      color: #702082; /* VA紫色 */
    }
    .status {
      font-size: 14px;
      padding: 5px 10px;
      border-radius: 4px;
      font-weight: bold;
    }
    .status.recording {
      background-color: #dc3545;
      color: white;
    }
    .status.idle {
      background-color: #6c757d;
      color: white;
    }
    .controls {
      display: flex;
      gap: 10px;
      margin-bottom: 15px;
    }
    button {
      padding: 8px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
      transition: background-color 0.2s;
    }
    button:hover {
      opacity: 0.9;
    }
    .start-btn {
      background-color: #28a745;
      color: white;
    }
    .stop-btn {
      background-color: #dc3545;
      color: white;
    }
    .clear-btn {
      background-color: #6c757d;
      color: white;
    }
    .export-btn {
      background-color: #007bff;
      color: white;
    }
    .records-container {
      max-height: 300px;
      overflow-y: auto;
      margin-bottom: 15px;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      background-color: white;
    }
    .record-item {
      padding: 10px;
      border-bottom: 1px solid #dee2e6;
      cursor: pointer;
    }
    .record-item:hover {
      background-color: #f1f1f1;
    }
    .record-item:last-child {
      border-bottom: none;
    }
    .operation-name {
      font-weight: bold;
      color: #702082;
    }
    .timestamp {
      font-size: 12px;
      color: #6c757d;
    }
    .status-code {
      font-size: 12px;
      padding: 2px 5px;
      border-radius: 3px;
      margin-left: 5px;
    }
    .status-code.success {
      background-color: #d4edda;
      color: #155724;
    }
    .status-code.error {
      background-color: #f8d7da;
      color: #721c24;
    }
    .detail-view {
      display: none;
      padding: 10px;
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      margin-top: 10px;
    }
    .detail-view pre {
      white-space: pre-wrap;
      word-break: break-all;
      max-height: 200px;
      overflow-y: auto;
      background-color: #f1f1f1;
      padding: 10px;
      border-radius: 4px;
    }
    .tab-container {
      display: flex;
      border-bottom: 1px solid #dee2e6;
      margin-bottom: 10px;
    }
    .tab {
      padding: 8px 15px;
      cursor: pointer;
      border-bottom: 2px solid transparent;
    }
    .tab.active {
      border-bottom: 2px solid #702082;
      font-weight: bold;
    }
    .tab-content {
      display: none;
    }
    .tab-content.active {
      display: block;
    }
    .empty-message {
      text-align: center;
      padding: 20px;
      color: #6c757d;
    }
    .footer {
      font-size: 12px;
      color: #6c757d;
      text-align: center;
      margin-top: 15px;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="title">Virgin Australia API Recorder</div>
    <div id="status" class="status idle">空闲</div>
  </div>
  
  <div class="controls">
    <button id="startBtn" class="start-btn">开始记录</button>
    <button id="stopBtn" class="stop-btn" disabled>停止记录</button>
    <button id="clearBtn" class="clear-btn">清除记录</button>
    <button id="exportBtn" class="export-btn">导出记录</button>
  </div>
  
  <div id="recordsContainer" class="records-container">
    <div class="empty-message">暂无记录</div>
  </div>
  
  <div id="detailView" class="detail-view">
    <div class="tab-container">
      <div class="tab active" data-tab="request">请求</div>
      <div class="tab" data-tab="variables">变量</div>
      <div class="tab" data-tab="headers">请求头</div>
      <div class="tab" data-tab="cookies">Cookie</div>
      <div class="tab" data-tab="query">查询</div>
    </div>
    
    <div id="requestTab" class="tab-content active">
      <pre id="requestDetail"></pre>
    </div>
    
    <div id="variablesTab" class="tab-content">
      <pre id="variablesDetail"></pre>
    </div>
    
    <div id="headersTab" class="tab-content">
      <pre id="headersDetail"></pre>
    </div>
    
    <div id="cookiesTab" class="tab-content">
      <pre id="cookiesDetail"></pre>
    </div>
    
    <div id="queryTab" class="tab-content">
      <pre id="queryDetail"></pre>
    </div>
  </div>
  
  <div class="footer">
    Virgin Australia API Recorder v1.0 | 记录关键API请求，辅助分析预订流程
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
