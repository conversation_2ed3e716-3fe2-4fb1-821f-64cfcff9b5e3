#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟真实场景的测试
"""

import sys
import os
import asyncio
import threading
import time
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_real_scenario():
    """模拟真实的UI场景"""
    print("=" * 60)
    print("真实场景模拟测试")
    print("=" * 60)
    
    try:
        # 设置事件循环（模拟主程序）
        try:
            loop = asyncio.get_event_loop()
            print("[*] 使用现有的事件循环")
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            print("[*] 创建新的事件循环")
        
        # 导入UI模块
        import tkinter as tk
        from ui import RedeemApp, Task
        
        print("[1] 创建Tkinter根窗口...")
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口，只进行逻辑测试
        
        print("[2] 创建RedeemApp实例...")
        app = RedeemApp(root)
        
        print("[3] 验证RedeemApp.get_instance()...")
        instance = RedeemApp.get_instance()
        print(f"   RedeemApp._instance = {RedeemApp._instance}")
        print(f"   RedeemApp.get_instance() = {instance}")
        print(f"   app == instance: {app == instance}")
        
        print("[4] 创建测试任务...")
        test_params = {
            "origin": "SEA",
            "destination": "HND",
            "date_str": "20250604",
            "flight_no_str": "NH117",
            "cabin": "Business",
            "auto_book_on_find": True,
            "passengers_info": [{"first_name": "TEST", "last_name": "USER", "dob": "1990-01-01", "gender": "MALE"}],
            "payment_info": {"card_number": "1234567890123456", "expiry_month": "12", "expiry_year": "2025", "cvv": "123"},
            "billing_address": {"line1": "Test Address", "city": "Test City", "state": "XX", "postal_code": "0000", "country_code": "AU"}
        }
        
        task = Task(1, test_params)
        app.tasks.append(task)
        
        print("[5] 验证任务查找...")
        found_task = app.get_task_by_id(1)
        print(f"   app.get_task_by_id(1) = {found_task}")
        print(f"   task == found_task: {task == found_task}")
        
        print("[6] 测试task.log_task_message...")
        task.log_task_message("测试日志消息")
        
        print("[7] 模拟回调函数调用...")
        
        # 模拟match_info
        match_info = {
            "flight_signature": {
                "offer_flight_numbers_found": ["NH117"],
                "offer_cabin_classes_found": ["Business"],
                "origin": "SEA",
                "destination": "HND",
                "date": "2025-06-04"
            },
            "original_offer_data": {"shoppingBasketHashCode": "-1146731462"},
            "original_shoppingBasketHashCode": "-1146731462"
        }
        
        # 模拟从工作线程调用回调
        def simulate_callback():
            print(f"[WORKER THREAD] 模拟工作线程调用回调...")
            app.on_flight_found_for_task(match_info, 1)
        
        # 启动工作线程
        worker_thread = threading.Thread(target=simulate_callback, daemon=True)
        worker_thread.start()
        
        print("[8] 运行事件循环...")
        
        async def run_test():
            print(f"   事件循环开始运行 (线程: {threading.current_thread().name})")
            await asyncio.sleep(3)  # 给回调足够时间执行
            print(f"   事件循环运行完成")
        
        try:
            loop.run_until_complete(run_test())
            
            print(f"\n[9] 检查测试结果:")
            print(f"   任务状态: {task.status}")
            print(f"   任务日志条数: {len(task.log_messages)}")
            if task.log_messages:
                print(f"   最新日志: {task.log_messages[-1]}")
            
            return True
                
        except Exception as e:
            print(f"   ❌ 事件循环运行失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        finally:
            worker_thread.join(timeout=1)
            root.destroy()
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_callback():
    """简单的回调测试"""
    print("\n" + "=" * 60)
    print("简单回调测试")
    print("=" * 60)
    
    try:
        # 导入必要模块
        from ui import Task, RedeemApp
        import tkinter as tk
        
        # 创建简单的应用实例
        root = tk.Tk()
        root.withdraw()
        
        app = RedeemApp(root)
        
        # 创建任务
        test_params = {"origin": "SEA", "destination": "HND", "auto_book_on_find": True}
        task = Task(1, test_params)
        app.tasks.append(task)
        
        print("[1] 直接测试task.log_task_message...")
        task.log_task_message("直接测试日志")
        
        print("[2] 直接测试app._handle_flight_found_on_main_thread...")
        match_info = {"flight_signature": {"offer_flight_numbers_found": ["NH117"]}}
        app._handle_flight_found_on_main_thread(match_info, 1)
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 简单回调测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print(f"开始测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行测试
    test1_ok = test_real_scenario()
    test2_ok = test_simple_callback()
    
    print(f"\n" + "=" * 60)
    print(f"测试结果:")
    print(f"  真实场景测试: {'✅ 通过' if test1_ok else '❌ 失败'}")
    print(f"  简单回调测试: {'✅ 通过' if test2_ok else '❌ 失败'}")
    
    if test1_ok and test2_ok:
        print(f"\n🎉 所有测试通过！")
    else:
        print(f"\n⚠️  部分测试失败，需要进一步调试。")
{
  "url": "https://book.virginaustralia.com/dx/VADX/#/flight-selection?promoCode=FLV28&showPromoCode=0&ADT=1&class=Business&awardBooking=true&pos=au-en&activeMonth=06-04-2025&journeyType=one-way&date=06-04-2025&origin=SEA&destination=HND&execution=4e23984c-d7fd-4370-a194-fd7dc227fe74",
  "executionId": "4e23984c-d7fd-4370-a194-fd7dc227fe74",
  "flightId": "",
  "DCSESSIONID": "130cc6ee-b769-43ca-9992-63c9a272e031-TargetGroupA",
  "itinerary": {
    "origin": "SEA",
    "destination": "HND",
    "date": "2025-06-04",
    "cabinClass": "Business",
    "awardBooking": true
  },
  "headers": {
    "x-sabre-storefront": "VADX",
    "authorization": "Bearer Basic anNvbl91c2VyOmpzb25fcGFzc3dvcmQ=",
    "application-id": "SWS1:SBR-DigConShpBk:fd34efe9a9",
    "content-type": "application/json",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; rv:134.0) Gecko/20100101 Firefox/134.0"
  },
  "cookies": [
    {
      "name": "cookieStartTime",
      "value": "1748510672600",
      "domain": "book.virginaustralia.com",
      "path": "/dx/VADX",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": true,
      "expirationDate": 1748512495
    },
    {
      "name": "ADRUM",
      "value": "s~1748510673545&r~****************************************************************************************************",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "lax",
      "storeId": "firefox-default",
      "session": true,
      "hostOnly": false
    },
    {
      "name": "tfpsi",
      "value": "6601f01c-9c3b-4c81-bedf-6638b9df050f",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1748512481
    },
    {
      "name": "visid_incap_1768154",
      "value": "gPMkXAxnS0yGOy09NPsltc0nOGgAAAAAQUIPAAAAAABklnNrGbyO5RI5VMpO5rTM",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": false,
      "httpOnly": true,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1779983906
    },
    {
      "name": "incap_ses_1789_1768154",
      "value": "LRCIODZCXFsHeqqpW87TGM0nOGgAAAAA3qIYB8DdxO8GddpgDhGLIw==",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": false,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": true,
      "hostOnly": false
    },
    {
      "name": "nlbi_1768154_2147483392",
      "value": "APpvXSC4qlG9trclKIrHqgAAAAAuRbPc52tWzP2ZeTKB1B4J",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": false,
      "httpOnly": true,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": true,
      "hostOnly": false
    },
    {
      "name": "reese84",
      "value": "3:YZPnxWDRFGD9f/XRSe1rWA==:7ScqL8MuBmkaCrXkeT6DbkoknPy9bLff3I6sfpw9u36oW54QMhpPoOVK5Ny5yqC5OK3SlavvlyS2uJHuwyVxhrM7nFeT1X5vyV2M2zMBpFCc9sSM54lDtX1uXj/+WQwVaLVjG1qebdC7VQQUHBn3CTnW8Zbjtag3foOvNbib1nch70mBZ6VqWwYRXsikw1yNiaqR+fxtwoF4LtvHKhTxbviAeeduj+iv93IBoCrDmrc9kP9z/i+DeCCSbGGeyr1no8Q4MYiImaID8Yk48e2KzLmLdaoAKCGc1HGMWJQ+G0pWTyGJ40wzUE+ycBdata5URxq9g0uCh2ojA0ltL8lG2QutSRpeqFWVmJoMsydJFd2lBGZZSRIl0cpUOAnmq+JnvUSv0L3Tx76l0DQZLkpxH+ak/eTJoZzEDVRgkhS5OCFAiPOdPj6/IeRLtv52C7jMHwX9z0q8hjQaaIrE9qXWk4KWeDfbYZfzqiDuEs1hvAxy9sxdKDPti93I3m4ozvqLQobYqUEBd96X2131CQeQwYnH9oCJvun8rpuLdJ7Awd0=:CxGbh9eg/3qtNTINa5bZS9fcRmh/C2dZzN8Kvic/gsY=",
      "domain": ".book.virginaustralia.com",
      "path": "/",
      "secure": false,
      "httpOnly": false,
      "sameSite": "lax",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1751102674
    },
    {
      "name": "AWSALB",
      "value": "EFBLMHZuYGrNpRdEQeb0IUcmegaPGTJJjASvaUu8CuG728y+qSIlOM0jfSPql3CLhFHYtG00V5NggVGO/L8vd2WnIoy+ePTrDyDpEL714Bi4Q4NGT0f2eKzKLq0y",
      "domain": "book.virginaustralia.com",
      "path": "/",
      "secure": false,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": true,
      "expirationDate": 1749115478
    },
    {
      "name": "AWSALBCORS",
      "value": "EFBLMHZuYGrNpRdEQeb0IUcmegaPGTJJjASvaUu8CuG728y+qSIlOM0jfSPql3CLhFHYtG00V5NggVGO/L8vd2WnIoy+ePTrDyDpEL714Bi4Q4NGT0f2eKzKLq0y",
      "domain": "book.virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": true,
      "expirationDate": 1749115478
    },
    {
      "name": "CID",
      "value": "cmb963we31qjgodq00qr0ekqf",
      "domain": "book.virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": true,
      "sameSite": "lax",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": true,
      "expirationDate": 1748532270
    },
    {
      "name": "SSWGID",
      "value": "cmb963we31qjgodq00qr0ekqf",
      "domain": "book.virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": true,
      "sameSite": "lax",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": true,
      "expirationDate": 1783070670
    },
    {
      "name": "ADRUM_BT",
      "value": "R:0|i:792809|g:acf9b098-795d-42d0-8640-0d61f956d2c5160259|e:1|n:Sabre_9e6c885b-de5c-4175-ac13-773e8d4b7ab2",
      "domain": "book.virginaustralia.com",
      "path": "/",
      "secure": false,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": true,
      "expirationDate": 1748510703
    },
    {
      "name": "nlbi_1768154",
      "value": "WarLfGSFznKZ7k80KIrHqgAAAAAXW7oVZWZYoFjoRRKGwLlT",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": false,
      "httpOnly": true,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": true,
      "hostOnly": false
    },
    {
      "name": "nlbi_1768154_2612579",
      "value": "LUYiO8t6JiPc8vJuKIrHqgAAAAAYDkS9xI5nmSCioZx56r0w",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": false,
      "httpOnly": true,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": true,
      "hostOnly": false
    },
    {
      "name": "cc_endpoint",
      "value": "SG",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1748512474
    },
    {
      "name": "cc_validEndpoint",
      "value": "SG",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1748597074
    },
    {
      "name": "AMCV_66F360B351E56BF50A490D4D%40AdobeOrg",
      "value": "-1041837474%7CMCIDTS%7C20238%7CMCMID%7C60525116580508649490418688628174421450%7CMCAID%7CNONE%7CMCOPTOUT-1748517895s%7CNONE%7CMCAAMLH-1749115495%7C3%7CMCAAMB-1749115495%7Cj8Odv6LonN4r3an7LhD3WZrU1bUpAkFkkiY1ncBR96t2PTI%7CvVersion%7C5.5.0%7CMCCIDH%7C-*********%7CMCSYNCSOP%7C411-20245",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1783070696
    },
    {
      "name": "at_check",
      "value": "true",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": false,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": true,
      "hostOnly": false
    },
    {
      "name": "mbox",
      "value": "session#********************************#1748512533|PC#********************************.38_0#1811755474",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": false,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1783070673
    },
    {
      "name": "promoCode",
      "value": "FLV28",
      "domain": "book.virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": true,
      "hostOnly": true
    },
    {
      "name": "session_flowtype",
      "value": "Normal%20Flow",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1748512495
    },
    {
      "name": "_cls_v",
      "value": "63ee8950-9a8e-4ef4-939e-128c9401190c",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1783070675
    },
    {
      "name": "_cls_s",
      "value": "2b84cb39-cf4a-460f-a43d-e1c079b87ab6:0",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": true,
      "hostOnly": false
    },
    {
      "name": "Bc",
      "value": "d:0*null_p:0*0.05_r:null*null",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": true,
      "hostOnly": false
    },
    {
      "name": "cc_behaviour",
      "value": "opt-out",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1764235479
    },
    {
      "name": "DCSESSIONID",
      "value": "130cc6ee-b769-43ca-9992-63c9a272e031-TargetGroupA",
      "domain": "book.virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": true,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": true,
      "expirationDate": 1748511895
    },
    {
      "name": "s_ecid",
      "value": "MCMID%7C60525116580508649490418688628174421450",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": false,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1783070695
    },
    {
      "name": "AMCVS_66F360B351E56BF50A490D4D%40AdobeOrg",
      "value": "1",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": true,
      "hostOnly": false
    },
    {
      "name": "rto",
      "value": "c0",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": true,
      "hostOnly": false
    },
    {
      "name": "mboxEdgeCluster",
      "value": "38",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": false,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1748512533
    },
    {
      "name": "QuantumMetricSessionID",
      "value": "4fdc604b8f1161a3595359fa178325fa",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1748512496
    },
    {
      "name": "QuantumMetricUserID",
      "value": "35155ed33708845ec3cf7111822920fa",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1780046677
    },
    {
      "name": "utag_main",
      "value": "v_id:01971b5b9bfa0002a1c270aebebc0504a011100d00fb8$_sn:1$_se:2$_ss:0$_st:1748512495098$ses_id:1748510678010%3Bexp-session$_pn:1%3Bexp-session$_prevpage:book%3Avirginaustralia%3Aair_select_page%3Bexp-1748514278690$vapi_domain:virginaustralia.com$dc_visit:1$dc_event:2%3Bexp-session$dc_region:ap-southeast-2%3Bexp-session",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1780046695
    },
    {
      "name": "entry_temp",
      "value": "others",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1748512495
    },
    {
      "name": "_uetsid",
      "value": "edd7e7603a3e11f0ba209f905c99b60f",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": false,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1748597078
    },
    {
      "name": "_uetvid",
      "value": "febe3e2038c311f0b46cdd1880a6c55d",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": false,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1782206678
    },
    {
      "name": "_gcl_au",
      "value": "1.1.*********.1748510679",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": false,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1756286679
    },
    {
      "name": "s_nr",
      "value": "1748510695656-New",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": **********
    },
    {
      "name": "s_cc",
      "value": "true",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": true,
      "hostOnly": false
    },
    {
      "name": "cc_cookie",
      "value": "%7B%22categories%22%3A%5B%22necessary%22%2C%22performance%22%2C%22functional%22%2C%22targeting%22%5D%2C%22revision%22%3A0%2C%22data%22%3Anull%2C%22consentTimestamp%22%3A%222025-05-29T09%3A24%3A39.070Z%22%2C%22consentId%22%3A%229195e436-1ff2-4e00-b928-a77c8b507bed%22%2C%22services%22%3A%7B%22necessary%22%3A%5B%5D%2C%22performance%22%3A%5B%5D%2C%22functional%22%3A%5B%5D%2C%22targeting%22%3A%5B%5D%7D%2C%22lastConsentTimestamp%22%3A%222025-05-29T09%3A24%3A39.070Z%22%2C%22expirationTime%22%3A1764235479070%7D",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1764235479
    },
    {
      "name": "IR_gbd",
      "value": "virginaustralia.com",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": false,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": true,
      "hostOnly": false
    },
    {
      "name": "IR_14530",
      "value": "1748510679617%7C3018448%7C1748510679617%7C%7C",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": true,
      "hostOnly": false
    },
    {
      "name": "IR_PI",
      "value": "bfa4251b-3c6e-11f0-9435-2f3374647583%7C1748510679617",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1783070679
    },
    {
      "name": "s_sq",
      "value": "virginausglobal%3D%2526c.%2526a.%2526activitymap.%2526page%253Dbook%25253Avirginaustralia%25253Aair_select_page%2526link%253DVirgin%252520Australia%2526region%253DBODY%2526pageIDType%253D1%2526.activitymap%2526.a%2526.c%2526pid%253Dbook%25253Avirginaustralia%25253Aair_select_page%2526pidt%253D1%2526oid%253DfunctionXn%252528%252529%25257B%25257D%2526oidt%253D2%2526ot%253DDIV",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": true,
      "hostOnly": false
    },
    {
      "name": "_fbp",
      "value": "fb.1.1748510680734.31260989724678580",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": false,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1756286680
    },
    {
      "name": "_tt_enable_cookie",
      "value": "1",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": false,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1782206680
    },
    {
      "name": "_ttp",
      "value": "01JWDNQ9N73HPQ4TRRAD6S7WZK_.tt.1",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": false,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1782206680
    },
    {
      "name": "ttcsid_CDU00F3C77UDMA8EUO50",
      "value": "1748510680744::rnKvnPpx3eqZq5GX-9dL.1.1748510693815",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": false,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1782206693
    },
    {
      "name": "ttcsid",
      "value": "1748510680744::2bDS4Yi9mBptTSe5VLGz.1.1748510680745",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": false,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1782206680
    },
    {
      "name": "ibeopentoken",
      "value": "T1RLAQGBQ6PKyCUf7f62U6vyZ4389CXwqxApe54BgY4KUjgGHOSGFiDNAACQxVFgxwzEQd8Tmf%2B5gKM5zwmX82WBiPoqZBFmNM%2FLqmVJodJwWngC7jXWGAGa2LX1mbX4vNoi2QZojeZAI%2F5zHs5V1c41pIqirZti%2F3zEvt7hswMfgO9vcURTof9I5ZxEq0SgUCznICy3TRI0Ul5Hz7E%2BGFcerAYuZAw280u2yyAsAyd59QxptCIBs7IlqZT6",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": **********
    },
    {
      "name": "member_auth",
      "value": "Velocity%20Member%20%7C%20Travelbank%20Account",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": **********
    },
    {
      "name": "fltk",
      "value": "segID%3D1612598%2CsegID%3D13466096",
      "domain": ".book.virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": **********
    },
    {
      "name": "aam_uuid",
      "value": "60194410272424919810412349738792204978",
      "domain": ".book.virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": **********
    },
    {
      "name": "tva_auds",
      "value": "a634",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1783070695
    },
    {
      "name": "tva_props",
      "value": "%7B%22p6874%22%3A%22SEA%22%2C%22p6876%22%3A%22HND%22%2C%22p6878%22%3A%**********%22%2C%22p6882%22%3A%221%22%2C%22p7177%22%3A0%2C%22p21262436%22%3A%22Redemption%20Search%22%2C%22p21262432%22%3A%22one-way%22%2C%22p21263501%22%3A%22one-way%22%2C%22p21265219%22%3A%22Redemption%20Search%22%7D",
      "domain": ".virginaustralia.com",
      "path": "/",
      "secure": true,
      "httpOnly": false,
      "sameSite": "no_restriction",
      "storeId": "firefox-default",
      "session": false,
      "hostOnly": false,
      "expirationDate": 1783070695
    }
  ],
  "pageData": {
    "title": "Flight selection - Virgin Australia",
    "flightInfo": [
      {
        "flightNumber": "0",
        "flightId": "va-app"
      },
      {
        "flightNumber": null,
        "flightId": "expanded-shopping-cart-flights"
      },
      {
        "flightNumber": null,
        "flightId": "expanded-shopping-cart-flights-title"
      },
      {
        "flightNumber": "75,000",
        "flightId": "dxp-flight-selection-view"
      },
      {
        "flightNumber": "75,000",
        "flightId": "dxp-shared-flight-selection"
      },
      {
        "flightNumber": "75,000",
        "flightId": ""
      },
      {
        "flightNumber": "75,000",
        "flightId": ""
      },
      {
        "flightNumber": "75,000",
        "flightId": "dxp-calendar-ribbons"
      },
      {
        "flightNumber": "75,000",
        "flightId": ""
      },
      {
        "flightNumber": "75,000",
        "flightId": ""
      },
      {
        "flightNumber": "75,000",
        "flightId": ""
      },
      {
        "flightNumber": "75,000",
        "flightId": ""
      },
      {
        "flightNumber": "75,000",
        "flightId": ""
      },
      {
        "flightNumber": "75,000",
        "flightId": ""
      },
      {
        "flightNumber": "75,000",
        "flightId": ""
      },
      {
        "flightNumber": "75,000",
        "flightId": ""
      },
      {
        "flightNumber": "75,000",
        "flightId": ""
      },
      {
        "flightNumber": null,
        "flightId": "flight-selection-points-currency-toggle-0"
      },
      {
        "flightNumber": null,
        "flightId": "flight-selection-points-currency-toggle-1"
      },
      {
        "flightNumber": "NH 117",
        "flightId": "dxp-flight-table-section"
      },
      {
        "flightNumber": "NH 117",
        "flightId": ""
      },
      {
        "flightNumber": "NH 117",
        "flightId": ""
      },
      {
        "flightNumber": "NH 117",
        "flightId": ""
      },
      {
        "flightNumber": null,
        "flightId": "fare-disclaimer-section-flights"
      }
    ],
    "graphqlEndpoint": "https://book.virginaustralia.com/api/graphql"
  },
  "urlParams": {
    "execution": "4e23984c-d7fd-4370-a194-fd7dc227fe74"
  }
}