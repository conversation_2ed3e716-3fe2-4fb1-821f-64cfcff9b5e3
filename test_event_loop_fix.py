#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试事件循环修复
"""

import asyncio
import threading
import time
from datetime import datetime

def test_event_loop_setup():
    """测试事件循环设置"""
    print("=" * 60)
    print("事件循环设置测试")
    print("=" * 60)
    
    # 模拟主程序的事件循环设置
    print("[1] 模拟主程序事件循环设置...")
    
    try:
        loop = asyncio.get_event_loop()
        print(f"   ✅ 使用现有的事件循环: {loop}")
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        print(f"   ✅ 创建新的事件循环: {loop}")
    
    # 模拟RedeemApp初始化时的事件循环获取
    print("[2] 模拟RedeemApp初始化...")
    
    class MockRedeemApp:
        def __init__(self):
            try:
                self.main_loop = asyncio.get_running_loop()
                print(f"   ✅ 使用当前运行的事件循环: {self.main_loop}")
            except RuntimeError:
                try:
                    self.main_loop = asyncio.get_event_loop()
                    print(f"   ✅ 使用现有的事件循环: {self.main_loop}")
                except RuntimeError:
                    self.main_loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(self.main_loop)
                    print(f"   ✅ 创建新的事件循环: {self.main_loop}")
            
            # 检查事件循环状态
            print(f"   事件循环是否运行: {self.main_loop.is_running()}")
            print(f"   事件循环是否关闭: {self.main_loop.is_closed()}")
    
    app = MockRedeemApp()
    
    # 测试call_soon_threadsafe
    print("[3] 测试call_soon_threadsafe...")
    
    def test_callback():
        print(f"   ✅ 回调函数被执行 (线程: {threading.current_thread().name})")
    
    def test_from_thread():
        print(f"   从工作线程调用 (线程: {threading.current_thread().name})")
        try:
            app.main_loop.call_soon_threadsafe(test_callback)
            print(f"   ✅ call_soon_threadsafe 调用成功")
        except Exception as e:
            print(f"   ❌ call_soon_threadsafe 调用失败: {e}")
    
    # 启动工作线程
    worker_thread = threading.Thread(target=test_from_thread, daemon=True)
    worker_thread.start()
    
    # 运行事件循环一小段时间
    print("[4] 运行事件循环...")
    
    async def run_test():
        print(f"   事件循环开始运行 (线程: {threading.current_thread().name})")
        await asyncio.sleep(1)  # 给回调函数时间执行
        print(f"   事件循环运行完成")
    
    try:
        loop.run_until_complete(run_test())
        print("   ✅ 事件循环运行成功")
    except Exception as e:
        print(f"   ❌ 事件循环运行失败: {e}")
    
    # 等待工作线程完成
    worker_thread.join(timeout=2)
    
    print("\n✅ 事件循环设置测试完成")
    return True

def test_callback_scheduling():
    """测试回调调度"""
    print("\n" + "=" * 60)
    print("回调调度测试")
    print("=" * 60)
    
    # 设置事件循环
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    
    class MockApp:
        def __init__(self):
            self.main_loop = asyncio.get_event_loop()
            self.callback_executed = False
        
        def on_flight_found_for_task(self, match_info, task_id):
            print(f"[模拟] 找到航班回调被调用 (任务: {task_id}, 线程: {threading.current_thread().name})")
            
            # 检查main_loop状态
            main_loop_available = hasattr(self, 'main_loop') and self.main_loop is not None
            main_loop_running = main_loop_available and self.main_loop.is_running()
            
            print(f"   main_loop可用: {main_loop_available}, 运行中: {main_loop_running}")
            
            if main_loop_available and main_loop_running:
                try:
                    self.main_loop.call_soon_threadsafe(
                        self._handle_flight_found_on_main_thread,
                        match_info,
                        task_id
                    )
                    print(f"   ✅ 已成功调度到主线程处理")
                except Exception as e:
                    print(f"   ❌ 调度到主线程失败: {e}")
                    self._handle_flight_found_on_main_thread(match_info, task_id)
            else:
                print(f"   ⚠️  main_loop不可用，直接在当前线程处理")
                self._handle_flight_found_on_main_thread(match_info, task_id)
        
        def _handle_flight_found_on_main_thread(self, match_info, task_id):
            print(f"   ✅ _handle_flight_found_on_main_thread 开始执行 (任务: {task_id}, 线程: {threading.current_thread().name})")
            self.callback_executed = True
    
    app = MockApp()
    
    # 模拟从工作线程调用回调
    def simulate_crawler_callback():
        time.sleep(0.1)  # 模拟一些工作
        match_info = {"flight_signature": {"offer_flight_numbers_found": ["NH117"]}}
        app.on_flight_found_for_task(match_info, 1)
    
    print("[1] 启动模拟爬虫线程...")
    crawler_thread = threading.Thread(target=simulate_crawler_callback, daemon=True)
    crawler_thread.start()
    
    print("[2] 运行事件循环...")
    
    async def run_callback_test():
        print(f"   事件循环开始运行")
        await asyncio.sleep(2)  # 给回调足够时间执行
        print(f"   事件循环运行完成")
    
    try:
        loop.run_until_complete(run_callback_test())
        
        # 检查回调是否被执行
        if app.callback_executed:
            print("   ✅ 回调函数成功执行")
        else:
            print("   ❌ 回调函数未被执行")
            
    except Exception as e:
        print(f"   ❌ 事件循环运行失败: {e}")
    
    crawler_thread.join(timeout=1)
    
    print("\n✅ 回调调度测试完成")
    return app.callback_executed

if __name__ == "__main__":
    print(f"开始测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行测试
    test1_ok = test_event_loop_setup()
    test2_ok = test_callback_scheduling()
    
    print(f"\n" + "=" * 60)
    print(f"测试结果:")
    print(f"  事件循环设置测试: {'✅ 通过' if test1_ok else '❌ 失败'}")
    print(f"  回调调度测试: {'✅ 通过' if test2_ok else '❌ 失败'}")
    
    if test1_ok and test2_ok:
        print(f"\n🎉 所有测试通过！事件循环问题已修复。")
        print(f"现在可以重新运行ui.py，应该能看到回调函数的调试日志。")
    else:
        print(f"\n⚠️  部分测试失败，请检查相关问题。")
