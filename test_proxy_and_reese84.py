#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试代理配置和reese84生成
"""

import sys
import os
import time
import json
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_proxy_manager():
    """测试代理管理器"""
    print("=" * 60)
    print("测试代理管理器")
    print("=" * 60)
    
    try:
        from proxy_manager import ProxyManager
        
        print("[1] 创建代理管理器...")
        pm = ProxyManager()
        
        print("[2] 获取代理统计...")
        stats = pm.get_proxy_stats()
        print(f"   总代理数: {stats['total']}")
        print(f"   健康代理: {stats['healthy']}")
        print(f"   冷却代理: {stats['cooldown']}")
        print(f"   失败代理: {stats['failed']}")
        
        if stats['total'] == 0:
            print("❌ 没有加载到任何代理！")
            return False
        
        print("[3] 获取健康代理...")
        for i in range(3):
            proxy = pm.get_healthy_proxy()
            if proxy:
                proxy_url = proxy.get('http', 'Unknown')
                print(f"   代理 {i+1}: {proxy_url[:50]}...")
            else:
                print(f"   代理 {i+1}: None")
        
        print("✅ 代理管理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 代理管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_reese84_generation():
    """测试reese84生成"""
    print("\n" + "=" * 60)
    print("测试reese84生成")
    print("=" * 60)
    
    try:
        # 检查bestV8目录
        bestV8_dir = os.path.join(current_dir, "bestV8")
        print(f"[1] 检查bestV8目录: {bestV8_dir}")
        
        if not os.path.exists(bestV8_dir):
            print("❌ bestV8目录不存在")
            return False
        
        # 检查必要文件
        demo_file = os.path.join(bestV8_dir, "demo.py")
        dll_file = os.path.join(bestV8_dir, "bestV8_win64.dll")
        
        print(f"[2] 检查demo.py: {os.path.exists(demo_file)}")
        print(f"[3] 检查bestV8_win64.dll: {os.path.exists(dll_file)}")
        
        if not os.path.exists(demo_file):
            print("❌ demo.py文件不存在")
            return False
        
        if not os.path.exists(dll_file):
            print("❌ bestV8_win64.dll文件不存在")
            return False
        
        # 尝试导入demo模块
        print("[4] 导入demo模块...")
        if bestV8_dir not in sys.path:
            sys.path.insert(0, bestV8_dir)
        
        import demo
        from demo import Reese84Resolve
        print("✅ demo模块导入成功")
        
        # 测试reese84生成
        print("[5] 测试reese84生成...")
        challenge_url = 'https://book.virginaustralia.com/side-you-ares-may-Exit-sition-Alaruern-Naugmen-G?d=book.virginaustralia.com'
        
        resolver = Reese84Resolve(src=challenge_url)
        print("✅ Reese84Resolve实例创建成功")
        
        print("[6] 开始生成reese84令牌...")
        start_time = time.time()
        result = resolver.resolve()
        end_time = time.time()
        
        print(f"   生成耗时: {end_time - start_time:.2f}秒")
        
        if result and result.get('status'):
            token = result.get("data", {}).get("token")
            ua = result.get('ua')
            if token:
                print(f"✅ reese84生成成功: {token[:10]}...{token[-5:]}")
                print(f"   User-Agent: {ua[:50]}..." if ua else "   User-Agent: None")
                return True
            else:
                print(f"❌ reese84生成失败: 响应中没有token")
                print(f"   响应数据: {result.get('data')}")
                return False
        else:
            print(f"❌ reese84生成失败")
            print(f"   状态: {result.get('status') if result else 'None'}")
            print(f"   数据: {result.get('data') if result else 'None'}")
            return False
        
    except Exception as e:
        print(f"❌ reese84生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_high_concurrency_crawler():
    """测试高并发爬虫的代理使用"""
    print("\n" + "=" * 60)
    print("测试高并发爬虫代理使用")
    print("=" * 60)
    
    try:
        from high_concurrency_crawler import HighConcurrencyCrawler
        from api_client import VAApiClient
        
        print("[1] 创建爬虫实例...")
        crawler = HighConcurrencyCrawler(max_workers=1)
        
        print("[2] 检查代理管理器...")
        if hasattr(crawler, 'proxy_manager') and crawler.proxy_manager:
            stats = crawler.proxy_manager.get_proxy_stats()
            print(f"   爬虫代理统计: {stats}")
            
            print("[3] 测试获取健康代理...")
            proxy = crawler._get_healthy_proxy()
            if proxy:
                print(f"✅ 获取到代理: {proxy.get('http', 'Unknown')[:50]}...")
            else:
                print("⚠️  没有获取到代理（可能使用直连）")
        else:
            print("❌ 爬虫没有代理管理器")
            return False
        
        print("✅ 高并发爬虫代理测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 高并发爬虫测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_search_with_proxy():
    """测试使用代理进行搜索"""
    print("\n" + "=" * 60)
    print("测试使用代理进行搜索")
    print("=" * 60)
    
    try:
        from high_concurrency_crawler import search_flights_with_demo
        from proxy_manager import get_proxy_manager
        
        print("[1] 获取代理...")
        pm = get_proxy_manager()
        proxy = pm.get_healthy_proxy()
        
        if not proxy:
            print("⚠️  没有可用代理，使用直连测试")
            proxy = {}
        else:
            print(f"   使用代理: {proxy.get('http', 'Unknown')[:50]}...")
        
        print("[2] 执行搜索测试...")
        start_time = time.time()
        result = search_flights_with_demo(
            origin="SEA",
            destination="HND", 
            date="2025-06-04",
            cabin="Business",
            proxy_dict=proxy
        )
        end_time = time.time()
        
        print(f"   搜索耗时: {end_time - start_time:.2f}秒")
        
        if result and isinstance(result, dict):
            if result.get("errors"):
                print(f"⚠️  搜索返回错误: {str(result['errors'])[:100]}...")
            elif result.get("data"):
                offers_data = result.get("data", {}).get("bookingAirSearch", {}).get("originalResponse", {})
                unbundled_offers = offers_data.get("unbundledOffers", [])
                if unbundled_offers and unbundled_offers[0]:
                    offer_count = len(unbundled_offers[0])
                    print(f"✅ 搜索成功: 找到 {offer_count} 个航班选项")
                else:
                    print("⚠️  搜索成功但无航班数据")
            else:
                print("⚠️  搜索返回空数据")
        else:
            print("❌ 搜索返回异常格式")
            return False
        
        # 记录代理使用结果
        if proxy:
            pm.record_proxy_result(proxy, True, end_time - start_time)
            print("✅ 代理使用结果已记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 代理搜索测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print(f"开始测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🔧 测试代理配置和reese84生成")
    
    # 运行测试
    test1_ok = test_proxy_manager()
    test2_ok = test_reese84_generation()
    test3_ok = test_high_concurrency_crawler()
    test4_ok = test_search_with_proxy()
    
    print(f"\n" + "=" * 60)
    print(f"测试结果:")
    print(f"  代理管理器测试: {'✅ 通过' if test1_ok else '❌ 失败'}")
    print(f"  reese84生成测试: {'✅ 通过' if test2_ok else '❌ 失败'}")
    print(f"  爬虫代理测试: {'✅ 通过' if test3_ok else '❌ 失败'}")
    print(f"  代理搜索测试: {'✅ 通过' if test4_ok else '❌ 失败'}")
    
    all_passed = test1_ok and test2_ok and test3_ok and test4_ok
    
    if all_passed:
        print(f"\n🎉 所有测试通过！")
        print(f"\n📋 问题已解决:")
        print(f"  ✅ 代理配置正确加载")
        print(f"  ✅ 高频搜索可以使用代理")
        print(f"  ✅ reese84令牌生成正常")
        print(f"  ✅ 代理和reese84一致性保证")
    else:
        print(f"\n⚠️  部分测试失败，需要进一步调试。")
        
        if not test1_ok:
            print(f"\n🔍 代理管理器问题:")
            print(f"  - 检查proxy_config.json文件是否存在")
            print(f"  - 检查代理格式是否正确")
            
        if not test2_ok:
            print(f"\n🔍 reese84生成问题:")
            print(f"  - 检查bestV8目录和文件")
            print(f"  - 检查demo.py和bestV8_win64.dll")
            
        if not test3_ok:
            print(f"\n🔍 爬虫代理问题:")
            print(f"  - 检查爬虫代理管理器初始化")
            
        if not test4_ok:
            print(f"\n🔍 代理搜索问题:")
            print(f"  - 检查网络连接和代理可用性")

if __name__ == "__main__":
    main()
