#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的自动出票功能
"""

import sys
import os
import json
import time
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_booking_flow():
    """测试完整的出票流程"""
    print("=" * 60)
    print("Virgin Australia 自动出票功能测试")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from api_client import VAApiClient
        
        print("[1] 初始化API客户端...")
        client = VAApiClient()
        
        # 测试数据
        test_offer = {
            "shoppingBasketHashCode": "-1146731462",
            "total": {
                "alternatives": [[
                    {"amount": 75000, "currency": "FFCURRENCY"},
                    {"amount": 150.50, "currency": "AUD"}
                ]]
            }
        }
        
        test_passengers = [{
            "first_name": "TEST",
            "last_name": "USER", 
            "title": "MR",
            "gender": "MALE",
            "dob": "1990-01-01",
            "email": "<EMAIL>",
            "phone": "0400000000"
        }]
        
        test_auth_data = {
            "token": "test_token",
            "execution_id": "test_exec_id",
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        print("[2] 测试book_flight方法...")
        
        # 测试book_flight方法是否正确实现
        try:
            result = client.book_flight(
                test_offer, 
                test_passengers,
                award_amount_ref=75000,
                cash_amount_ref=150.50,
                cash_currency_ref="AUD",
                auth_data=test_auth_data,
                explicit_cookies={"reese84": "test_token"},
                use_specific_proxy=None
            )
            
            success, data, error_code = result
            print(f"   book_flight 返回: success={success}, error_code={error_code}")
            
            if success:
                print("   ✅ book_flight 方法实现正确")
            else:
                print(f"   ⚠️  book_flight 返回错误: {data}")
                
        except Exception as e:
            print(f"   ❌ book_flight 方法调用失败: {e}")
            import traceback
            traceback.print_exc()
        
        print("[3] 检查必要的方法是否存在...")
        
        required_methods = [
            'add_itinerary',
            '_get_booking_cart', 
            'update_passengers',
            '_confirm_award_payment_details',
            'make_payment'
        ]
        
        for method_name in required_methods:
            if hasattr(client, method_name):
                print(f"   ✅ {method_name} 方法存在")
            else:
                print(f"   ❌ {method_name} 方法缺失")
        
        print("[4] 测试方法签名...")
        
        # 检查方法签名是否正确
        import inspect
        
        try:
            sig = inspect.signature(client.book_flight)
            params = list(sig.parameters.keys())
            expected_params = ['selected_offer', 'passengers_info_list', 'award_amount_ref', 
                             'cash_amount_ref', 'cash_currency_ref', 'auth_data', 
                             'explicit_cookies', 'use_specific_proxy']
            
            missing_params = [p for p in expected_params if p not in params]
            if missing_params:
                print(f"   ⚠️  book_flight 缺少参数: {missing_params}")
            else:
                print("   ✅ book_flight 方法签名正确")
                
        except Exception as e:
            print(f"   ❌ 检查方法签名失败: {e}")
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_integration():
    """测试UI集成"""
    print("\n" + "=" * 60)
    print("UI集成测试")
    print("=" * 60)
    
    try:
        # 检查ui.py中的关键修复
        with open('ui.py', 'r', encoding='utf-8') as f:
            ui_content = f.read()
        
        # 检查关键修复点
        checks = [
            ('main_loop可用检查', 'main_loop_available = hasattr(self, \'main_loop\')'),
            ('备用处理方案', '_handle_flight_found_on_main_thread(match_info, task_id_from_crawler)'),
            ('调试日志', 'main_loop可用:'),
            ('异常处理', 'except Exception as e:')
        ]
        
        for check_name, check_pattern in checks:
            if check_pattern in ui_content:
                print(f"   ✅ {check_name}: 已修复")
            else:
                print(f"   ⚠️  {check_name}: 可能需要检查")
        
        print("   ✅ UI集成检查完成")
        return True
        
    except Exception as e:
        print(f"❌ UI集成测试失败: {e}")
        return False

if __name__ == "__main__":
    print(f"开始测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行测试
    booking_test_ok = test_booking_flow()
    ui_test_ok = test_ui_integration()
    
    print(f"\n测试结果:")
    print(f"  出票功能测试: {'✅ 通过' if booking_test_ok else '❌ 失败'}")
    print(f"  UI集成测试: {'✅ 通过' if ui_test_ok else '❌ 失败'}")
    
    if booking_test_ok and ui_test_ok:
        print(f"\n🎉 所有测试通过！现在可以重新运行ui.py测试自动出票功能。")
    else:
        print(f"\n⚠️  部分测试失败，请检查相关问题。")
