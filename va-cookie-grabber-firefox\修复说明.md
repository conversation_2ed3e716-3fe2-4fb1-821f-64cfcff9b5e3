# Virgin Australia Cookie Grabber 扩展修复说明

## 问题分析

原始错误信息：
```
Could not establish connection. Receiving end does not exist.
```

这个错误表明背景脚本无法与内容脚本建立通信连接。主要原因包括：

1. **内容脚本生命周期问题**：页面导航时内容脚本可能被销毁
2. **消息传递时机问题**：在内容脚本完全准备好之前就尝试通信
3. **Manifest配置不完整**：缺少自动注入配置

## 修复内容

### 1. Manifest.json 改进

**修改内容：**
- 版本升级到 1.1
- 添加 `webNavigation` 权限
- 设置 `persistent: true` 确保背景脚本持续运行
- 添加 `content_scripts` 配置，实现自动注入

**新增配置：**
```json
{
  "background": {
    "scripts": ["background.js"],
    "persistent": true
  },
  "content_scripts": [
    {
      "matches": ["*://*.virginaustralia.com/*"],
      "js": ["content_script.js"],
      "run_at": "document_end",
      "all_frames": false
    }
  ]
}
```

### 2. Background.js 通信改进

**主要改进：**

1. **智能脚本检测**：
   - 检查内容脚本是否已通过manifest自动注入
   - 只在必要时手动注入脚本

2. **改进的等待机制**：
   - 使用循环检查内容脚本就绪状态
   - 最多等待15秒，每秒检查一次
   - 检查 `vaContentScriptReady` 和 `vaContentScriptLoading` 状态

3. **增强的Ping测试**：
   - 多次尝试ping通信（最多5次）
   - 每次失败后等待1秒再重试

4. **备用响应处理**：
   - 添加对内容脚本备用响应方式的支持

### 3. Content_script.js 稳定性改进

**主要改进：**

1. **防重复监听器**：
   - 使用 `window.vaMessageListenerSet` 标志防止重复设置监听器

2. **增强的错误处理**：
   - 在 `sendResponse` 失败时使用备用响应方式
   - 通过 `runtime.sendMessage` 发送响应数据

3. **更好的状态管理**：
   - 明确设置全局状态变量
   - 改进脚本就绪状态检测

## 测试方法

### 1. 重新加载扩展

1. 在Firefox中打开 `about:debugging`
2. 点击"此Firefox"
3. 找到"Virgin Australia Cookie Grabber"扩展
4. 点击"重新载入"

### 2. 使用测试脚本

运行提供的测试脚本：
```bash
python test_extension.py
```

### 3. 手动测试

1. 启动UI程序
2. 观察扩展连接状态
3. 检查是否能成功获取Cookie

## 预期改进效果

1. **消除连接错误**：解决"Could not establish connection"错误
2. **提高稳定性**：减少通信失败的概率
3. **更好的错误恢复**：即使主要通信方式失败，也有备用方案
4. **自动注入**：通过manifest自动注入，减少手动注入失败的风险

## 注意事项

1. **扩展重新加载**：修改后需要重新加载扩展
2. **浏览器重启**：如果问题持续，建议重启Firefox
3. **权限检查**：确保扩展有足够的权限访问目标网站
4. **网络连接**：确保WebSocket服务器正常运行

## 故障排除

如果问题仍然存在：

1. **检查控制台日志**：
   - 打开Firefox开发者工具
   - 查看控制台是否有错误信息

2. **检查扩展状态**：
   - 在 `about:debugging` 中检查扩展是否正常加载

3. **清除缓存**：
   - 清除浏览器缓存和Cookie
   - 重新启动浏览器

4. **检查权限**：
   - 确保扩展有访问目标网站的权限
   - 检查是否被其他安全软件阻止
