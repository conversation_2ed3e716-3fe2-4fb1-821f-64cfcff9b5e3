#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用真实cookie和行程信息进行完整预订测试
"""

import sys
import os
import asyncio
import threading
import time
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_real_booking():
    """使用真实cookie和行程信息进行完整预订测试"""
    print("=" * 60)
    print("真实预订测试")
    print("=" * 60)
    
    try:
        # 设置事件循环
        try:
            loop = asyncio.get_event_loop()
            print("[*] 使用现有的事件循环")
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            print("[*] 创建新的事件循环")
        
        # 导入UI模块
        import tkinter as tk
        from ui import RedeemApp, Task
        
        print("[1] 创建Tkinter根窗口...")
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口，只进行逻辑测试
        
        print("[2] 创建RedeemApp实例...")
        app = RedeemApp(root)
        
        print("[3] 创建真实测试任务...")
        # 使用真实的行程信息
        test_params = {
            "origin": "SEA",
            "destination": "HND", 
            "date_str": "20250604",
            "flight_no_str": "NH117",
            "cabin": "Business",
            "auto_book_on_find": True,
            "passengers_info": [{
                "first_name": "QUAN",
                "last_name": "ZHANG", 
                "dob": "1968-02-07",
                "gender": "MALE",
                "title": "MR",
                "email": "<EMAIL>",
                "phone": "0400000000"
            }],
            "payment_info": {
                "card_number": "****************",
                "expiry_month": "03", 
                "expiry_year": "2026",
                "cvv": "888"
            },
            "billing_address": {
                "line1": "Test Address",
                "city": "Test City", 
                "state": "NSW",
                "postal_code": "2000",
                "country_code": "AU"
            }
        }
        
        task = Task(1, test_params)
        app.tasks.append(task)
        
        print("[4] 添加真实的认证数据...")
        # 真实的认证数据
        real_auth_data = {
            "token": "3:YZPnxWDRFGD9f/XRSe1rWA==:7ScqL8MuBmkaCrXkeT6DbkoknPy9bLff3I6sfpw9u36oW54QMhpPoOVK5Ny5yqC5OK3SlavvlyS2uJHuwyVxhrM7nFeT1X5vyV2M2zMBpFCc9sSM54lDtX1uXj/+WQwVaLVjG1qebdC7VQQUHBn3CTnW8Zbjtag3foOvNbib1nch70mBZ6VqWwYRXsikw1yNiaqR+fxtwoF4LtvHKhTxbviAeeduj+iv93IBoCrDmrc9kP9z/i+DeCCSbGGeyr1no8Q4MYiImaID8Yk48e2KzLmLdaoAKCGc1HGMWJQ+G0pWTyGJ40wzUE+ycBdata5URxq9g0uCh2ojA0ltL8lG2QutSRpeqFWVmJoMsydJFd2lBGZZSRIl0cpUOAnmq+JnvUSv0L3Tx76l0DQZLkpxH+ak/eTJoZzEDVRgkhS5OCFAiPOdPj6/IeRLtv52C7jMHwX9z0q8hjQaaIrE9qXWk4KWeDfbYZfzqiDuEs1hvAxy9sxdKDPti93I3m4ozvqLQobYqUEBd96X2131CQeQwYnH9oCJvun8rpuLdJ7Awd0=:CxGbh9eg/3qtNTINa5bZS9fcRmh/C2dZzN8Kvic/gsY=",
            "execution_id": "4e23984c-d7fd-4370-a194-fd7dc227fe74",
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; rv:134.0) Gecko/20100101 Firefox/134.0"
        }
        
        real_cookies = {
            "DCSESSIONID": "130cc6ee-b769-43ca-9992-63c9a272e031-TargetGroupA",
            "reese84": "3:YZPnxWDRFGD9f/XRSe1rWA==:7ScqL8MuBmkaCrXkeT6DbkoknPy9bLff3I6sfpw9u36oW54QMhpPoOVK5Ny5yqC5OK3SlavvlyS2uJHuwyVxhrM7nFeT1X5vyV2M2zMBpFCc9sSM54lDtX1uXj/+WQwVaLVjG1qebdC7VQQUHBn3CTnW8Zbjtag3foOvNbib1nch70mBZ6VqWwYRXsikw1yNiaqR+fxtwoF4LtvHKhTxbviAeeduj+iv93IBoCrDmrc9kP9z/i+DeCCSbGGeyr1no8Q4MYiImaID8Yk48e2KzLmLdaoAKCGc1HGMWJQ+G0pWTyGJ40wzUE+ycBdata5URxq9g0uCh2ojA0ltL8lG2QutSRpeqFWVmJoMsydJFd2lBGZZSRIl0cpUOAnmq+JnvUSv0L3Tx76l0DQZLkpxH+ak/eTJoZzEDVRgkhS5OCFAiPOdPj6/IeRLtv52C7jMHwX9z0q8hjQaaIrE9qXWk4KWeDfbYZfzqiDuEs1hvAxy9sxdKDPti93I3m4ozvqLQobYqUEBd96X2131CQeQwYnH9oCJvun8rpuLdJ7Awd0=:CxGbh9eg/3qtNTINa5bZS9fcRmh/C2dZzN8Kvic/gsY="
        }
        
        # 将认证数据添加到任务中
        task.auth_data = real_auth_data
        task.explicit_cookies = real_cookies
        
        print("[5] 测试真实预订流程...")
        # 测试真实的book_flight方法
        try:
            from api_client import VAApiClient
            api_client = VAApiClient()
            
            # 构造真实的offer数据
            selected_offer = {
                "shoppingBasketHashCode": "-1146731462",
                "total": {
                    "alternatives": [[
                        {"amount": 75000, "currency": "FFCURRENCY"},
                        {"amount": 150.50, "currency": "AUD"}
                    ]]
                }
            }
            
            print(f"   调用 book_flight 方法...")
            result = api_client.book_flight(
                selected_offer,
                test_params["passengers_info"],
                award_amount_ref=75000,
                cash_amount_ref=150.50,
                cash_currency_ref="AUD",
                auth_data=real_auth_data,
                explicit_cookies=real_cookies,
                use_specific_proxy=None
            )
            
            success, data, error_code = result
            print(f"   book_flight 结果: success={success}, error_code={error_code}")
            if not success:
                print(f"   错误详情: {data}")
            else:
                print(f"   预订成功: {data}")
                
        except Exception as e:
            print(f"   预订测试失败: {e}")
            import traceback
            traceback.print_exc()
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print(f"开始测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行测试
    test_ok = test_real_booking()
    
    print(f"\n" + "=" * 60)
    print(f"测试结果:")
    print(f"  真实预订测试: {'✅ 通过' if test_ok else '❌ 失败'}")
    
    if test_ok:
        print(f"\n🎉 测试通过！")
    else:
        print(f"\n⚠️  测试失败，需要进一步调试。")
