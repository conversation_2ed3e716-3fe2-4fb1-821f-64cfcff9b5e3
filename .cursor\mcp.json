{"mcpServers": {"filesystem": {"command": "C:\\Windows\\system32\\cmd.exe", "args": ["/C", "npx", "-y", "@modelcontextprotocol/server-filesystem", "C:\\Users\\<USER>\\Desktop\\ana-fuzhucaozuo"], "enabled": true}, "mcp-server-firecrawl": {"command": "C:\\Windows\\system32\\cmd.exe", "args": ["/C", "npx", "-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_URL": "http://localhost:3002/"}, "enabled": true}, "Framelink Figma MCP": {"command": "cmd", "args": ["/c", "npx", "-y", "figma-developer-mcp", "--figma-api-key=*********************************************", "--st<PERSON>"]}}}