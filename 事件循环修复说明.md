# Virgin Australia 事件循环回调问题修复说明

## 问题分析

从日志可以看到，爬虫成功找到航班并触发了回调函数，但是没有后续的自动出票操作：

```
[HCC TaskMapID:1] 🎉 找到匹配航班: NH117 (Business)
[HCC TaskMapID:1] 触发回调函数...
[THREADING DEBUG] RedeemApp.on_flight_found_for_task called for task 1 from thread: ThreadPoolExecutor-0_2
```

**关键问题**：没有看到以下调试日志，说明 `_handle_flight_found_on_main_thread` 函数没有被执行：
- `任务 1: main_loop可用: xxx, 运行中: xxx`
- `任务 1: _handle_flight_found_on_main_thread 开始执行。`

**问题根源**：
1. **事件循环不匹配**：`RedeemApp` 初始化时获取的 `main_loop` 与实际运行的事件循环不是同一个
2. **初始化时序问题**：在 `__main__` 中，事件循环在 `RedeemApp` 初始化之后才开始运行
3. **回调调度失败**：`call_soon_threadsafe` 无法正确调度到主线程

## 修复内容

### 1. 改进事件循环获取逻辑

**文件**：`ui.py` - `RedeemApp.__init__`

**原始问题**：
```python
try: self.main_loop = asyncio.get_event_loop()
except RuntimeError: self.main_loop = asyncio.new_event_loop(); asyncio.set_event_loop(self.main_loop)
```

**修复后**：
```python
# 获取当前运行的事件循环，如果没有则创建新的
try: 
    self.main_loop = asyncio.get_running_loop()
    self.log_message("使用当前运行的事件循环", "debug")
except RuntimeError:
    try:
        self.main_loop = asyncio.get_event_loop()
        self.log_message("使用现有的事件循环", "debug")
    except RuntimeError:
        self.main_loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.main_loop)
        self.log_message("创建新的事件循环", "debug")
```

**改进点**：
- 优先尝试获取当前运行的事件循环
- 添加详细的调试日志
- 更健壮的错误处理

### 2. 修正主程序初始化顺序

**文件**：`ui.py` - `__main__` 部分

**原始问题**：
```python
if __name__ == "__main__":
    root_tk = tk.Tk()
    try: loop = asyncio.get_event_loop()
    except RuntimeError: loop = asyncio.new_event_loop(); asyncio.set_event_loop(loop)
    app_instance = RedeemApp(root_tk)  # 在事件循环设置之后初始化
```

**修复后**：
```python
if __name__ == "__main__":
    # 首先设置事件循环
    try: 
        loop = asyncio.get_event_loop()
        print("[*] 使用现有的事件循环")
    except RuntimeError: 
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        print("[*] 创建新的事件循环")
    
    # 然后创建Tkinter根窗口和应用实例
    root_tk = tk.Tk()
    app_instance = RedeemApp(root_tk)  # 在事件循环设置之前初始化
```

**改进点**：
- 确保事件循环在 `RedeemApp` 初始化之前就已经设置好
- 保证 `main_loop` 引用的是正确的事件循环
- 添加调试输出

## 测试验证

创建了 `test_event_loop_fix.py` 测试脚本，验证：

### 测试结果
```
============================================================
事件循环设置测试
============================================================
[1] 模拟主程序事件循环设置...
   ✅ 使用现有的事件循环: <ProactorEventLoop running=False closed=False debug=False>
[2] 模拟RedeemApp初始化...
   ✅ 使用现有的事件循环: <ProactorEventLoop running=False closed=False debug=False>
[3] 测试call_soon_threadsafe...
   ✅ call_soon_threadsafe 调用成功
   ✅ 回调函数被执行 (线程: MainThread)

============================================================
回调调度测试
============================================================
[模拟] 找到航班回调被调用 (任务: 1, 线程: Thread-2)
   main_loop可用: True, 运行中: True
   ✅ 已成功调度到主线程处理
   ✅ _handle_flight_found_on_main_thread 开始执行 (任务: 1, 线程: MainThread)
   ✅ 回调函数成功执行

🎉 所有测试通过！事件循环问题已修复。
```

**验证结果**：
- ✅ 事件循环设置正确
- ✅ `call_soon_threadsafe` 调用成功
- ✅ 回调函数能够正确调度到主线程
- ✅ 主线程处理函数正常执行

## 预期修复效果

修复后的完整流程：

1. **程序启动** → 正确设置事件循环
2. **RedeemApp初始化** → 获取正确的事件循环引用
3. **爬虫找到航班** → 触发回调函数
4. **回调调度** → 成功调度到主线程
5. **主线程处理** → 执行 `_handle_flight_found_on_main_thread`
6. **自动出票** → 启动完整的出票流程

## 预期日志输出

修复后应该能看到完整的调试日志：

```
[HCC TaskMapID:1] 🎉 找到匹配航班: NH117 (Business)
[HCC TaskMapID:1] 触发回调函数...
[THREADING DEBUG] RedeemApp.on_flight_found_for_task called for task 1 from thread: ThreadPoolExecutor-0_2
任务 1: 收到爬虫找到航班的回调，准备调度到主线程处理。
任务 1: main_loop可用: True, 运行中: True
任务 1: 已成功调度到主线程处理。
任务 1: _handle_flight_found_on_main_thread 开始执行。
匹配成功! 主线程处理回调。
航班: ['NH117'], 舱位: ['Business']
任务 1: 状态更新为 found_match，准备更新UI。
任务 1: auto_book_on_find = True
任务 1: 找到航班，准备调度自动出票...
任务 1: 开始自动出票流程。
```

## 使用方法

1. **重新启动程序**：
   ```bash
   python ui.py
   ```

2. **观察调试日志**：
   - 查看是否有事件循环设置的调试信息
   - 确认回调函数的调试日志出现
   - 监控自动出票流程的执行

3. **预期行为**：
   - 爬虫找到航班后应该立即看到回调调试日志
   - 主线程处理函数应该正常执行
   - 自动出票流程应该自动启动

## 故障排除

如果仍然遇到问题：

1. **检查事件循环日志**：
   - 确认看到 "使用现有的事件循环" 或类似信息
   - 验证事件循环设置正确

2. **验证回调调度**：
   - 确认看到 "main_loop可用: True, 运行中: True"
   - 检查是否有调度失败的错误信息

3. **运行测试脚本**：
   ```bash
   python test_event_loop_fix.py
   ```

4. **检查线程状态**：
   - 确认回调函数在正确的线程中执行
   - 验证主线程处理函数被调用

## 注意事项

1. **事件循环一致性**：确保整个应用使用同一个事件循环
2. **线程安全**：所有跨线程调用都通过 `call_soon_threadsafe` 进行
3. **调试模式**：保持调试日志开启以便监控执行流程
4. **异常处理**：即使调度失败也有备用处理方案

修复完成后，系统应该能够：
- ✅ 正确设置和使用事件循环
- ✅ 成功调度回调函数到主线程
- ✅ 执行完整的自动出票流程
- ✅ 显示详细的调试日志便于监控
