// 初始化存储
let apiRequests = [];
let isRecording = false;
let requestBodyMap = new Map(); // 用于临时存储请求体

// 关键操作名称列表 - 只记录这些操作
const KEY_OPERATIONS = [
  "bookingAirSearch",
  "getBookingCart",
  "bookingAddItinerary",
  "bookingUpdatePassengers",
  "bookingPaymentDetails",
  "bookingPurchase",
  "fetchBookingCombinableFop",
  "signIn",
  "dcmcInit"
];

// 初始化
browser.runtime.onInstalled.addListener(() => {
  console.log("Virgin Australia API Recorder 已安装");
  browser.storage.local.set({ apiRequests: [], isRecording: false });
});

// 监听来自popup的消息
browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === "startRecording") {
    isRecording = true;
    apiRequests = [];
    browser.storage.local.set({ isRecording: true, apiRequests: [] });
    sendResponse({ success: true, message: "开始记录" });
    updateBadge();
  } else if (message.action === "stopRecording") {
    isRecording = false;
    browser.storage.local.set({ isRecording: false });
    sendResponse({ success: true, message: "停止记录" });
    updateBadge();
  } else if (message.action === "getRecords") {
    browser.storage.local.get("apiRequests", (data) => {
      sendResponse({ success: true, data: data.apiRequests || [] });
    });
    return true; // 异步响应
  } else if (message.action === "clearRecords") {
    apiRequests = [];
    browser.storage.local.set({ apiRequests: [] });
    sendResponse({ success: true, message: "记录已清除" });
  } else if (message.action === "getStatus") {
    browser.storage.local.get("isRecording", (data) => {
      sendResponse({ isRecording: data.isRecording || false });
    });
    return true; // 异步响应
  }
});

// 更新扩展图标上的徽章
function updateBadge() {
  if (isRecording) {
    browser.browserAction.setBadgeText({ text: "REC" });
    browser.browserAction.setBadgeBackgroundColor({ color: "#FF0000" });
  } else {
    browser.browserAction.setBadgeText({ text: "" });
  }
}

// 加载存储的状态
browser.storage.local.get(["isRecording", "apiRequests"], (data) => {
  isRecording = data.isRecording || false;
  apiRequests = data.apiRequests || [];
  updateBadge();
});

// 监听请求，捕获请求体
browser.webRequest.onBeforeRequest.addListener(
  (details) => {
    if (!isRecording) return;
    if (details.url.includes("virginaustralia.com/api/graphql") && details.method === "POST") {
      try {
        if (details.requestBody && details.requestBody.raw) {
          const decoder = new TextDecoder("utf-8");
          const rawData = details.requestBody.raw[0].bytes;
          const requestBody = JSON.parse(decoder.decode(rawData));
          
          // 只记录关键操作
          if (requestBody.operationName && KEY_OPERATIONS.includes(requestBody.operationName)) {
            requestBodyMap.set(details.requestId, requestBody);
          }
        }
      } catch (error) {
        console.error("解析请求体时出错:", error);
      }
    }
  },
  { urls: ["*://*.virginaustralia.com/api/graphql"] },
  ["requestBody"]
);

// 监听请求头
browser.webRequest.onSendHeaders.addListener(
  (details) => {
    if (!isRecording) return;
    if (details.url.includes("virginaustralia.com/api/graphql") && details.method === "POST") {
      const requestBody = requestBodyMap.get(details.requestId);
      if (requestBody && requestBody.operationName && KEY_OPERATIONS.includes(requestBody.operationName)) {
        // 提取关键请求头
        const headers = {};
        const keyHeadersToRecord = [
          "authorization", 
          "x-sabre-storefront", 
          "application-id", 
          "execution", 
          "content-type", 
          "user-agent",
          "referer" // 添加 Referer，它有时对WAF很重要
        ];
        
        details.requestHeaders.forEach(header => {
          if (keyHeadersToRecord.includes(header.name.toLowerCase())) {
            headers[header.name.toLowerCase()] = header.value;
          }
        });
        
        // 提取并记录当前请求URL相关的所有Cookie的详细快照
        // 注意：这是一个异步操作，需要妥善处理。但 onSendHeaders 是同步的。
        // 为了简单起见，并考虑到主要目的是调试，我们先尝试同步获取。
        // 如果 browser.cookies.getAll 不能在 onSendHeaders 的同步回调中可靠工作，
        // 可能需要更复杂的逻辑，例如在 onBeforeRequest 中就开始准备，或者接受一定的时序不精确性。
        // 对于Firefox，browser.cookies.getAll 应该可以工作。
        
        let allCookiesSnapshot = [];
        // 尝试同步获取（实践中，cookies API 通常是异步的，但这里我们是为了在 onSendHeaders 中获取状态）
        // Firefox 的 webRequest API 通常允许在回调中进行一些异步操作，但结果需要通过其他方式传递。
        // 此处的直接调用和赋值可能不会按预期工作，如果 getAll 是严格异步的。
        // 更稳妥的做法是，将 requestId 标记，然后在 onCompleted 中，如果标记了，再调用 getAll。
        // 但为了"发送时"的快照，我们先这样尝试，并留意其限制。
        // 或者，我们只记录原始的 Cookie 请求头字符串，然后在popup.js中显示时，再让用户在那个时刻去获取当前浏览器的Cookie。
        
        // 方案：记录原始Cookie请求头字符串，由popup.js在需要时去获取实时快照。
        // 或者，扩展在每次请求时都获取一次快照并存储。

        const cookieHeader = details.requestHeaders.find(h => h.name.toLowerCase() === "cookie");
        const rawCookieHeaderString = cookieHeader ? cookieHeader.value : "";

        // 存储请求信息
        const requestInfo = {
          id: details.requestId,
          timestamp: new Date().toISOString(),
          url: details.url,
          method: details.method,
          operationName: requestBody.operationName,
          variables: requestBody.variables,
          query: requestBody.query,
          requestHeadersSnapshot: headers, // 重命名为更精确的名称
          rawRequestCookieHeader: rawCookieHeaderString // 记录原始Cookie头
          // 后续考虑在 onCompleted 时异步获取 browser.cookies.getAll({url: details.url})
          // 并附加到这个 requestInfo 上，或者在 popup.js 中提供一个按钮实时获取当前Cookie状态。
        };
        
        requestBodyMap.set(details.requestId, requestInfo); // 更新Map中的对象
      }
    }
  },
  { urls: ["*://*.virginaustralia.com/api/graphql"] },
  ["requestHeaders"]
);

// 监听响应
browser.webRequest.onCompleted.addListener(
  async (details) => { // 将此回调设为异步，以便可以使用 await
    if (!isRecording) return;
    if (details.url.includes("virginaustralia.com/api/graphql") && details.method === "POST") {
      const requestInfoFromMap = requestBodyMap.get(details.requestId);
      if (requestInfoFromMap && typeof requestInfoFromMap === 'object' && requestInfoFromMap.operationName) {
        const completeRequestInfo = { ...requestInfoFromMap }; 

        completeRequestInfo.statusCode = details.statusCode;
        completeRequestInfo.statusLine = details.statusLine;
        
        // 移除在 onCompleted 中获取Cookie快照的尝试，因为它导致了 browser.cookies undefined 错误
        // completeRequestInfo.cookiesSnapshotPostRequest = "SKIPPED_DUE_TO_API_UNAVAILABLE_IN_ONCOMPLETED";
        // 或者直接不设置这个字段，让前端知道它不存在

        apiRequests.push(completeRequestInfo);
        if (apiRequests.length > 200) { 
            apiRequests = apiRequests.slice(-200);
        }
        browser.storage.local.set({ apiRequests: apiRequests });
        
        requestBodyMap.delete(details.requestId);
      }
    }
  },
  { urls: ["*://*.virginaustralia.com/api/graphql"] }
);

// 监听响应体 (需要额外权限，可能无法实现)
// 由于Firefox扩展限制，无法直接获取响应体
// 可以考虑使用content script注入来捕获响应
