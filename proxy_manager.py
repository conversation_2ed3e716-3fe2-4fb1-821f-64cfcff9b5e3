#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理管理器 - 优化代理使用和健康检查
"""

import json
import time
import random
import threading
from typing import Dict, List, Optional, Tuple
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed


class ProxyManager:
    """代理管理器，提供代理健康检查、负载均衡和故障转移功能"""

    def __init__(self, proxy_config_file: str = "proxy_config.json"):
        self.proxy_config_file = proxy_config_file
        self.proxies: List[Dict] = []
        self.proxy_health: Dict[str, Dict] = {}
        self.proxy_cooldown: Dict[str, float] = {}
        self.proxy_response_times: Dict[str, List[float]] = {}

        # 配置参数
        self.failure_threshold = 3  # 连续失败阈值
        self.cooldown_duration = 120  # 冷却时间（秒）
        self.health_check_interval = 300  # 健康检查间隔（秒）
        self.max_response_time = 10.0  # 最大响应时间（秒）
        self.response_time_samples = 10  # 响应时间样本数量

        self.lock = threading.Lock()
        self.last_health_check = 0

        self.load_proxies()

    def _safe_print(self, message):
        """安全的打印方法，避免Windows控制台编码问题"""
        try:
            print(message)
        except (OSError, UnicodeEncodeError, UnicodeDecodeError) as e:
            try:
                # 尝试使用ASCII编码
                print(message.encode('ascii', 'ignore').decode('ascii'))
            except:
                try:
                    # 最后尝试简化消息
                    print(f"[LOG] {len(message)} chars message (encoding error)")
                except:
                    pass  # 完全静默失败

    def load_proxies(self) -> bool:
        """加载代理配置"""
        try:
            with open(self.proxy_config_file, 'r', encoding='utf-8') as f:
                proxy_list = json.load(f)

            self.proxies = []
            for proxy_str in proxy_list:
                if not isinstance(proxy_str, str):
                    continue

                parts = proxy_str.strip().split(':')
                if len(parts) == 2:
                    host, port = parts
                    proxy_dict = {
                        "http": f"http://{host}:{port}",
                        "https": f"http://{host}:{port}"
                    }
                elif len(parts) == 4:
                    host, port, user, passwd = parts
                    proxy_dict = {
                        "http": f"http://{user}:{passwd}@{host}:{port}",
                        "https": f"http://{user}:{passwd}@{host}:{port}"
                    }
                else:
                    continue

                self.proxies.append(proxy_dict)
                proxy_key = proxy_dict["http"]
                if proxy_key not in self.proxy_health:
                    self.proxy_health[proxy_key] = {
                        "successes": 0,
                        "failures": 0,
                        "last_success": 0,
                        "last_failure": 0,
                        "avg_response_time": 0.0
                    }
                    self.proxy_response_times[proxy_key] = []

            self._safe_print(f"[ProxyManager] 成功加载 {len(self.proxies)} 个代理")
            return True

        except Exception as e:
            self._safe_print(f"[ProxyManager] 加载代理配置失败: {e}")
            return False

    def get_healthy_proxy(self, exclude_proxies: List[Dict] = None) -> Optional[Dict]:
        """获取一个健康的代理"""
        with self.lock:
            current_time = time.time()

            # 定期进行健康检查
            if current_time - self.last_health_check > self.health_check_interval:
                self._background_health_check()
                self.last_health_check = current_time

            exclude_keys = set()
            if exclude_proxies:
                exclude_keys = {p.get("http", str(p)) for p in exclude_proxies}

            available_proxies = []

            for proxy in self.proxies:
                proxy_key = proxy["http"]

                # 跳过排除的代理
                if proxy_key in exclude_keys:
                    continue

                # 检查是否在冷却期
                if proxy_key in self.proxy_cooldown:
                    if current_time < self.proxy_cooldown[proxy_key]:
                        continue
                    else:
                        # 冷却期结束
                        del self.proxy_cooldown[proxy_key]
                        self.proxy_health[proxy_key]["failures"] = 0

                # 检查健康状态
                health = self.proxy_health.get(proxy_key, {})
                if health.get("failures", 0) < self.failure_threshold:
                    available_proxies.append((proxy, health.get("avg_response_time", 999)))

            if not available_proxies:
                self._safe_print("[ProxyManager] 警告: 无可用健康代理")
                return {}

            # 按响应时间排序，选择较快的代理
            available_proxies.sort(key=lambda x: x[1])

            # 从前50%的代理中随机选择
            top_half = available_proxies[:max(1, len(available_proxies) // 2)]
            selected_proxy = random.choice(top_half)[0]

            return selected_proxy

    def record_proxy_result(self, proxy: Dict, success: bool, response_time: float = None):
        """记录代理使用结果"""
        if not proxy:
            return

        proxy_key = proxy.get("http", str(proxy))
        current_time = time.time()

        with self.lock:
            if proxy_key not in self.proxy_health:
                self.proxy_health[proxy_key] = {
                    "successes": 0,
                    "failures": 0,
                    "last_success": 0,
                    "last_failure": 0,
                    "avg_response_time": 0.0
                }
                self.proxy_response_times[proxy_key] = []

            health = self.proxy_health[proxy_key]

            if success:
                health["successes"] += 1
                health["failures"] = 0  # 重置失败计数
                health["last_success"] = current_time

                # 记录响应时间
                if response_time is not None:
                    times = self.proxy_response_times[proxy_key]
                    times.append(response_time)
                    if len(times) > self.response_time_samples:
                        times.pop(0)
                    health["avg_response_time"] = sum(times) / len(times)
            else:
                health["failures"] += 1
                health["last_failure"] = current_time

                # 如果失败次数达到阈值，加入冷却期
                if health["failures"] >= self.failure_threshold:
                    self.proxy_cooldown[proxy_key] = current_time + self.cooldown_duration
                    self._safe_print(f"[ProxyManager] 代理 {proxy_key} 失败次数过多，进入冷却期 {self.cooldown_duration}秒")

    def _background_health_check(self):
        """后台健康检查（简化版）"""
        # 这里可以实现更复杂的健康检查逻辑
        # 比如定期ping代理服务器或发送测试请求
        pass

    def get_proxy_stats(self) -> Dict:
        """获取代理统计信息"""
        with self.lock:
            total_proxies = len(self.proxies)
            healthy_proxies = 0
            cooldown_proxies = 0
            current_time = time.time()

            for proxy in self.proxies:
                proxy_key = proxy["http"]

                if proxy_key in self.proxy_cooldown and current_time < self.proxy_cooldown[proxy_key]:
                    cooldown_proxies += 1
                elif self.proxy_health.get(proxy_key, {}).get("failures", 0) < self.failure_threshold:
                    healthy_proxies += 1

            return {
                "total": total_proxies,
                "healthy": healthy_proxies,
                "cooldown": cooldown_proxies,
                "failed": total_proxies - healthy_proxies - cooldown_proxies
            }

    def reset_proxy_health(self, proxy_key: str = None):
        """重置代理健康状态"""
        with self.lock:
            if proxy_key:
                if proxy_key in self.proxy_health:
                    self.proxy_health[proxy_key]["failures"] = 0
                if proxy_key in self.proxy_cooldown:
                    del self.proxy_cooldown[proxy_key]
            else:
                # 重置所有代理
                for key in self.proxy_health:
                    self.proxy_health[key]["failures"] = 0
                self.proxy_cooldown.clear()


# 全局代理管理器实例
_proxy_manager = None

def get_proxy_manager() -> ProxyManager:
    """获取全局代理管理器实例"""
    global _proxy_manager
    if _proxy_manager is None:
        _proxy_manager = ProxyManager()
    return _proxy_manager


if __name__ == "__main__":
    # 测试代理管理器
    pm = ProxyManager()

    pm._safe_print("代理统计:" + str(pm.get_proxy_stats()))

    # 获取几个健康代理
    for i in range(3):
        proxy = pm.get_healthy_proxy()
        pm._safe_print(f"代理 {i+1}: {proxy.get('http', 'Direct') if proxy else 'None'}")

        # 模拟使用结果
        if proxy:
            pm.record_proxy_result(proxy, random.choice([True, False]), random.uniform(0.5, 3.0))

    pm._safe_print("更新后统计:" + str(pm.get_proxy_stats()))
