// DOM元素
const startBtn = document.getElementById('startBtn');
const stopBtn = document.getElementById('stopBtn');
const clearBtn = document.getElementById('clearBtn');
const exportBtn = document.getElementById('exportBtn');
const statusEl = document.getElementById('status');
const recordsContainer = document.getElementById('recordsContainer');
const detailView = document.getElementById('detailView');
const requestDetail = document.getElementById('requestDetail');
const variablesDetail = document.getElementById('variablesDetail');
const headersDetail = document.getElementById('headersDetail');
const cookiesDetail = document.getElementById('cookiesDetail');
const queryDetail = document.getElementById('queryDetail');

// 当前记录数据
let apiRecords = [];
let selectedRecordId = null;

// 初始化
document.addEventListener('DOMContentLoaded', () => {
  // 加载记录状态
  browser.runtime.sendMessage({ action: "getStatus" })
    .then(response => {
      updateRecordingStatus(response.isRecording);
    });
  
  // 加载已记录的API请求
  loadRecords();
  
  // 设置标签页切换
  setupTabs();
});

// 开始记录按钮
startBtn.addEventListener('click', () => {
  browser.runtime.sendMessage({ action: "startRecording" })
    .then(response => {
      if (response.success) {
        updateRecordingStatus(true);
      }
    });
});

// 停止记录按钮
stopBtn.addEventListener('click', () => {
  browser.runtime.sendMessage({ action: "stopRecording" })
    .then(response => {
      if (response.success) {
        updateRecordingStatus(false);
        // 重新加载记录
        loadRecords();
      }
    });
});

// 清除记录按钮
clearBtn.addEventListener('click', () => {
  if (confirm('确定要清除所有记录吗？')) {
    browser.runtime.sendMessage({ action: "clearRecords" })
      .then(response => {
        if (response.success) {
          apiRecords = [];
          renderRecords();
          hideDetailView();
        }
      });
  }
});

// 导出记录按钮
exportBtn.addEventListener('click', () => {
  if (apiRecords.length === 0) {
    alert('没有可导出的记录');
    return;
  }
  
  // 准备导出数据
  const exportData = {
    timestamp: new Date().toISOString(),
    records: apiRecords,
    summary: generateSummary(apiRecords)
  };
  
  // 创建下载链接
  const dataStr = JSON.stringify(exportData, null, 2);
  const dataBlob = new Blob([dataStr], { type: 'application/json' });
  const url = URL.createObjectURL(dataBlob);
  
  // 创建下载链接并点击
  const a = document.createElement('a');
  a.href = url;
  a.download = `va-api-records-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
  document.body.appendChild(a);
  a.click();
  
  // 清理
  setTimeout(() => {
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, 100);
});

// 更新记录状态UI
function updateRecordingStatus(isRecording) {
  if (isRecording) {
    statusEl.textContent = '记录中';
    statusEl.className = 'status recording';
    startBtn.disabled = true;
    stopBtn.disabled = false;
  } else {
    statusEl.textContent = '空闲';
    statusEl.className = 'status idle';
    startBtn.disabled = false;
    stopBtn.disabled = true;
  }
}

// 加载记录
function loadRecords() {
  browser.runtime.sendMessage({ action: "getRecords" })
    .then(response => {
      if (response.success) {
        apiRecords = response.data;
        renderRecords();
      }
    });
}

// 渲染记录列表
function renderRecords() {
  if (apiRecords.length === 0) {
    recordsContainer.innerHTML = '<div class="empty-message">暂无记录</div>';
    return;
  }
  
  recordsContainer.innerHTML = '';
  
  // 按时间排序，最新的在前面
  apiRecords.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  
  apiRecords.forEach(record => {
    const recordEl = document.createElement('div');
    recordEl.className = 'record-item';
    recordEl.dataset.id = record.id;
    
    // 状态码样式
    const statusClass = record.statusCode >= 200 && record.statusCode < 300 ? 'success' : 'error';
    
    // 格式化时间
    const timestamp = new Date(record.timestamp).toLocaleTimeString();
    
    recordEl.innerHTML = `
      <div class="operation-name">${record.operationName}</div>
      <div class="timestamp">
        ${timestamp}
        <span class="status-code ${statusClass}">${record.statusCode}</span>
      </div>
    `;
    
    recordEl.addEventListener('click', () => {
      // 高亮选中的记录
      document.querySelectorAll('.record-item').forEach(el => {
        el.style.backgroundColor = '';
      });
      recordEl.style.backgroundColor = '#e9ecef';
      
      // 显示详情
      selectedRecordId = record.id;
      showRecordDetail(record);
    });
    
    recordsContainer.appendChild(recordEl);
  });
}

// 显示记录详情
function showRecordDetail(record) {
  // 基本请求信息
  requestDetail.textContent = JSON.stringify({
    operationName: record.operationName,
    method: record.method,
    url: record.url,
    timestamp: record.timestamp,
    statusCode: record.statusCode,
    statusLine: record.statusLine
  }, null, 2);
  
  // 变量
  variablesDetail.textContent = JSON.stringify(record.variables, null, 2);
  
  // 请求头
  headersDetail.textContent = JSON.stringify(record.headers, null, 2);
  
  // Cookie
  cookiesDetail.textContent = JSON.stringify(record.cookies, null, 2);
  
  // 查询
  queryDetail.textContent = record.query || '无查询数据';
  
  // 显示详情视图
  detailView.style.display = 'block';
}

// 隐藏详情视图
function hideDetailView() {
  detailView.style.display = 'none';
  selectedRecordId = null;
}

// 设置标签页切换
function setupTabs() {
  const tabs = document.querySelectorAll('.tab');
  const tabContents = document.querySelectorAll('.tab-content');
  
  tabs.forEach(tab => {
    tab.addEventListener('click', () => {
      // 移除所有活动标签
      tabs.forEach(t => t.classList.remove('active'));
      tabContents.forEach(c => c.classList.remove('active'));
      
      // 激活当前标签
      tab.classList.add('active');
      const tabName = tab.dataset.tab;
      document.getElementById(`${tabName}Tab`).classList.add('active');
    });
  });
}

// 生成记录摘要
function generateSummary(records) {
  // 按操作名称分组
  const operationGroups = {};
  records.forEach(record => {
    if (!operationGroups[record.operationName]) {
      operationGroups[record.operationName] = [];
    }
    operationGroups[record.operationName].push(record);
  });
  
  // 生成摘要
  const summary = {
    totalRequests: records.length,
    operationCounts: {},
    successRates: {},
    flowSequence: []
  };
  
  // 计算每种操作的请求数和成功率
  Object.keys(operationGroups).forEach(op => {
    const requests = operationGroups[op];
    const successfulRequests = requests.filter(r => r.statusCode >= 200 && r.statusCode < 300);
    
    summary.operationCounts[op] = requests.length;
    summary.successRates[op] = (successfulRequests.length / requests.length) * 100;
  });
  
  // 尝试推断流程顺序
  if (records.length > 0) {
    const sortedRecords = [...records].sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
    const uniqueOperations = new Set();
    
    sortedRecords.forEach(record => {
      if (!uniqueOperations.has(record.operationName)) {
        uniqueOperations.add(record.operationName);
        summary.flowSequence.push({
          operation: record.operationName,
          timestamp: record.timestamp
        });
      }
    });
  }
  
  return summary;
}
