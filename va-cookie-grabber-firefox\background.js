console.log("[VA BackgroundHost] 后台脚本 (v6) 开始加载...");

const PYTHON_WS_URL = "ws://127.0.0.1:8765";
let socket = null;
let currentTaskForAutoRefresh = null;
const AUTO_REFRESH_ALARM_NAME = "va_cookie_auto_refresh_alarm";
const AUTO_REFRESH_MINUTES = 14;
let isSessionMonitoring = false;
let sessionMonitoringTabId = null;
let lastSessionExtensionTime = null;
let ws;
let wsReconnectInterval = 5000; // 5 seconds
const wsPort = "8765"; // Ensure this matches your Python server
const wsHost = "127.0.0.1";
let executionContext = null; // To store execution_id, user_agent, itinerary from content script
let lastKnownTabIdForExecution = null; // Tab ID associated with the current execution context

// Helper function to send logs back to this background script's console for easier debugging
function logToBackground(message, level = "log") {
    switch (level) {
        case "error":
            console.error(`[VA BG Inter-script Log] ${message}`);
            break;
        case "warn":
            console.warn(`[VA BG Inter-script Log] ${message}`);
            break;
        default:
            console.log(`[VA BG Inter-script Log] ${message}`);
            break;
    }
}

async function saveSessionData(data) {
  try {
    if (browser && browser.storage && browser.storage.local) {
      await browser.storage.local.set(data);
    } else {
      const currentData = localStorage.getItem('va-session-data');
      const parsedData = currentData ? JSON.parse(currentData) : {};
      const newData = { ...parsedData, ...data };
      localStorage.setItem('va-session-data', JSON.stringify(newData));
    }
  } catch (error) {
    console.warn('[VA BackgroundHost] Error saving session data, using localStorage instead:', error);
    const currentData = localStorage.getItem('va-session-data');
    const parsedData = currentData ? JSON.parse(currentData) : {};
    const newData = { ...parsedData, ...data };
    localStorage.setItem('va-session-data', JSON.stringify(newData));
  }
}

async function loadSessionData() {
  try {
    let data = {};
    try {
      if (browser && browser.storage && browser.storage.local) {
        data = await browser.storage.local.get(['isSessionMonitoring', 'sessionMonitoringTabId', 'lastSessionExtensionTime']);
      } else {
        const storedData = localStorage.getItem('va-session-data');
        if (storedData) data = JSON.parse(storedData);
      }
    } catch (storageError) {
      console.warn('[VA BackgroundHost] Storage API error, falling back to localStorage:', storageError);
      const storedData = localStorage.getItem('va-session-data');
      if (storedData) data = JSON.parse(storedData);
    }
    isSessionMonitoring = data.isSessionMonitoring || false;
    sessionMonitoringTabId = data.sessionMonitoringTabId || null;
    lastSessionExtensionTime = data.lastSessionExtensionTime || null;
    console.log('[VA BackgroundHost] 已加载会话状态:', { isSessionMonitoring, sessionMonitoringTabId, lastSessionExtensionTime });
    if (sessionMonitoringTabId) {
      try { await browser.tabs.get(sessionMonitoringTabId); }
      catch (e) {
        isSessionMonitoring = false; sessionMonitoringTabId = null;
        await saveSessionData({ isSessionMonitoring: false, sessionMonitoringTabId: null });
        console.log('[VA BackgroundHost] 会话监控标签页不存在，已重置。');
      }
    }
  } catch (error) {
    console.error('[VA BackgroundHost] 加载会话状态时出错:', error);
  }
}

function sendToPython(data) {
    if (socket && socket.readyState === WebSocket.OPEN) {
        try {
            const message = JSON.stringify(data);
            socket.send(message);
            console.log("[VA BackgroundHost] Sent to Python:", message.substring(0,150));
        } catch (e) {
            console.error("[VA BackgroundHost] Error sending to Python:", e);
        }
    } else {
        console.warn("[VA BackgroundHost] WebSocket not open. Message not sent to Python:", data.type);
    }
}

async function processNavigationAndCookieGrab(task) {
    logToBackground("processNavigationAndCookieGrab (v6) 被调用", "log");
    const { url, credentials, source } = task;
    let tabToUse = null;
    try {
        if (!url) {
            console.error("[VA BackgroundHost] Task is missing URL.");
            sendToPython({ type: "status_update", error: "任务缺少URL." });
            return;
        }
        sendToPython({ type: "status_update", message: `正在导航到: ${url.substring(0, 70)}... (源: ${source || '未知'})` });
        let tabs = await browser.tabs.query({ url: "*://*.virginaustralia.com/*", currentWindow: true, active: true });
        if (tabs && tabs.length > 0) {
            tabToUse = tabs[0];
            await browser.tabs.update(tabToUse.id, { url: url, active: true });
        } else {
            tabs = await browser.tabs.query({ url: "*://*.virginaustralia.com/*", currentWindow: true });
            if (tabs && tabs.length > 0) {
                tabToUse = tabs[0];
                await browser.tabs.update(tabToUse.id, { url: url, active: true });
            } else {
                tabToUse = await browser.tabs.create({ url: url, active: true });
            }
        }
        sendToPython({ type: "status_update", message: `已导航/激活标签页 ${tabToUse.id}, 等待内容脚本注入 (1s)...` });
        await new Promise(resolve => setTimeout(resolve, 1000));
        console.log("[VA BackgroundHost] Attempting to inject content_script.js (v4) into tab:", tabToUse.id);

        // 检查标签页状态
        const tabInfo = await browser.tabs.get(tabToUse.id);
        if (!tabInfo || !tabInfo.url || tabInfo.url.startsWith('about:') || tabInfo.url.startsWith('moz-extension:')) {
            throw new Error(`Invalid tab or URL: ${tabInfo ? tabInfo.url : 'Tab not found'}`);
        }

        // 等待页面完全加载
        if (tabInfo.status !== 'complete') {
            console.log("[VA BackgroundHost] Waiting for tab to complete loading...");
            await new Promise((resolve) => {
                const updateListener = (updatedTabId, changeInfo) => {
                    if (updatedTabId === tabToUse.id && changeInfo.status === 'complete') {
                        browser.tabs.onUpdated.removeListener(updateListener);
                        resolve();
                    }
                };
                browser.tabs.onUpdated.addListener(updateListener);

                // 10秒超时
                setTimeout(() => {
                    browser.tabs.onUpdated.removeListener(updateListener);
                    resolve();
                }, 10000);
            });
        }

        // 注入内容脚本，增加错误处理
        try {
            await browser.tabs.executeScript(tabToUse.id, {
                file: "content_script.js",
                runAt: "document_end"
            });

            if (browser.runtime.lastError) {
                throw new Error(`注入内容脚本失败: ${browser.runtime.lastError.message}`);
            }

            console.log("[VA BackgroundHost] Content script injected successfully");
        } catch (injectError) {
            console.error("[VA BackgroundHost] Script injection failed:", injectError);
            throw new Error(`脚本注入失败: ${injectError.message}`);
        }

        // 等待内容脚本准备就绪，增加重试机制
        console.log("[VA BackgroundHost] Waiting for content script to be ready...");
        await new Promise(resolve => setTimeout(resolve, 2000)); // 增加等待时间

        // 首先测试ping通信
        console.log("[VA BackgroundHost] Testing ping communication...");
        let pingSuccess = false;
        try {
            const pingResponse = await browser.tabs.sendMessage(tabToUse.id, {
                action: "ping_content_script"
            });
            if (pingResponse && pingResponse.status === "pong") {
                console.log("[VA BackgroundHost] Ping test successful");
                pingSuccess = true;
            }
        } catch (pingError) {
            console.warn("[VA BackgroundHost] Ping test failed:", pingError.message);
        }

        if (!pingSuccess) {
            // 如果ping失败，尝试检查内容脚本是否存在
            try {
                // 检查内容脚本的多个状态
                const readyCheck = await browser.tabs.executeScript(tabToUse.id, {
                    code: 'window.vaContentScriptReady'
                });
                const versionCheck = await browser.tabs.executeScript(tabToUse.id, {
                    code: 'window.vaContentScriptVersion'
                });
                const loadingCheck = await browser.tabs.executeScript(tabToUse.id, {
                    code: 'window.vaContentScriptLoading'
                });

                console.log("[VA BackgroundHost] Script status check:", {
                    ready: readyCheck,
                    version: versionCheck,
                    loading: loadingCheck
                });

                // 如果脚本正在加载，等待一下
                if (loadingCheck && loadingCheck[0] === true) {
                    console.log("[VA BackgroundHost] Script is loading, waiting 2 seconds...");
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    // 重新检查
                    const finalReadyCheck = await browser.tabs.executeScript(tabToUse.id, {
                        code: 'window.vaContentScriptReady'
                    });

                    if (!finalReadyCheck || finalReadyCheck[0] !== true) {
                        throw new Error("内容脚本加载超时或失败");
                    }
                } else if (!readyCheck || readyCheck[0] !== true) {
                    throw new Error("内容脚本未正确加载或初始化");
                }

                console.log("[VA BackgroundHost] Script check passed, proceeding with communication");
            } catch (checkError) {
                console.error("[VA BackgroundHost] Script check failed:", checkError.message);
                throw new Error(`内容脚本状态检查失败: ${checkError.message}`);
            }
        }

        // 发送消息给内容脚本，增加重试机制
        let responseFromContentScript = null;
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries && !responseFromContentScript) {
            try {
                console.log(`[VA BackgroundHost] Sending message to content script (attempt ${retryCount + 1}/${maxRetries})`);
                responseFromContentScript = await browser.tabs.sendMessage(tabToUse.id, {
                    action: "perform_login_and_extract",
                    credentials: credentials,
                    targetUrl: url
                });

                if (responseFromContentScript) {
                    console.log("[VA BackgroundHost] Received response from content script");
                    break;
                }
            } catch (messageError) {
                console.warn(`[VA BackgroundHost] Message attempt ${retryCount + 1} failed:`, messageError.message);
                retryCount++;

                if (retryCount < maxRetries) {
                    console.log("[VA BackgroundHost] Retrying in 2 seconds...");
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    // 在重试前再次检查内容脚本状态
                    try {
                        const retryCheck = await browser.tabs.executeScript(tabToUse.id, {
                            code: 'window.vaContentScriptReady'
                        });
                        console.log(`[VA BackgroundHost] Retry ${retryCount} script check:`, retryCheck);
                    } catch (e) {
                        console.warn(`[VA BackgroundHost] Retry script check failed:`, e.message);
                    }
                } else {
                    throw new Error(`内容脚本通信失败 (${maxRetries} 次重试后): ${messageError.message}`);
                }
            }
        }
        console.log("[VA BackgroundHost] Response from content_script:", JSON.stringify(responseFromContentScript).substring(0, 300));
        if (responseFromContentScript && responseFromContentScript.success) {
            let pageData = responseFromContentScript.data;
            logToBackground("内容脚本成功返回。后台脚本现在将确认/获取 execution_id...", "log");
            if (!pageData.executionId) {
                logToBackground("内容脚本未能提供 execution_id。后台脚本尝试从当前标签页URL获取...", "warn");
                const freshTabInfo = await browser.tabs.get(tabToUse.id);
                const currentTabUrl = new URL(freshTabInfo.url);
                const hashParams = new URLSearchParams(currentTabUrl.hash.split('?')[1]);
                let bgExecutionId = null;
                if (hashParams.has('execution')) bgExecutionId = hashParams.get('execution');
                else if (currentTabUrl.searchParams.has('execution')) bgExecutionId = currentTabUrl.searchParams.get('execution');
                if (bgExecutionId) {
                    logToBackground(`后台脚本从 Tab URL 获取到 execution_id: ${bgExecutionId.substring(0,10)}...`, "log");
                    pageData.executionId = bgExecutionId;
                } else {
                    logToBackground("后台脚本也未能从当前 Tab URL 获取 execution_id。", "error");
                }
            }
            if (!pageData.executionId) {
                sendToPython({ type: "status_update", error: "关键错误: 后台脚本最终未能获取到 execution_id。" });
                logToBackground("警告: execution_id 最终缺失，但仍将尝试获取Cookie。", "warn");
            } else {
                logToBackground(`最终确认的 execution_id (background.js): ${pageData.executionId}`, "log");
            }
            console.log("[VA BackgroundHost] 准备获取Cookie (后台)...", tabToUse.id);
            await new Promise(resolve => setTimeout(resolve, 1500));
            const refreshedTab = await browser.tabs.get(tabToUse.id);
            console.log(`[VA BackgroundHost] 尝试从 StoreID: ${refreshedTab.cookieStoreId} 为 URL: ${refreshedTab.url.substring(0,70)}... 获取Cookie`);
            const cookiesFromBrowser = await browser.cookies.getAll({ url: refreshedTab.url, storeId: refreshedTab.cookieStoreId });
            if (browser.runtime.lastError) {
                throw new Error(`后台获取Cookie失败: ${browser.runtime.lastError.message}`);
            }
            const processedCookies = cookiesFromBrowser ? cookiesFromBrowser.map(c => ({ name: c.name, value: c.value, domain: c.domain, path: c.path, secure: c.secure, httpOnly: c.httpOnly, sameSite: c.sameSiteStatus || c.sameSite || 'unspecified', expirationDate: c.expirationDate || null, storeId: c.storeId, session: c.session, hostOnly: c.hostOnly })) : [];

            sendToPython({
                type: "cookie_data", payload: processedCookies, execution_id: pageData.executionId,
                user_agent: pageData.user_agent, itinerary: pageData.itinerary,
                message: "成功提取所有数据。",
                source: source
            });
            console.log(`[VA BackgroundHost] Cookie数据已发送到Python (源: ${source})。准备关闭标签页 ${tabToUse.id}...`);

            try {
                await browser.tabs.remove(tabToUse.id);
                console.log(`[VA BackgroundHost] 标签页 ${tabToUse.id} 已关闭。`);
                sendToPython({ type: "status_update", message: `标签页 ${tabToUse.id} 已自动关闭。`});
            } catch (closeError) {
                console.error(`[VA BackgroundHost] 关闭标签页 ${tabToUse.id} 失败:`, closeError.message);
                sendToPython({ type: "status_update", error: `关闭标签页 ${tabToUse.id} 失败: ${closeError.message}`});
            }

        } else {
            const errorMsg = responseFromContentScript?.error || "内容脚本执行失败或未返回数据。";
            logToBackground(`内容脚本返回失败: ${errorMsg}`, "error");
            sendToPython({ type: "status_update", error: `内容脚本错误: ${errorMsg}` });
        }
    } catch (e) {
        console.error(`[VA BackgroundHost] processNavigationAndCookieGrab 发生严重错误: ${e.message}`, e);
        sendToPython({ type: "status_update", error: `ProcessNavigationAndCookieGrab 内部错误: ${e.message}` });
    }
}

function setupAutoRefreshAlarm(taskDetails) {
    if (!taskDetails || !taskDetails.url) {
        console.warn("[VA BackgroundHost] 无法设置自动刷新：任务详情或URL缺失。");
        return;
    }
    currentTaskForAutoRefresh = taskDetails;
    browser.alarms.create(AUTO_REFRESH_ALARM_NAME, { delayInMinutes: AUTO_REFRESH_MINUTES, periodInMinutes: AUTO_REFRESH_MINUTES });
    sendToPython({ type: "status_update", message: `Cookie自动刷新已设置 (每${AUTO_REFRESH_MINUTES}分钟)。` });
}

function clearAutoRefreshAlarm() {
    browser.alarms.clear(AUTO_REFRESH_ALARM_NAME, (wasCleared) => {
        if (wasCleared) sendToPython({ type: "status_update", message: "Cookie自动刷新已停止。" });
    });
    currentTaskForAutoRefresh = null;
}

// Event Listeners
browser.runtime.onInstalled.addListener(() => { console.log('[VA BackgroundHost] Virgin Australia Cookie Grabber 已安装'); });

browser.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url && tab.url.includes('virginaustralia.com')) {
    try {
      // 检查标签页是否有效
      if (!tab.url || tab.url.startsWith('about:') || tab.url.startsWith('moz-extension:')) {
        return;
      }

      // 检查是否已经注入过会话监控脚本
      const existingMonitoring = await browser.tabs.executeScript(tabId, {
        code: 'window.vaSessionMonitorInterval !== undefined'
      }).catch(() => [false]);

      if (existingMonitoring && existingMonitoring[0] === true) {
        console.log('[VA BackgroundHost] 会话保持脚本已存在于标签页:', tabId);
        return;
      }

      // 检查session_keep_alive.js文件是否存在
      // 如果文件不存在，创建一个简单的内联脚本
      try {
        await browser.tabs.executeScript(tabId, { file: "session_keep_alive.js" });
        console.log('[VA BackgroundHost] 已自动注入会话保持脚本到标签页:', tabId);
      } catch (fileError) {
        console.warn('[VA BackgroundHost] session_keep_alive.js文件不存在，使用内联脚本');

        // 使用内联脚本作为备选方案
        await browser.tabs.executeScript(tabId, {
          code: `
            if (!window.vaSessionMonitorInterval) {
              console.log('[VA Session] 启动会话保持监控');
              window.vaSessionMonitorInterval = setInterval(() => {
                // 简单的会话保持逻辑
                if (document.hidden) return;

                // 检查是否有会话保持按钮
                const keepSessionBtn = document.querySelector('[data-testid="keep-session-button"], .keep-session, .session-extend');
                if (keepSessionBtn && keepSessionBtn.style.display !== 'none') {
                  keepSessionBtn.click();
                  console.log('[VA Session] 自动点击会话保持按钮');
                }
              }, 30000); // 每30秒检查一次
            }
          `
        });
        console.log('[VA BackgroundHost] 已注入内联会话保持脚本到标签页:', tabId);
      }

      isSessionMonitoring = true;
      sessionMonitoringTabId = tabId;
      await saveSessionData({ isSessionMonitoring: true, sessionMonitoringTabId: tabId });

    } catch (error) {
      console.error('[VA BackgroundHost] 自动注入会话保持脚本失败:', error.message);
      // 不抛出错误，避免影响其他功能
    }
  }
});

browser.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === "log_from_content") {
        logToBackground(request.data, request.level || "log");
        return;
    }
    if (request.action === 'getCookies') {
        browser.cookies.getAll({ url: request.url })
            .then(cookies => sendResponse({ cookies: cookies }))
            .catch(error => sendResponse({ error: error.message }));
        return true;
    }
    if (request.action === 'sessionExtended') {
        lastSessionExtensionTime = request.time;
        saveSessionData({ lastSessionExtensionTime: lastSessionExtensionTime });
        browser.runtime.sendMessage({ action: 'sessionExtendedNotification', time: lastSessionExtensionTime }).catch(() => {});
        return true;
    }
    if (request.action === 'startSessionMonitoring' || request.action === 'stopSessionMonitoring') {
        isSessionMonitoring = request.action === 'startSessionMonitoring';
        sessionMonitoringTabId = isSessionMonitoring ? request.tabId : null;
        saveSessionData({ isSessionMonitoring: isSessionMonitoring, sessionMonitoringTabId: sessionMonitoringTabId });
        sendResponse({ success: true });
        return true;
    }
    if (request.action === "connect_websocket_from_popup") {
        if (!socket || socket.readyState === WebSocket.CLOSED) connectWebSocket();
        sendResponse({status: socket ? socket.readyState : "连接中..."});
        return true;
    }
    // Keep this listener specific for Python commands or clearly defined actions
    // The onmessage handler for the socket itself will handle Python commands primarily.
});

browser.alarms.onAlarm.addListener(async (alarm) => {
    if (alarm.name === AUTO_REFRESH_ALARM_NAME) {
        if (currentTaskForAutoRefresh && currentTaskForAutoRefresh.url) {
            sendToPython({ type: "status_update", message: "自动刷新: 正在获取新Cookie..." });
            await processNavigationAndCookieGrab({...currentTaskForAutoRefresh, source: 'auto_refresh'});
        } else {
            clearAutoRefreshAlarm();
        }
    }
});

function connectWebSocket() {
    if (socket && (socket.readyState === WebSocket.OPEN || socket.readyState === WebSocket.CONNECTING)) {
        console.log("[VA BackgroundHost] WebSocket is already open or connecting.");
        return;
    }
    console.log("[VA BackgroundHost] 快速连接到 Python WebSocket:", PYTHON_WS_URL);
    try {
        socket = new WebSocket(PYTHON_WS_URL);

        // 设置连接超时
        const connectionTimeout = setTimeout(() => {
            if (socket && socket.readyState === WebSocket.CONNECTING) {
                console.warn("[VA BackgroundHost] WebSocket连接超时，关闭连接");
                socket.close();
                socket = null;
                setTimeout(connectWebSocket, 2000); // 2秒后重试
            }
        }, 5000); // 5秒超时

        socket.onopen = function(event) {
            clearTimeout(connectionTimeout);
            console.log("[VA BackgroundHost] WebSocket 快速连接已建立。");
            sendToPython({ type: "status_update", message: "扩展 (v6) 已快速连接到Python WebSocket." });
        };
        socket.onmessage = async function(event) {
            console.log("[VA BackgroundHost] Message from Python server:", event.data.substring(0,100));
            try {
                const command = JSON.parse(event.data);
                if (command.action === "navigate_and_get_cookies") {
                    console.log("[VA BackgroundHost] Received task from Python:", command.action, command.url ? command.url.substring(0,50) : '');
                    const taskWithSource = {...command, source: command.source || 'initial_request' };
                    sendToPython({ type: "status_update", message: `收到任务: ${taskWithSource.action} (源: ${taskWithSource.source})` });
                    currentTaskForAutoRefresh = taskWithSource;
                    await processNavigationAndCookieGrab(taskWithSource);
                    setupAutoRefreshAlarm(command);
                } else {
                    console.warn("[VA BackgroundHost] Unknown action received:", command);
                }
            } catch (e) {
                console.error("[VA BackgroundHost] Error processing message from Python:", e);
                sendToPython({ type: "status_update", error: `处理Python指令时出错: ${e.message}` });
            }
        };
        socket.onclose = function(event) {
            clearTimeout(connectionTimeout);
            console.warn(`[VA BackgroundHost] WebSocket 连接已关闭。Code: ${event.code}, Reason: ${event.reason}. 尝试在2秒后快速重连。`);
            socket = null;
            clearAutoRefreshAlarm();
            // 减少重连延迟，提高响应速度
            setTimeout(connectWebSocket, 2000);
        };
        socket.onerror = function(error) {
            clearTimeout(connectionTimeout);
            console.error("[VA BackgroundHost] WebSocket 错误:", error.message || "未知WebSocket错误");
            // 错误时立即尝试重连
            if (socket) {
                socket.close();
                socket = null;
            }
            setTimeout(connectWebSocket, 1000); // 1秒后重试
        };
    } catch (e) {
        console.error("[VA BackgroundHost] 创建 WebSocket 时发生错误:", e.message, e);
    }
}

// Initial Load and Execution
loadSessionData(); // Load any persisted session state

console.log("[VA BackgroundHost] 后台脚本 (v6) 准备初始化 WebSocket 连接...");
try {
    connectWebSocket();
    console.log("[VA BackgroundHost] connectWebSocket() 已调用。");
} catch (e) {
    console.error("[VA BackgroundHost] 调用 connectWebSocket() 时发生顶层错误:", e.message, e);
}

console.log("[VA BackgroundHost] 后台脚本 (v6) 加载完毕。");

function getDomainFromUrl(url) {
    try {
        const urlObj = new URL(url);
        // Return the hostname, which is suitable for cookie domain matching in many cases.
        // For more specific matching like ".example.com", further processing might be needed
        // if browser.cookies.getAll({ domain: ... }) requires that exact format.
        // However, for getAll, just hostname usually works to get all cookies for that host and its subdomains.
        return urlObj.hostname;
    } catch (e) {
        console.error("BG: Invalid URL for getDomainFromUrl:", url, e);
        return null;
    }
}

async function clearDomainCookies(domains) {
    if (!browser.cookies) {
        console.error("BG: browser.cookies API is not available.");
        return;
    }
    console.log("BG: Attempting to clear cookies for domains:", domains);
    for (const domain of domains) {
        try {
            const cookies = await browser.cookies.getAll({ domain: domain });
            if (cookies.length === 0) {
                console.log(`BG: No cookies found for domain: ${domain}`);
                continue;
            }
            let clearedCount = 0;
            for (const cookie of cookies) {
                // Construct the URL from the cookie information
                const protocol = cookie.secure ? "https://" : "http://";
                // Cookie domain might start with a '.', remove it for URL construction if needed,
                // but for browser.cookies.remove, the name and url are primary.
                const cookieDomain = cookie.domain.startsWith(".") ? cookie.domain.substring(1) : cookie.domain;
                const url = protocol + cookieDomain + cookie.path;

                try {
                    await browser.cookies.remove({
                        url: url,
                        name: cookie.name,
                        storeId: cookie.storeId
                    });
                    clearedCount++;
                    // console.log(`BG: Removed cookie: ${cookie.name} from domain: ${cookie.domain}`);
                } catch (e) {
                    console.error(`BG: Failed to remove cookie: ${cookie.name} from ${url}`, e);
                }
            }
            console.log(`BG: Cleared ${clearedCount} cookies for domain: ${domain}. Total found: ${cookies.length}`);
        } catch (e) {
            console.error(`BG: Error getting cookies for domain ${domain}:`, e);
        }
    }
    // Send a status update back to Python app
    if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({ type: "status_update", message: "Browser cookies cleared for relevant domains." }));
    }
}


function connect() {
// ... existing code ...
            ws.onmessage = async (event) => { // Added async here
                console.log("BG: Received message from Python app:", event.data.substring(0, 200));
                const message = JSON.parse(event.data);

                if (message.action === "navigate_and_get_cookies" && message.url) {
                    console.log("BG: Action 'navigate_and_get_cookies' received. URL:", message.url);

                    // Store credentials if provided, to be picked up by content script
                    if (message.credentials) {
                        await browser.storage.local.set({ tempCredentials: message.credentials });
                        console.log("BG: Temporary credentials stored for tab processing.");
                    } else {
                        await browser.storage.local.remove("tempCredentials");
                    }
                     // Store the URL to be accessed
                    await browser.storage.local.set({ targetUrlForTab: message.url });

                    try {
                        const newTab = await browser.tabs.create({ url: message.url });
                        console.log("BG: Opened new tab with ID:", newTab.id, "for URL:", message.url);
                        lastKnownTabIdForExecution = newTab.id; // Keep track of this tab

                        // Content script will be injected based on manifest.json matching the URL.
                        // We expect content_script.js to eventually send "send_data_to_background"
                    } catch (error) {
                        console.error("BG: Error creating tab or injecting script:", error);
                        if (ws && ws.readyState === WebSocket.OPEN) {
                            ws.send(JSON.stringify({ type: "status_update", status: "error", message: "Failed to open tab: " + error.message }));
                        }
                    }
                } else {
                    console.log("BG: Received unhandled WebSocket message action or missing URL.");
                }
            };
// ... existing code ...
// Listen for messages from content script or popup
browser.runtime.onMessage.addListener(async (message, sender, sendResponse) => {
    console.log("BG: Message received in background script:", message);

    if (message.action === "send_data_to_background") {
        console.log("BG: 'send_data_to_background' received from content script.", message.data);
        // Store the received data (execution_id, user_agent, etc.)
        executionContext = message.data;
        lastKnownTabIdForExecution = sender.tab ? sender.tab.id : null; // Capture tab ID from sender

        // Now that we have context, tell the content script it's okay to proceed
        // or if we directly want to get cookies if context is complete
        if (executionContext && executionContext.execution_id && executionContext.user_agent && executionContext.itinerary) {
            console.log("BG: Execution context is complete. Ready to fetch cookies if requested by UI or automatically.");
            // Potentially trigger cookie fetching if this was part of an automated flow
            // For now, this just stores the context. The UI button "自动获取并应用Cookie" will trigger the cookie fetch.
             if (ws && ws.readyState === WebSocket.OPEN && executionContext) {
                const domainsToClear = ["virginaustralia.com", "velocityfrequentflyer.com", "book.virginaustralia.com"];
                let allCookies = [];
                for (const domain of domainsToClear) {
                    try {
                        const domainCookies = await browser.cookies.getAll({ domain: getDomainFromUrl(domain) || domain }); // Fallback to raw domain
                        if (domainCookies) {
                            allCookies = allCookies.concat(domainCookies.map(c => ({...c, source_domain: domain }))); // Add source_domain for clarity
                        }
                    } catch (e) {
                        console.error(`BG: Error getting cookies for ${domain}:`, e);
                    }
                }

                if (allCookies.length > 0) {
                    const cookieMessage = {
                        type: "cookie_data",
                        payload: allCookies,
                        execution_id: executionContext.execution_id,
                        user_agent: executionContext.user_agent,
                        itinerary: executionContext.itinerary,
                        source: executionContext.source || "unknown_cs_source"
                    };
                    ws.send(JSON.stringify(cookieMessage));
                    console.log("BG: Sent cookie data over WebSocket after receiving from content script.");
                    sendResponse({ status: "success", message: "BG: Cookies sent to WebSocket from content_script data." });

                    if (lastKnownTabIdForExecution) {
                        try {
                            await browser.tabs.remove(lastKnownTabIdForExecution);
                            console.log(`BG: Tab ${lastKnownTabIdForExecution} closed after sending cookies (triggered by content_script).`);
                        } catch (err) {
                            console.error(`BG: Error closing tab ${lastKnownTabIdForExecution}:`, err);
                        }
                    }
                    // Clear cookies after sending and closing tab
                    await clearDomainCookies(domainsToClear);

                } else {
                    console.warn("BG: No cookies found for specified domains from content_script context.");
                    sendResponse({ status: "error", message: "BG: No cookies found to send." });
                }
            } else {
                 console.warn("BG: WebSocket not open or execution context not set, cannot send cookies from content_script trigger.");
                 sendResponse({ status: "error", message: "BG: WebSocket not open or context missing." });
            }
        } else {
            console.warn("BG: Execution context from content script is incomplete:", executionContext);
            sendResponse({ status: "error", message: "BG: Incomplete data from content script." });
        }
        return true; // Indicate an asynchronous response.
    }
    // This is triggered by the POPUP button "Manually Get & Send Cookies"
    else if (message.action === "get_cookies_from_active_tab_and_send") {
        if (!executionContext || !executionContext.execution_id || !executionContext.user_agent) {
            console.warn("BG: Execution context (execution_id, user_agent) not set from content script. Cannot get cookies.");
            if (sender.tab && sender.tab.id) {
                browser.tabs.sendMessage(sender.tab.id, { action: "request_execution_context" })
                    .catch(e => console.error("BG: Error requesting context from tab", e));
            }
            // Notify UI or popup that context is missing
            sendResponse({ status: "error", message: "BG: Missing page context (execution_id/user_agent). Try reloading the VA page or ensure content script runs." });
            if (ws && ws.readyState === WebSocket.OPEN) {
                 ws.send(JSON.stringify({ type: "status_update", status: "error", message: "扩展错误: 页面上下文 (execution_id/user_agent) 缺失。请刷新VA页面或确保内容脚本已运行。" }));
            }
            return true;
        }

        console.log("BG: 'get_cookies_from_active_tab_and_send' received, using stored execution context:", executionContext);
        const domainsToClear = ["virginaustralia.com", "velocityfrequentflyer.com", "book.virginaustralia.com"];
        let allCookies = [];
        // Domain list for fetching cookies. Should align with what the airline uses.
        // Example: ".virginaustralia.com" to catch "book.virginaustralia.com" etc.
        // Using getDomainFromUrl on the target URL might be more dynamic if it's always accurate.
        // For simplicity, hardcoding primary domains known to be involved.
        const cookieDomainsToFetch = ["virginaustralia.com", "velocityfrequentflyer.com"];


        for (const domain of cookieDomainsToFetch) {
            try {
                // Passing a more general domain to getAll often works for subdomains too.
                const domainCookies = await browser.cookies.getAll({ domain: domain });
                if (domainCookies) {
                     allCookies = allCookies.concat(domainCookies.map(c => ({...c, source_domain: domain })));
                }
            } catch (e) {
                console.error("BG: Error getting cookies for domain:", domain, e);
            }
        }

        if (allCookies.length > 0) {
            console.log(`BG: Found ${allCookies.length} cookies from specified domains.`);
            if (ws && ws.readyState === WebSocket.OPEN) {
                const cookieMessage = {
                    type: "cookie_data",
                    payload: allCookies,
                    execution_id: executionContext.execution_id, // From stored context
                    user_agent: executionContext.user_agent,     // From stored context
                    itinerary: executionContext.itinerary,       // From stored context
                    source: "manual_popup_click"                 // Source of this cookie retrieval
                };
                ws.send(JSON.stringify(cookieMessage));
                console.log("BG: Sent cookie data over WebSocket (triggered by popup).");
                sendResponse({ status: "success", message: "BG: Cookies sent to WebSocket." });

                // Close the tab from which the popup was opened, if it was the target VA tab
                if (message.tabIdToClose) {
                     console.log(`BG: Request to close tab ${message.tabIdToClose} received from popup.`);
                     try {
                        await browser.tabs.remove(message.tabIdToClose);
                        console.log(`BG: Tab ${message.tabIdToClose} closed after sending cookies (triggered by popup).`);
                    } catch (err) {
                        console.error(`BG: Error closing tab ${message.tabIdToClose}:`, err);
                    }
                }
                 // Clear cookies after sending and closing tab
                await clearDomainCookies(domainsToClear);

            } else {
                console.error("BG: WebSocket is not open. Cannot send cookies.");
                sendResponse({ status: "error", message: "BG: WebSocket not open." });
            }
        } else {
            console.warn("BG: No cookies found for the specified domains via popup click.");
            sendResponse({ status: "error", message: "BG: No cookies found for specified domains." });
             if (ws && ws.readyState === WebSocket.OPEN) {
                 ws.send(JSON.stringify({ type: "status_update", status: "warning", message: "扩展警告: 未找到相关域的Cookie。" }));
            }
        }
        return true; // Indicate asynchronous response
    } else if (message.action === "notify_tab_closed_by_content_script") {
        console.log("BG: Content script reported tab closure for tabId:", message.tabId);
        if (lastKnownTabIdForExecution === message.tabId) {
            lastKnownTabIdForExecution = null; // Clear the stored tab ID
            executionContext = null; // Clear context if the relevant tab was closed
            console.log("BG: Cleared lastKnownTabIdForExecution and executionContext as associated tab was closed by content script.");
        }
    }
    // other message handlers
});

// Initialize WebSocket connection
connect();

// Optional: Listen for tab updates to re-inject content script if necessary,
// or for other tracking purposes.
browser.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url) {
        // If a tab completes loading and matches a URL pattern where credentials might be needed
        // (e.g., the login page), we can try to re-apply credentials if they were meant for this navigation.
        // This is more complex and needs careful state management.

        // Example: If a tab navigates to the login page and we had stored creds.
        const storedCreds = await browser.storage.local.get("tempCredentials");
        const targetUrl = await browser.storage.local.get("targetUrlForTab");

        if (tab.url.includes("Login") && storedCreds.tempCredentials && targetUrl.targetUrlForTab === tab.url) {
            console.log(`BG: Tab ${tabId} updated to ${tab.url}, attempting to re-apply login credentials if content script is active.`);
            // The content script should ideally handle this on its own load if it finds credentials.
            // Sending a message might be redundant if content script already checks storage on load.
            try {
                // Ensure content script is there (it should be if manifest matches)
                // Then tell it to try logging in.
                // await browser.tabs.sendMessage(tabId, { action: "attempt_login_from_storage" });
            } catch (e) {
                // console.warn("BG: Could not send attempt_login_from_storage to content script, maybe not ready or not matching.", e);
            }
        }
         // Clean up stored URL after it's been used or tab is complete.
        if (targetUrl.targetUrlForTab === tab.url) {
           // await browser.storage.local.remove("targetUrlForTab"); // Or manage this more carefully
        }
    }
});

// Clean up storage on startup, in case of leftover data from a crash
browser.runtime.onStartup.addListener(async () => {
    console.log("BG: Extension startup, clearing temporary storage.");
    await browser.storage.local.remove("tempCredentials");
    await browser.storage.local.remove("targetUrlForTab");
    await browser.storage.local.remove("currentVAExecutionDetails"); // If this was used
})}
