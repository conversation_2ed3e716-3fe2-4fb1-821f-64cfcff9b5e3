document.addEventListener('DOMContentLoaded', function() {
  const grabButton = document.getElementById('grabCookies');
  const keepSessionBtn = document.getElementById('keepSessionBtn');
  const refreshPageBtn = document.getElementById('refreshPageBtn');
  const statusMessage = document.getElementById('statusMessage');
  const sessionInfo = document.getElementById('sessionInfo');
  const sessionStatus = document.getElementById('sessionStatus');
  const lastExtension = document.getElementById('lastExtension');

  // 存储会话状态
  let isSessionMonitoring = false;
  let sessionMonitoringTabId = null;
  let lastSessionExtensionTime = null;

  // 初始化会话信息
  initSessionInfo();

  // 数据预览元素
  const dataPreview = document.getElementById('dataPreview');
  const dataPreviewContent = document.getElementById('dataPreviewContent');

  // 抓取所有数据按钮
  grabButton.addEventListener('click', async function() {
    try {
      // 获取当前活动标签页
      const tabs = await browser.tabs.query({ active: true, currentWindow: true });
      const tab = tabs[0];

      // 检查是否在 Virgin Australia 网站上
      if (!tab.url.includes('virginaustralia.com')) {
        showStatus('请在 Virgin Australia 网站上使用此扩展', 'error');
        return;
      }

      // 显示加载状态
      showStatus('正在抓取数据...', 'info');

      // 获取 URL 参数
      const urlParams = {};
      const url = new URL(tab.url);

      // 特别检查 execution 参数
      const executionMatch = tab.url.match(/execution=([^&]*)/);
      if (executionMatch) {
        urlParams.execution = executionMatch[1];
      }

      // 注入脚本获取页面上的其他参数
      const injectionResults = await browser.tabs.executeScript(tab.id, {
        code: `(${extractPageData.toString()})()`
      });

      const pageData = injectionResults[0] || {};

      // 获取所有 Cookie，包括 HttpOnly
      const cookiesFromBrowser = await browser.cookies.getAll({ url: tab.url });

      // 构建结果对象
      const result = {
        url: tab.url,
        executionId: pageData.executionId || urlParams.execution || '',
        flightId: pageData.flightId || pageData.selectFlights || '',
        itinerary: pageData.itinerary || {},
        cookies: cookiesFromBrowser.map(c => ({
            name: c.name,
            value: c.value,
            domain: c.domain,
            path: c.path,
            secure: c.secure,
            httpOnly: c.httpOnly,
            sameSite: c.sameSiteStatus || c.sameSite,
            storeId: c.storeId,
            session: c.session,
            hostOnly: c.hostOnly,
            expirationDate: c.expirationDate
        })),
        headers: pageData.headers || {},
        pageData: pageData,
        urlParams: urlParams
      };

      // 从完整的cookiesFromBrowser数组中提取DCSESSIONID到顶层
      const dcsessionCookieFromArray = cookiesFromBrowser.find(c => c.name === "DCSESSIONID");
      if (dcsessionCookieFromArray) {
        result.DCSESSIONID = dcsessionCookieFromArray.value;
      } else {
        result.DCSESSIONID = '';
      }

      // 确保关键请求头存在
      if (!result.headers['x-sabre-storefront']) {
        result.headers['x-sabre-storefront'] = 'VADX';
      }

      if (!result.headers['authorization']) {
        result.headers['authorization'] = 'Bearer Basic anNvbl91c2VyOmpzb25fcGFzc3dvcmQ=';
      }

      if (!result.headers['application-id']) {
        result.headers['application-id'] = 'SWS1:SBR-DigConShpBk:fd34efe9a9';
      }

      // 格式化数据，将最重要的信息放在前面
      const formattedResult = {
        // 最重要的信息
        url: result.url,
        executionId: result.executionId,
        flightId: result.flightId,
        DCSESSIONID: result.DCSESSIONID,
        itinerary: result.itinerary,

        // 关键请求头
        headers: {
          'x-sabre-storefront': result.headers['x-sabre-storefront'] || 'VADX',
          'authorization': result.headers['authorization'] || 'Bearer Basic anNvbl91c2VyOmpzb25fcGFzc3dvcmQ=',
          'application-id': result.headers['application-id'] || 'SWS1:SBR-DigConShpBk:fd34efe9a9',
          'content-type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; rv:134.0) Gecko/20100101 Firefox/134.0'
        },

        // 其他信息
        cookies: result.cookies,
        pageData: {
          title: pageData.title,
          flightInfo: pageData.flightInfo,
          graphqlEndpoint: pageData.graphqlEndpoint,
          deviceFingerprintId: pageData.deviceFingerprintId
        },
        urlParams: result.urlParams
      };

      // 复制到剪贴板
      const textArea = document.createElement('textarea');
      textArea.value = JSON.stringify(formattedResult, null, 2);
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);

      // 显示数据预览
      dataPreviewContent.textContent = JSON.stringify({
        executionId: formattedResult.executionId,
        flightId: formattedResult.flightId,
        DCSESSIONID: formattedResult.DCSESSIONID ? formattedResult.DCSESSIONID.substring(0, 10) + '...' : '',
        itinerary: formattedResult.itinerary,
        headers: {
          'x-sabre-storefront': formattedResult.headers['x-sabre-storefront'],
          'authorization': '(已包含)',
          'application-id': formattedResult.headers['application-id']
        }
      }, null, 2);
      dataPreview.classList.remove('hidden');

      showStatus('成功抓取所有数据并复制到剪贴板！', 'success');
    } catch (error) {
      console.error('Error:', error);
      showStatus('发生错误: ' + error.message, 'error');
    }
  });

  // 保持会话按钮
  keepSessionBtn.addEventListener('click', async function() {
    try {
      const tabs = await browser.tabs.query({ active: true, currentWindow: true });
      const tab = tabs[0];

      // 检查是否在 Virgin Australia 网站上
      if (!tab.url.includes('virginaustralia.com')) {
        showStatus('请在 Virgin Australia 网站上使用此扩展', 'error');
        return;
      }

      if (isSessionMonitoring && sessionMonitoringTabId === tab.id) {
        // 停止会话监控
        await stopSessionMonitoring(tab.id);
        showStatus('已停止会话监控', 'info');
      } else {
        // 开始会话监控
        await startSessionMonitoring(tab.id);
        showStatus('已开始会话监控，将自动延长会话', 'success');
      }

      updateSessionInfo();
    } catch (error) {
      console.error('Error:', error);
      showStatus('会话监控操作失败: ' + error.message, 'error');
    }
  });

  // 刷新页面按钮
  refreshPageBtn.addEventListener('click', async function() {
    try {
      const tabs = await browser.tabs.query({ active: true, currentWindow: true });
      const tab = tabs[0];

      // 刷新当前页面
      await browser.tabs.reload(tab.id);
      showStatus('页面刷新成功', 'info');
    } catch (error) {
      console.error('Error:', error);
      showStatus('页面刷新失败: ' + error.message, 'error');
    }
  });

  // 显示状态消息
  function showStatus(message, type) {
    statusMessage.textContent = message;
    statusMessage.className = 'status ' + type;
    statusMessage.classList.remove('hidden');

    // 5秒后隐藏消息
    setTimeout(() => {
      statusMessage.classList.add('hidden');
    }, 5000);
  }

  // 初始化会话信息
  async function initSessionInfo() {
    try {
      // 使用 localStorage 作为备选存储方式
      let data = {};

      try {
        // 尝试使用 browser.storage API
        if (browser && browser.storage && browser.storage.local) {
          data = await browser.storage.local.get(['isSessionMonitoring', 'sessionMonitoringTabId', 'lastSessionExtensionTime']);
        } else {
          // 如果 browser.storage 不可用，使用 localStorage
          const storedData = localStorage.getItem('va-session-data');
          if (storedData) {
            data = JSON.parse(storedData);
          }
        }
      } catch (storageError) {
        console.warn('Storage API error, falling back to localStorage:', storageError);
        // 使用 localStorage 作为备选
        const storedData = localStorage.getItem('va-session-data');
        if (storedData) {
          data = JSON.parse(storedData);
        }
      }

      isSessionMonitoring = data.isSessionMonitoring || false;
      sessionMonitoringTabId = data.sessionMonitoringTabId || null;
      lastSessionExtensionTime = data.lastSessionExtensionTime || null;

      // 检查标签页是否仍然存在
      if (sessionMonitoringTabId) {
        try {
          await browser.tabs.get(sessionMonitoringTabId);
        } catch (e) {
          // 标签页不存在，重置会话监控状态
          isSessionMonitoring = false;
          sessionMonitoringTabId = null;

          // 保存更新后的状态
          saveSessionData({
            isSessionMonitoring: false,
            sessionMonitoringTabId: null
          });
        }
      }

      updateSessionInfo();

      // 自动启动会话监控（默认开启）
      if (!isSessionMonitoring) {
        const tabs = await browser.tabs.query({ active: true, currentWindow: true });
        if (tabs.length > 0 && tabs[0].url.includes('virginaustralia.com')) {
          await startSessionMonitoring(tabs[0].id);
          showStatus('已自动开启会话保持和页面刷新', 'success');
          updateSessionInfo();
        }
      }
    } catch (error) {
      console.error('Error initializing session info:', error);
    }
  }

  // 保存会话数据的通用函数
  async function saveSessionData(data) {
    try {
      // 尝试使用 browser.storage API
      if (browser && browser.storage && browser.storage.local) {
        await browser.storage.local.set(data);
      } else {
        // 如果 browser.storage 不可用，使用 localStorage
        const currentData = localStorage.getItem('va-session-data');
        const parsedData = currentData ? JSON.parse(currentData) : {};
        const newData = { ...parsedData, ...data };
        localStorage.setItem('va-session-data', JSON.stringify(newData));
      }
    } catch (error) {
      console.warn('Error saving session data to storage API, using localStorage instead:', error);
      // 使用 localStorage 作为备选
      const currentData = localStorage.getItem('va-session-data');
      const parsedData = currentData ? JSON.parse(currentData) : {};
      const newData = { ...parsedData, ...data };
      localStorage.setItem('va-session-data', JSON.stringify(newData));
    }
  }

  // 更新会话信息显示
  function updateSessionInfo() {
    sessionInfo.classList.remove('hidden');

    if (isSessionMonitoring) {
      sessionStatus.textContent = '监控中';
      keepSessionBtn.textContent = '停止自动会话保持和页面刷新';

      if (lastSessionExtensionTime) {
        const date = new Date(lastSessionExtensionTime);
        lastExtension.textContent = date.toLocaleString();
      } else {
        lastExtension.textContent = '尚未延长';
      }
    } else {
      sessionStatus.textContent = '未监控';
      keepSessionBtn.textContent = '开始自动会话保持和页面刷新';
      lastExtension.textContent = '无';
    }
  }

  // 开始会话监控
  async function startSessionMonitoring(tabId) {
    // 注入会话监控脚本
    await browser.tabs.executeScript(tabId, {
      code: `
        // 如果已经存在会话监控，先移除
        if (window.vaSessionMonitorInterval) {
          clearInterval(window.vaSessionMonitorInterval);
        }

        if (window.vaAutoRefreshInterval) {
          clearInterval(window.vaAutoRefreshInterval);
        }

        // 创建状态指示器
        let statusIndicator = document.getElementById('va-session-status');
        if (!statusIndicator) {
          statusIndicator = document.createElement('div');
          statusIndicator.id = 'va-session-status';
          statusIndicator.style.position = 'fixed';
          statusIndicator.style.bottom = '10px';
          statusIndicator.style.right = '10px';
          statusIndicator.style.zIndex = '9999';
          statusIndicator.style.padding = '5px 10px';
          statusIndicator.style.backgroundColor = '#4CAF50'; // 绿色
          statusIndicator.style.color = 'white';
          statusIndicator.style.border = 'none';
          statusIndicator.style.borderRadius = '3px';
          statusIndicator.style.fontSize = '12px';
          statusIndicator.style.fontWeight = 'bold';
          statusIndicator.style.boxShadow = '0 2px 5px rgba(0,0,0,0.3)';
          statusIndicator.textContent = '会话监控已启动';
          document.body.appendChild(statusIndicator);
        } else {
          statusIndicator.style.backgroundColor = '#4CAF50'; // 绿色
          statusIndicator.textContent = '会话监控已启动';
        }

        // 检测并处理会话延长弹窗
        function handleSessionExtension() {
          // 检测"Yes, extend"按钮
          const extendButtons = Array.from(document.querySelectorAll('button, a')).filter(el =>
            el.textContent && el.textContent.trim().toLowerCase().includes('yes, extend')
          );

          if (extendButtons.length > 0) {
            console.log('检测到会话延长弹窗，自动点击"Yes, extend"按钮');

            // 更新状态指示器
            if (statusIndicator) {
              statusIndicator.style.backgroundColor = '#FF9800'; // 橙色
              statusIndicator.textContent = '正在延长会话...';

              // 2秒后恢复绿色
              setTimeout(() => {
                statusIndicator.style.backgroundColor = '#4CAF50'; // 绿色
                statusIndicator.textContent = '会话已延长';

                // 再过5秒恢复正常状态
                setTimeout(() => {
                  statusIndicator.textContent = '会话监控已启动';
                }, 5000);
              }, 2000);
            }

            // 点击延长按钮
            extendButtons[0].click();

            // 记录最后一次延长会话的时间
            window.lastSessionExtensionTime = Date.now();

            // 通知扩展会话已延长
            try {
              browser.runtime.sendMessage({
                action: 'sessionExtended',
                time: window.lastSessionExtensionTime
              });
            } catch (e) {
              console.error('无法发送会话延长消息:', e);
            }

            // 延长会话后自动刷新页面
            setTimeout(() => {
              console.log('会话延长后自动刷新页面');
              location.reload();
            }, 3000); // 延迟3秒后刷新，给服务器时间处理会话延长
          }
        }

        // 自动刷新页面函数
        function autoRefreshPage() {
          console.log('执行自动刷新页面');

          // 更新状态指示器
          if (statusIndicator) {
            statusIndicator.style.backgroundColor = '#2196F3'; // 蓝色
            statusIndicator.textContent = '正在刷新页面...';

            // 2秒后恢复绿色
            setTimeout(() => {
              statusIndicator.style.backgroundColor = '#4CAF50'; // 绿色
              statusIndicator.textContent = '会话监控已启动';
            }, 2000);
          }

          // 刷新页面
          location.reload();
        }

        // 启动定期检查会话状态
        window.vaSessionMonitorInterval = setInterval(handleSessionExtension, 30000); // 每30秒检查一次

        // 启动自动刷新页面
        window.vaAutoRefreshInterval = setInterval(autoRefreshPage, 15 * 60 * 1000); // 每15分钟自动刷新一次

        // 首次检查
        setTimeout(handleSessionExtension, 5000); // 页面加载5秒后进行首次检查

        console.log('Virgin Australia 会话监控和自动刷新已启动');

        // 返回成功状态
        true;
      `
    });

    // 更新会话监控状态
    isSessionMonitoring = true;
    sessionMonitoringTabId = tabId;

    // 保存到存储
    await saveSessionData({
      isSessionMonitoring: true,
      sessionMonitoringTabId: tabId
    });

    // 监听来自内容脚本的消息
    browser.runtime.onMessage.addListener(handleMessage);
  }

  // 停止会话监控
  async function stopSessionMonitoring(tabId) {
    // 注入脚本停止会话监控
    await browser.tabs.executeScript(tabId, {
      code: `
        // 停止会话监控
        if (window.vaSessionMonitorInterval) {
          clearInterval(window.vaSessionMonitorInterval);
          window.vaSessionMonitorInterval = null;
          console.log('Virgin Australia 会话监控已停止');
        }

        // 停止自动刷新
        if (window.vaAutoRefreshInterval) {
          clearInterval(window.vaAutoRefreshInterval);
          window.vaAutoRefreshInterval = null;
          console.log('Virgin Australia 自动刷新已停止');
        }

        // 移除状态指示器
        const statusIndicator = document.getElementById('va-session-status');
        if (statusIndicator) {
          statusIndicator.remove();
        }

        // 返回成功状态
        true;
      `
    });

    // 更新会话监控状态
    isSessionMonitoring = false;
    sessionMonitoringTabId = null;

    // 保存到存储
    await saveSessionData({
      isSessionMonitoring: false,
      sessionMonitoringTabId: null
    });

    // 移除消息监听器
    browser.runtime.onMessage.removeListener(handleMessage);
  }

  // 处理来自内容脚本的消息
  function handleMessage(message) {
    if (message.action === 'sessionExtended') {
      lastSessionExtensionTime = message.time;

      // 保存到存储
      saveSessionData({
        lastSessionExtensionTime: lastSessionExtensionTime
      });

      // 更新会话信息显示
      updateSessionInfo();
    }
  }
});

// 在页面上下文中执行的函数，提取页面数据
function extractPageData() {
  const pageData = {
    headers: {} // 初始化headers对象
  };

  // 尝试从页面中提取 execution ID
  try {
    // 从 meta 标签中查找
    const metaTags = document.querySelectorAll('meta');
    metaTags.forEach(tag => {
      if (tag.name === 'execution-id' || tag.getAttribute('name') === 'execution-id') {
        pageData.executionId = tag.content || tag.getAttribute('content');
      }
    });

    // 从全局变量中查找
    if (window.executionId) {
      pageData.executionId = window.executionId;
    }

    // 从 script 标签中查找
    const scripts = document.querySelectorAll('script');
    scripts.forEach(script => {
      const content = script.textContent;
      if (content) {
        const match = content.match(/execution['"]\s*:\s*['"]([\w-]+)['"]/);
        if (match) {
          pageData.executionId = match[1];
        }

        // 尝试提取请求头信息
        const xSabreMatch = content.match(/['"](x-sabre-storefront)['"]:\s*['"]([\w-]+)['"]/i);
        if (xSabreMatch) {
          pageData.headers['x-sabre-storefront'] = xSabreMatch[2];
        }

        const authMatch = content.match(/['"](authorization)['"]:\s*['"](Bearer[^'"]+)['"]/i);
        if (authMatch) {
          pageData.headers['authorization'] = authMatch[2];
        }

        const appIdMatch = content.match(/['"](application-id)['"]:\s*['"]([\w:.-]+)['"]/i);
        if (appIdMatch) {
          pageData.headers['application-id'] = appIdMatch[2];
        }
      }
    });

    // 从 input 标签中查找
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
      if (input.name === 'execution') {
        pageData.executionId = input.value;
      }
    });

    // 从 API 请求中查找
    if (window.performance && window.performance.getEntries) {
      const entries = window.performance.getEntries();
      entries.forEach(entry => {
        if (entry.name && entry.name.includes('execution=')) {
          const match = entry.name.match(/execution=([^&]*)/);
          if (match) {
            pageData.executionId = match[1];
          }
        }
      });
    }

    // 尝试从网络请求头中提取 application-id 和 x-sabre-storefront
    if (window.performance && window.performance.getEntriesByType) {
      const resources = window.performance.getEntriesByType('resource');
      resources.forEach(resource => {
        if (resource.name && resource.name.includes('graphql')) {
          pageData.graphqlEndpoint = resource.name;

          // 如果浏览器支持，尝试获取请求头信息
          if (resource.requestHeaders) {
            for (const [key, value] of Object.entries(resource.requestHeaders)) {
              if (['x-sabre-storefront', 'authorization', 'application-id'].includes(key.toLowerCase())) {
                pageData.headers[key.toLowerCase()] = value;
              }
            }
          }
        }
      });
    }

    // 尝试从 DOM 中提取其他重要信息
    pageData.title = document.title;
    pageData.url = window.location.href;

    // 提取航班ID - 这是我们需要的关键信息
    // 方法1: 从URL中提取
    const flightIdMatch = window.location.href.match(/flight=(\d+)/);
    if (flightIdMatch) {
      pageData.flightId = flightIdMatch[1];
    }

    // 方法2: 从DOM中提取
    // 查找所有可能包含航班ID的元素
    const flightIdElements = document.querySelectorAll('[data-flight-id], [data-id], [id*="flight"], [class*="flight"], [data-test-id*="flight"]');
    flightIdElements.forEach(el => {
      const id = el.getAttribute('data-flight-id') || el.getAttribute('data-id');
      if (id && /^\d+$/.test(id)) {
        pageData.flightId = id;
      }

      // 检查元素的data属性
      for (const key in el.dataset) {
        if (key.includes('flight') && el.dataset[key] && /^\d+$/.test(el.dataset[key])) {
          pageData.flightId = el.dataset[key];
        }
      }
    });

    // 方法3: 从网络请求中提取
    if (window.performance && window.performance.getEntries) {
      const entries = window.performance.getEntries();
      entries.forEach(entry => {
        if (entry.name) {
          // 查找selectFlights参数
          const selectFlightsMatch = entry.name.match(/selectFlights=(\d+)/);
          if (selectFlightsMatch) {
            pageData.selectFlights = selectFlightsMatch[1];
          }

          // 查找flightId参数
          const flightIdMatch = entry.name.match(/flightId=(\d+)/);
          if (flightIdMatch && !pageData.flightId) {
            pageData.flightId = flightIdMatch[1];
          }

          // 查找searchFlights参数
          const searchFlightsMatch = entry.name.match(/searchFlights=([^&]+)/);
          if (searchFlightsMatch) {
            pageData.searchFlights = searchFlightsMatch[1];
          }
        }
      });
    }

    // 方法4: 从页面上的JSON数据中提取
    try {
      // 查找所有script标签
      const scripts = document.querySelectorAll('script:not([src])');
      scripts.forEach(script => {
        const content = script.textContent;
        if (content && content.includes('flightId') && content.includes('selectFlight')) {
          // 尝试提取JSON数据
          const jsonMatch = content.match(/\{[\s\S]*"flightId"\s*:\s*"?(\d+)"?[\s\S]*\}/);
          if (jsonMatch && jsonMatch[1]) {
            pageData.flightId = jsonMatch[1];
          }
        }
      });

      // 查找页面上的所有JSON数据
      const jsonElements = document.querySelectorAll('[type="application/json"], [data-json]');
      jsonElements.forEach(el => {
        try {
          const jsonContent = el.textContent || el.getAttribute('data-json');
          if (jsonContent) {
            const jsonData = JSON.parse(jsonContent);
            if (jsonData.flightId) {
              pageData.flightId = jsonData.flightId;
            } else if (jsonData.flights && jsonData.flights.length > 0) {
              pageData.flightId = jsonData.flights[0].id || jsonData.flights[0].flightId;
            }
          }
        } catch (e) {
          // 忽略JSON解析错误
        }
      });
    } catch (e) {
      console.error('Error extracting flight ID from JSON:', e);
    }

    // 方法5: 从页面上的所有可点击元素中提取
    const clickableElements = document.querySelectorAll('a, button, [role="button"], [onclick]');
    clickableElements.forEach(el => {
      // 检查元素的onclick属性
      const onclickAttr = el.getAttribute('onclick');
      if (onclickAttr && onclickAttr.includes('flight')) {
        const flightIdMatch = onclickAttr.match(/flight[^\d]*(\d+)/i);
        if (flightIdMatch) {
          pageData.flightId = flightIdMatch[1];
        }
      }

      // 检查元素的href属性
      const hrefAttr = el.getAttribute('href');
      if (hrefAttr && hrefAttr.includes('flight')) {
        const flightIdMatch = hrefAttr.match(/flight[^\d]*(\d+)/i);
        if (flightIdMatch) {
          pageData.flightId = flightIdMatch[1];
        }
      }
    });

    // 提取所有可见的航班信息
    const flightInfo = [];

    // 方法1: 使用data属性
    const flightElements = document.querySelectorAll('[data-flight-number]');
    flightElements.forEach(el => {
      const info = {
        flightNumber: el.getAttribute('data-flight-number'),
        departureTime: el.getAttribute('data-departure-time'),
        arrivalTime: el.getAttribute('data-arrival-time')
      };

      // 尝试提取航班ID
      const flightId = el.getAttribute('data-flight-id') || el.getAttribute('data-id');
      if (flightId) {
        info.flightId = flightId;
        if (!pageData.flightId) {
          pageData.flightId = flightId;
        }
      }

      flightInfo.push(info);
    });

    // 方法2: 查找包含航班信息的元素
    const flightCards = document.querySelectorAll('.flight-card, .flight-item, [class*="flight"], [id*="flight"]');
    flightCards.forEach(card => {
      // 尝试提取航班号
      const flightNumberEl = card.querySelector('[class*="flight-number"], [class*="flightNumber"], [class*="number"]');
      const flightNumber = flightNumberEl ? flightNumberEl.textContent.trim() : null;

      // 尝试提取航班ID
      const flightId = card.getAttribute('data-flight-id') || card.getAttribute('data-id') || card.id;

      if (flightNumber || flightId) {
        const info = { flightNumber, flightId };

        // 如果找到航班ID，保存到pageData
        if (flightId && /^\d+$/.test(flightId) && !pageData.flightId) {
          pageData.flightId = flightId;
        }

        flightInfo.push(info);
      }
    });

    if (flightInfo.length > 0) {
      pageData.flightInfo = flightInfo;
    }

    // 提取行程信息
    pageData.itinerary = {};

    // 提取出发地和目的地 - 优先从URL中提取
    const originMatch = window.location.href.match(/origin=([A-Z]{3})/);
    const destinationMatch = window.location.href.match(/destination=([A-Z]{3})/);

    if (originMatch) {
      pageData.itinerary.origin = originMatch[1];
    }

    if (destinationMatch) {
      pageData.itinerary.destination = destinationMatch[1];
    }

    // 如果URL中没有，尝试从DOM中提取
    if (!pageData.itinerary.origin || !pageData.itinerary.destination) {
      // 尝试从各种可能的元素中提取
      const originElements = document.querySelectorAll('[data-origin], [class*="origin"], [id*="origin"], [aria-label*="origin"], [aria-label*="departure"], [title*="origin"], [title*="departure"]');
      const destinationElements = document.querySelectorAll('[data-destination], [class*="destination"], [id*="destination"], [aria-label*="destination"], [aria-label*="arrival"], [title*="destination"], [title*="arrival"]');

      // 遍历所有可能的元素
      for (const el of originElements) {
        const text = el.getAttribute('data-origin') || el.textContent.trim();
        // 检查是否是三字母机场代码
        if (text && /^[A-Z]{3}$/.test(text)) {
          pageData.itinerary.origin = text;
          break;
        }
      }

      for (const el of destinationElements) {
        const text = el.getAttribute('data-destination') || el.textContent.trim();
        // 检查是否是三字母机场代码
        if (text && /^[A-Z]{3}$/.test(text)) {
          pageData.itinerary.destination = text;
          break;
        }
      }
    }

    // 提取日期 - 优先从URL中提取
    const dateMatch = window.location.href.match(/date=(\d{2}-\d{2}-\d{4})/);
    if (dateMatch) {
      // 转换为YYYY-MM-DD格式
      const dateParts = dateMatch[1].split('-');
      if (dateParts.length === 3) {
        pageData.itinerary.date = `${dateParts[2]}-${dateParts[0]}-${dateParts[1]}`;
      } else {
        pageData.itinerary.date = dateMatch[1];
      }
    }

    // 如果URL中没有日期，尝试从activeMonth参数中提取
    if (!pageData.itinerary.date) {
      const activeMonthMatch = window.location.href.match(/activeMonth=(\d{2}-\d{2}-\d{4})/);
      if (activeMonthMatch) {
        const dateParts = activeMonthMatch[1].split('-');
        if (dateParts.length === 3) {
          pageData.itinerary.date = `${dateParts[2]}-${dateParts[0]}-${dateParts[1]}`;
        }
      }
    }

    // 如果还是没有日期，尝试从DOM中提取
    if (!pageData.itinerary.date) {
      const dateElements = document.querySelectorAll('[data-date], [class*="date"], [id*="date"], [aria-label*="date"], [title*="date"]');
      for (const el of dateElements) {
        const text = el.getAttribute('data-date') || el.textContent.trim();
        // 尝试识别日期格式
        if (text && /\d{1,4}[-\/\.]\d{1,2}[-\/\.]\d{1,4}/.test(text)) {
          pageData.itinerary.date = text;
          break;
        }
      }
    }

    // 提取舱位等级 - 优先从URL中提取
    const classMatch = window.location.href.match(/class=([^&]+)/);
    if (classMatch) {
      pageData.itinerary.cabinClass = classMatch[1];
    }

    // 如果URL中没有舱位等级，尝试从DOM中提取
    if (!pageData.itinerary.cabinClass) {
      const cabinElements = document.querySelectorAll('[data-cabin-class], [class*="cabin"], [id*="cabin"], [aria-label*="cabin"], [title*="cabin"], [class*="class"], [id*="class"]');
      for (const el of cabinElements) {
        const text = el.getAttribute('data-cabin-class') || el.textContent.trim();
        if (text && ['Economy', 'Business', 'First', 'Premium'].some(cabin => text.includes(cabin))) {
          pageData.itinerary.cabinClass = text;
          break;
        }
      }
    }

    // 检查是否是奖励机票
    const awardBookingMatch = window.location.href.match(/awardBooking=(true|false)/);
    if (awardBookingMatch) {
      pageData.itinerary.awardBooking = awardBookingMatch[1] === 'true';
    }

    // 尝试获取设备指纹ID
    try {
      // 从localStorage获取
      const possibleKeys = [
        'deviceFingerprint',
        'deviceFingerprintId',
        'dfp',
        'dfpId',
        'fingerprint',
        'fpid',
        'deviceId'
      ];

      for (const key of possibleKeys) {
        const value = localStorage.getItem(key);
        if (value && value.length > 8) {
          pageData.deviceFingerprintId = value;
          break;
        }
      }

      // 如果没找到，尝试从sessionStorage获取
      if (!pageData.deviceFingerprintId) {
        for (const key of possibleKeys) {
          const value = sessionStorage.getItem(key);
          if (value && value.length > 8) {
            pageData.deviceFingerprintId = value;
            break;
          }
        }
      }

      // 如果还是没找到，尝试从全局变量获取
      if (!pageData.deviceFingerprintId && window.deviceFingerprint) {
        pageData.deviceFingerprintId = window.deviceFingerprint;
      }
    } catch (e) {
      console.error('Error extracting device fingerprint:', e);
    }

    // 提取GraphQL请求信息
    pageData.graphqlRequests = [];

    // 从网络请求中提取GraphQL请求
    if (window.performance && window.performance.getEntries) {
      const entries = window.performance.getEntries();
      entries.forEach(entry => {
        if (entry.name && entry.name.includes('graphql')) {
          pageData.graphqlRequests.push({
            url: entry.name,
            initiatorType: entry.initiatorType,
            duration: entry.duration
          });
        }
      });
    }

  } catch (e) {
    console.error('Error extracting page data:', e);
  }

  return pageData;
}
