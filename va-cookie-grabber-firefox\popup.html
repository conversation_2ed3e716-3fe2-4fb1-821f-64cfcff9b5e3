<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Virgin Australia Cookie <PERSON></title>
  <style>
    body {
      font-family: Arial, sans-serif;
      width: 350px;
      padding: 10px;
    }
    button {
      background-color: #e40000;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      margin-top: 10px;
      width: 100%;
    }
    button:hover {
      background-color: #c00000;
    }
    .status {
      margin-top: 10px;
      padding: 8px;
      border-radius: 4px;
      font-size: 14px;
    }
    .success {
      background-color: #e6ffe6;
      color: #006600;
    }
    .error {
      background-color: #ffe6e6;
      color: #990000;
    }
    .info {
      background-color: #e6f7ff;
      color: #0066cc;
    }
    .hidden {
      display: none;
    }
    h2 {
      margin-top: 0;
      color: #e40000;
    }
    p {
      font-size: 14px;
      line-height: 1.4;
    }
    .button-group {
      display: flex;
      gap: 10px;
      margin-top: 10px;
    }
    .button-group button {
      flex: 1;
      margin-top: 0;
    }
    .session-info {
      margin-top: 15px;
      font-size: 12px;
      color: #666;
      border-top: 1px solid #eee;
      padding-top: 10px;
    }
    .data-preview {
      margin-top: 15px;
      font-size: 12px;
      max-height: 150px;
      overflow-y: auto;
      border: 1px solid #eee;
      padding: 8px;
      border-radius: 4px;
      background-color: #f9f9f9;
    }
    .data-preview pre {
      margin: 0;
      white-space: pre-wrap;
      word-break: break-all;
    }
    .feature-list {
      margin-top: 15px;
      font-size: 12px;
      color: #666;
    }
    .feature-list ul {
      padding-left: 20px;
      margin: 5px 0;
    }
    .feature-list li {
      margin-bottom: 3px;
    }
  </style>
</head>
<body>
  <h2>Virgin Australia 增强版抓取工具</h2>
  <p>点击下方按钮抓取当前 Virgin Australia 页面的所有数据，包括 Cookie、执行ID、航班ID、行程信息等，并复制到剪贴板。</p>

  <button id="grabCookies">一键抓取所有数据</button>

  <div class="button-group">
    <button id="keepSessionBtn">自动会话保持</button>
    <button id="refreshPageBtn">立即刷新页面</button>
  </div>

  <div id="statusMessage" class="status hidden"></div>

  <div id="dataPreview" class="data-preview hidden">
    <h4 style="margin-top: 0;">数据预览：</h4>
    <pre id="dataPreviewContent"></pre>
  </div>

  <div class="feature-list">
    <h4 style="margin-bottom: 5px;">增强功能：</h4>
    <ul>
      <li>自动提取执行ID (execution ID)</li>
      <li>自动提取航班ID (flight ID)</li>
      <li>自动提取行程信息 (出发地、目的地、日期、舱位)</li>
      <li>自动提取所有Cookie (包括DCSESSIONID)</li>
      <li>自动保持会话活跃 (自动点击延长会话按钮)</li>
      <li>定时刷新页面 (防止会话过期)</li>
    </ul>
  </div>

  <div id="sessionInfo" class="session-info hidden">
    <div><strong>会话状态：</strong> <span id="sessionStatus">未监控</span></div>
    <div><strong>上次延长：</strong> <span id="lastExtension">无</span></div>
    <div><strong>自动刷新：</strong> <span>每15分钟</span></div>
    <div><strong>会话检查：</strong> <span>每30秒</span></div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
