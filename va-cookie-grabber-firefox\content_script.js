// 立即设置全局标识，确保后台脚本能检测到
window.vaContentScriptReady = false;
window.vaContentScriptVersion = "v4.8.4";
window.vaContentScriptLoading = true;

console.log("[VA ContentScript] 开始加载 (v4.8.4)...");

(async () => { // 使用IIFE使其成为一个模块，避免污染全局作用域
    console.log("[VA ContentScript] 已注入并运行 (v4)。");

    // 确保兼容Firefox和Chrome的API
    const runtimeAPI = (typeof browser !== 'undefined') ? browser : chrome;

    // Helper function to send logs back to background script
    function logToBackground(message, level = "log") {
        try {
            if (runtimeAPI && runtimeAPI.runtime) {
                runtimeAPI.runtime.sendMessage({ action: "log_from_content", data: message, level: level });
            } else {
                console.log(`[VA CS] ${level.toUpperCase()}: ${message}`);
            }
        } catch (e) {
            // Fallback if sendMessage fails (e.g., during script unload or if background is not listening)
            console.log(`[VA CS] ${level.toUpperCase()}: ${message}`);
        }
    }

    function waitForElement(selector, timeout = 10000, rootNode = document, pollIntervalMs = 200) {
        return new Promise((resolve, reject) => {
            const intervalTime = pollIntervalMs;
            let elapsedTime = 0;
            const interval = setInterval(() => {
                const element = rootNode.querySelector(selector);
                if (element && (element.offsetParent !== null || element.tagName.toLowerCase() === 'body' || element.checkVisibility?.())) {
                    clearInterval(interval);
                    resolve(element);
                    return;
                }
                elapsedTime += intervalTime;
                if (elapsedTime >= timeout) {
                    clearInterval(interval);
                    logToBackground(`CS Timeout: Element '${selector}' not found after ${timeout}ms`, "warn");
                    reject(new Error(`Element ${selector} not found after ${timeout}ms`));
                }
            }, intervalTime);
        });
    }

    function getExecutionIdFromCurrentUrl(description = "current URL") {
        try {
            const currentUrl = new URL(window.location.href);
            const hashParams = new URLSearchParams(currentUrl.hash.split('?')[1]);
            if (hashParams.has('execution')) return hashParams.get('execution');
            if (currentUrl.searchParams.has('execution')) return currentUrl.searchParams.get('execution');
        } catch (e) {
            logToBackground(`Error parsing executionId from URL (${description}): ${e.message}`, "warn");
        }
        return null;
    }

    async function performLoginIfNeeded(credentials) {
        logToBackground("检查登录状态 (v4.8.4 - Hi/Login按钮优先)...");
        const userGreetingSelectors = [
            'span.pii-data[data-translation="header.greetings"]',
            'span[class*="greeting"][class*="user" i]',
            '[data-testid="header-profile-button-name"]',
            'div[aria-label*="User account" i] button[aria-label*="Open user menu" i]'
        ];
        const loginButtonSelector = "button.dxp-button.header-item-label.logged-out-user";

        logToBackground("阶段1: 优先检查用户问候语。");
        try {
            for (const selector of userGreetingSelectors) {
                try {
                    const greetingElement = await waitForElement(selector, 1500, document, 300);
                    if (greetingElement && greetingElement.textContent &&
                        (greetingElement.textContent.trim().toLowerCase().startsWith("hi,") || greetingElement.textContent.trim().toLowerCase().startsWith("hi "))) {
                        logToBackground(`用户已登录 (检测到用户问候: ${selector} -> ${greetingElement.textContent.trim()})`);
                        return true;
                    }
                } catch (e) { /* Element not found, try next selector */ }
            }
            logToBackground("阶段1: 未通过用户问候语确认登录。");

            logToBackground(`阶段2: 尝试查找"登录"按钮.`);
            const signInButton = await waitForElement(loginButtonSelector, 7000, document, 500).catch(() => null);

            if (signInButton) {
                logToBackground('"登录"按钮找到 -> 用户未登录。');
                if (credentials && credentials.username && credentials.password) {
                    logToBackground("收到凭据，尝试自动登录...");
                    signInButton.click();
                    await new Promise(resolve => setTimeout(resolve, 3000));
                    const usernameInput = await waitForElement('input[data-test-id="sign-in-username"]', 7000, document, 500);
                    const passwordInput = await waitForElement('input[data-test-id="sign-in-password"]', 7000, document, 500);
                    const submitButton = await waitForElement('button[data-test-id="sign-in-button"]', 7000, document, 500);
                    usernameInput.value = credentials.username;
                    usernameInput.dispatchEvent(new Event('input', { bubbles: true }));
                    await new Promise(resolve => setTimeout(resolve, 300));
                    passwordInput.value = credentials.password;
                    passwordInput.dispatchEvent(new Event('input', { bubbles: true }));
                    await new Promise(resolve => setTimeout(resolve, 300));

                    if (!submitButton.disabled) {
                        submitButton.click();
                        logToBackground("登录表单已提交。等待登录成功确认 (最长40秒轮询)...");
                        let loginConfirmed = false;
                        const loginConfirmStartTime = Date.now();
                        const loginConfirmTimeout = 40000;
                        const pollIntervalAfterLogin = 750;
                        while (Date.now() - loginConfirmStartTime < loginConfirmTimeout) {
                            for (const selector of userGreetingSelectors) {
                                try {
                                   const ge = await waitForElement(selector, 1500, document, 300);
                                   if (ge && ge.textContent && (ge.textContent.trim().toLowerCase().startsWith("hi,") || ge.textContent.trim().toLowerCase().startsWith("hi "))) {
                                      logToBackground(`登录成功 (用户问候元素出现: ${selector})`); loginConfirmed = true; break;
                                   }
                                } catch(e) {/* continue polling */}
                            }
                            if (loginConfirmed) break;
                            const execId = getExecutionIdFromCurrentUrl("登录后URL轮询");
                            if (execId) {
                                logToBackground(`登录成功 (URL中检测到executionId: ${execId.substring(0,10)}...).`); loginConfirmed = true; break;
                            }
                            await new Promise(resolve => setTimeout(resolve, pollIntervalAfterLogin));
                        }
                        if (loginConfirmed) return true;
                        logToBackground("登录尝试后未确认成功。", "error");
                        throw new Error("登录超时或未能通过检查(问候/URL execId)确认成功状态。");
                    } else {
                        logToBackground("登录提交按钮被禁用。", "error");
                        throw new Error("登录提交按钮被禁用。");
                    }
                } else {
                    logToBackground('检测到"登录"按钮，但无凭据提供。', "warn");
                    throw new Error("需要登录但未提供凭据。");
                }
            } else {
                logToBackground("未找到用户问候也未找到登录按钮。最后尝试从URL获取executionId...", "warn");
                const execIdFromUrl = getExecutionIdFromCurrentUrl("最终URL上下文检查");
                if (execIdFromUrl) {
                    logToBackground("在URL中找到executionId。假定会话有效。", "info");
                    return true;
                } else {
                    logToBackground("未找到任何登录状态指示器。", "error");
                    throw new Error("无法通过任何方式确认登录状态/执行上下文。");
                }
            }
        } catch (e) {
            logToBackground(`performLoginIfNeeded 捕获到错误: ${e.message}`, "error");
            throw e;
        }
    }

    async function extractPageDataOnly(targetUrl) {
        logToBackground("开始从页面提取非Cookie数据 (v4.8.4)...");
        let executionId = getExecutionIdFromCurrentUrl();
        const userAgent = navigator.userAgent;
        const pageItinerary = {};
        if (!executionId && targetUrl) {
            logToBackground("当前URL无ExecID，尝试从targetUrl回退...", "warn");
            try {
                const originalTargetUrl = new URL(targetUrl);
                const originalHashParams = new URLSearchParams(originalTargetUrl.hash.split('?')[1]);
                 if (originalHashParams.has('execution')) {
                    executionId = originalHashParams.get('execution');
                 } else if (originalTargetUrl.searchParams.has('execution')) {
                    executionId = originalTargetUrl.searchParams.get('execution');
                 }
                 if(executionId) logToBackground(`ExecID 从 targetUrl 回退获取: ${executionId.substring(0,10)}...`);
            } catch(e) {logToBackground(`从targetUrl解析exec_id出错: ${e.message}`, "warn")}
        }
        logToBackground(executionId ? `最终提取的 ExecID (CS): ${executionId}` : "ExecID CS最终未能找到。", executionId ? "info" : "error");
        try {
             if (targetUrl) {
                const urlForItinerary = new URL(targetUrl);
                const params = new URLSearchParams(urlForItinerary.search || urlForItinerary.hash.split('?')[1]);
                pageItinerary.origin = params.get('origin') || '';
                pageItinerary.destination = params.get('destination') || '';
                const dateParam = params.get('date');
                if (dateParam) {
                    const parts = dateParam.split('-');
                    if (parts.length === 3) { pageItinerary.date = `${parts[2]}-${parts[0]}-${parts[1]}`; }
                    else { pageItinerary.date = dateParam; }
                }
                pageItinerary.cabinClass = params.get('class') || 'Business';
                pageItinerary.awardBooking = params.get('awardBooking') !== 'false'; // default true
            } else { logToBackground("targetUrl未提供给extractPageDataOnly，无法解析行程。", "warn"); }
        } catch(e) { logToBackground(`解析行程参数时出错: ${e.message}`, "warn");}
        return { executionId, user_agent: userAgent, itinerary: pageItinerary };
    }

    // Listener for messages from background.js (v6 flow)
    if (!runtimeAPI || !runtimeAPI.runtime) {
        logToBackground("运行时API不可用，无法设置消息监听器", "error");
        return;
    }

    // 确保只设置一次监听器
    if (!window.vaMessageListenerSet) {
        runtimeAPI.runtime.onMessage.addListener((request, sender, sendResponse) => {
            logToBackground(`CS收到消息: Action - ${request.action} (v4.8.4 for BG v6)`);

            if (request.action === "perform_login_and_extract") {
                logToBackground("开始处理 perform_login_and_extract 请求");

                // 使用Promise处理异步操作
                const handleAsync = async () => {
                    let extractedData = {}; // Initialize to prevent undefined issues
                    let success = false;
                    let errorMessage = "未知错误";

                    try {
                        logToBackground("开始登录和提取流程 (v4.8.4 onMessage)..");
                        await performLoginIfNeeded(request.credentials);
                        await new Promise(resolve => setTimeout(resolve, 750));
                        extractedData = await extractPageDataOnly(request.targetUrl);
                        success = true;
                        logToBackground(`数据提取完成. pageData: ${JSON.stringify(extractedData)}`, "info");
                    } catch (error) {
                        logToBackground(`perform_login_and_extract (onMessage) 出错: ${error.message}`, "error");
                        errorMessage = error.message;
                        success = false;
                        if (!extractedData || Object.keys(extractedData).length === 0) {
                            extractedData = { errorSource: "performLoginIfNeeded_or_early_extract", originalError: error.message };
                        }
                    }

                    const response = { success: success, data: extractedData, error: success ? null : errorMessage };
                    logToBackground(`准备发送响应给BG: success=${success}, error=${errorMessage}`);

                    try {
                        sendResponse(response);
                        logToBackground("响应已发送给后台脚本");
                    } catch (sendError) {
                        logToBackground(`发送响应失败: ${sendError.message}`, "error");
                        // 如果sendResponse失败，尝试通过其他方式通知背景脚本
                        try {
                            runtimeAPI.runtime.sendMessage({
                                action: "content_script_response",
                                response: response,
                                originalRequest: request.action
                            });
                        } catch (altError) {
                            logToBackground(`备用响应方式也失败: ${altError.message}`, "error");
                        }
                    }
                };

                // 执行异步处理
                handleAsync().catch(error => {
                    logToBackground(`异步处理出错: ${error.message}`, "error");
                    try {
                        sendResponse({ success: false, data: {}, error: error.message });
                    } catch (e) {
                        logToBackground(`发送错误响应失败: ${e.message}`, "error");
                        // 备用响应方式
                        try {
                            runtimeAPI.runtime.sendMessage({
                                action: "content_script_response",
                                response: { success: false, data: {}, error: error.message },
                                originalRequest: request.action
                            });
                        } catch (altError) {
                            logToBackground(`备用错误响应也失败: ${altError.message}`, "error");
                        }
                    }
                });

                return true; // 表示异步响应
            }
            else if (request.action === "ping_content_script") {
                logToBackground("CS收到来自BG的ping, 发送pong响应 (v4.8.4)。");
                try {
                    sendResponse({ status: "pong", version: "v4.8.4" });
                    return false; // Synchronous response for ping
                } catch (e) {
                    logToBackground(`Ping响应失败: ${e.message}`, "error");
                    return false;
                }
            }
            else {
                logToBackground(`未知的action: ${request.action}`, "warn");
                try {
                    sendResponse({ success: false, error: "未知的action" });
                } catch (e) {
                    logToBackground(`未知action响应失败: ${e.message}`, "error");
                }
                return false;
            }
        });

        window.vaMessageListenerSet = true;
        logToBackground("内容脚本(v4.8.4)监听器已设置。");
    } else {
        logToBackground("内容脚本监听器已存在，跳过重复设置。");
    }

    // 设置全局标识，表示内容脚本已准备就绪
    window.vaContentScriptReady = true;
    window.vaContentScriptVersion = "v4.8.4";
    window.vaContentScriptLoading = false;

    // 确保全局变量可以被检测到
    console.log("[VA ContentScript] 全局变量已设置:", {
        ready: window.vaContentScriptReady,
        version: window.vaContentScriptVersion,
        loading: window.vaContentScriptLoading
    });

    setTimeout(() => {
        try {
            logToBackground("CS尝试发送 cs_ready 消息到BG (v4.8.4)。");
            runtimeAPI.runtime.sendMessage({ action: "cs_ready", version: "v4.8.4" });
            logToBackground("cs_ready 消息发送完成");
        } catch (e) {
            logToBackground(`CS发送 cs_ready 消息失败: ${e.message}`, "error");
        }
    }, 150); // Increased delay slightly to 150ms

    // 添加额外的延迟测试
    setTimeout(() => {
        try {
            logToBackground("CS发送延迟测试消息");
            runtimeAPI.runtime.sendMessage({ action: "cs_delayed_ready", version: "v4.8.4", timestamp: Date.now() });
        } catch (e) {
            logToBackground(`CS发送延迟测试消息失败: ${e.message}`, "error");
        }
    }, 1000); // 1秒后再发送一次

    // REMOVED: Self-initiating logic for automated flow (initiateAutoExtraction and its call)
    // async function initiateAutoExtraction() { ... }
    // if (window.self === window.top) { ... setTimeout(initiateAutoExtraction, 1000); ... }

})();
