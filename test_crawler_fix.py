#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试爬虫启动修复
"""

import sys
import os
import time
import threading
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_crawler_startup():
    """测试爬虫启动和is_alive_internal方法"""
    print("=" * 60)
    print("爬虫启动测试")
    print("=" * 60)
    
    try:
        from high_concurrency_crawler import HighConcurrencyCrawler
        from api_client import VAApiClient
        
        print("[1] 创建爬虫实例...")
        crawler = HighConcurrencyCrawler(max_workers=3)
        
        print("[2] 添加测试目标...")
        crawler.add_target("SEA", "HND", "2025-06-04", "NH117")
        
        print("[3] 创建API客户端配置...")
        api_client = VAApiClient()
        
        print("[4] 测试is_alive_internal方法 (启动前)...")
        alive_before = crawler.is_alive_internal()
        print(f"   启动前状态: {alive_before} (应该为False)")
        
        print("[5] 启动爬虫...")
        start_success = crawler.start(task_id_for_redis=999, initial_api_client_template=api_client)
        print(f"   启动结果: {start_success}")
        
        if start_success:
            print("[6] 测试启动后的状态检查...")
            
            # 立即检查 (可能在启动缓冲期内)
            alive_immediate = crawler.is_alive_internal()
            print(f"   启动后立即检查: {alive_immediate} (应该为True)")
            
            # 等待1秒后检查
            time.sleep(1)
            alive_after_1s = crawler.is_alive_internal()
            print(f"   1秒后检查: {alive_after_1s} (应该为True)")
            
            # 检查工作线程状态
            worker_alive = crawler.worker_thread.is_alive() if crawler.worker_thread else False
            print(f"   工作线程状态: {worker_alive}")
            
            # 检查运行标志
            print(f"   运行标志: {crawler.running}")
            
            # 等待几秒让爬虫运行
            print("[7] 让爬虫运行5秒...")
            for i in range(5):
                time.sleep(1)
                alive_status = crawler.is_alive_internal()
                print(f"   第{i+1}秒: {alive_status}")
                if not alive_status:
                    break
            
            print("[8] 停止爬虫...")
            crawler.stop()
            
            # 检查停止后状态
            time.sleep(1)
            alive_after_stop = crawler.is_alive_internal()
            print(f"   停止后状态: {alive_after_stop} (应该为False)")
            
        else:
            print("   ❌ 爬虫启动失败")
            return False
        
        print("\n✅ 爬虫启动测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_monitoring_logic():
    """测试监控逻辑"""
    print("\n" + "=" * 60)
    print("监控逻辑测试")
    print("=" * 60)
    
    try:
        from high_concurrency_crawler import HighConcurrencyCrawler
        from api_client import VAApiClient
        
        print("[1] 模拟任务监控工作者的检查逻辑...")
        
        crawler = HighConcurrencyCrawler(max_workers=3)
        crawler.add_target("SEA", "HND", "2025-06-04", "NH117")
        
        api_client = VAApiClient()
        
        # 模拟启动
        start_success = crawler.start(task_id_for_redis=999, initial_api_client_template=api_client)
        
        if start_success:
            print("[2] 模拟监控检查...")
            
            # 模拟UI中的监控逻辑
            def simulate_monitoring_check():
                if hasattr(crawler, 'is_alive_internal'):
                    # 检查启动时间
                    crawler_start_time = getattr(crawler, 'stats', {}).get('start_time')
                    if crawler_start_time:
                        time_since_start = (datetime.now() - crawler_start_time).total_seconds()
                        print(f"   启动时间: {time_since_start:.1f}秒前")
                        
                        # 如果启动时间少于10秒，不进行死亡检查
                        if time_since_start < 10:
                            print(f"   ⏳ 启动缓冲期内，跳过死亡检查")
                            return "buffering"
                    
                    # 检查是否存活
                    is_alive = crawler.is_alive_internal()
                    print(f"   存活状态: {is_alive}")
                    
                    if not is_alive:
                        print(f"   ❌ 检测到爬虫死亡")
                        return "dead"
                    else:
                        print(f"   ✅ 爬虫正常运行")
                        return "alive"
                
                return "unknown"
            
            # 立即检查 (应该在缓冲期内)
            print("\n   立即检查:")
            result1 = simulate_monitoring_check()
            
            # 等待几秒后检查
            print("\n   3秒后检查:")
            time.sleep(3)
            result2 = simulate_monitoring_check()
            
            # 再等待几秒
            print("\n   再等3秒后检查:")
            time.sleep(3)
            result3 = simulate_monitoring_check()
            
            print(f"\n   检查结果: {result1} -> {result2} -> {result3}")
            
            crawler.stop()
            
        print("\n✅ 监控逻辑测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 监控逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print(f"开始测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行测试
    test1_ok = test_crawler_startup()
    test2_ok = test_monitoring_logic()
    
    print(f"\n" + "=" * 60)
    print(f"测试结果:")
    print(f"  爬虫启动测试: {'✅ 通过' if test1_ok else '❌ 失败'}")
    print(f"  监控逻辑测试: {'✅ 通过' if test2_ok else '❌ 失败'}")
    
    if test1_ok and test2_ok:
        print(f"\n🎉 所有测试通过！爬虫启动问题已修复。")
        print(f"现在可以重新运行ui.py，应该不会再出现'意外停止'的问题。")
    else:
        print(f"\n⚠️  部分测试失败，请检查相关问题。")
