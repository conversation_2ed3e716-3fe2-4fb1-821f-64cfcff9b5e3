import requests
from config import load_config
import json
from datetime import datetime
import random # 新增导入
import string # 新增导入
import time # 新增导入
import os # 导入os模块用于文件路径操作

# 新增：getBookingCart GraphQL 查询
GET_BOOKING_CART_QUERY = """
query getBookingCart {
  getBookingCart {
    originalResponse
    __typename
  }
}
"""

class VAApiClient:
    def __init__(self):
        self.config = load_config()
        self.session = requests.Session()
        self.base_url = "https://book.virginaustralia.com/api/graphql"
        self.token = None
        self.execution_id = None # 新增：存储 execution ID
        self.initial_ibeopentoken_value = None # 新增: 存储最初导入的ibeopentoken
        self.user_agent_override = None # 新增: 用于存储从导入数据中获取的User-Agent
        self.proxies = []  # 代理列表
        self.current_proxy_index = 0  # 当前使用的代理索引
        self.use_proxy = True  # 默认启用代理

        # 保存DCSESSIONID的变量
        self.saved_dcsessionid = None
        self.saved_dcsessionid_domain = None
        self.saved_dcsessionid_path = None
        
        self.shopping_basket_hash_code = None # 在 book_flight 中设置
        self.current_passengers_info = None # 在 book_flight 中设置
        self.confirmed_cash_amount = None 
        self.confirmed_cash_currency = None
        self.confirmed_award_amount = None
        self.device_fingerprint_id = None

        # 加载代理配置
        self.load_proxies()
        if self.proxies:
            self.set_proxy()  # 设置初始代理
            print(f"[*] 已启用代理，共加载 {len(self.proxies)} 个代理")
        else:
            print("[!] 未能加载代理，将使用直接连接")
            self.use_proxy = False

        # 加载cookie
        try:
            with open('va_ana_redeem/va_cookies.json', 'r', encoding='utf-8') as f:
                cookies = json.load(f)
            for cookie in cookies:
                self.session.cookies.set(cookie['name'], cookie['value'], domain=cookie.get('domain'))
            print("[*] 已加载cookie")
        except Exception as e:
            print(f"[!] 初始化时加载cookie失败: {e}") 

        print("[*] VAApiClient initialized.")
        print(f"[*] 使用的 Base URL: {self.base_url}")
        if self.use_proxy and self.session.proxies:
            print(f"[*] 当前代理: {self.session.proxies.get('http') or self.session.proxies.get('https')}")
        elif self.use_proxy:
            print("[!] 代理已启用，但当前未设置有效代理。")
        else:
            print("[*] 未使用代理。")

    def load_proxies(self):
        """加载代理配置"""
        try:
            # 直接在当前目录和上级目录查找代理配置文件
            current_dir = os.path.dirname(os.path.abspath(__file__))
            possible_paths = [
                os.path.join(current_dir, "proxy_config.json"),
                os.path.join(current_dir, "..", "proxy_config.json"),
                "proxy_config.json"  # 当前工作目录
            ]

            proxy_config_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    proxy_config_path = path
                    break

            if not proxy_config_path:
                print(f"[!] 未找到代理配置文件，尝试过以下路径: {possible_paths}")
                return

            # 加载代理配置
            with open(proxy_config_path, 'r', encoding='utf-8') as f:
                proxy_list = json.load(f)

            # 处理代理格式
            for proxy in proxy_list:
                if proxy and isinstance(proxy, str):
                    # 解析代理字符串 host:port:username:password
                    parts = proxy.split(':')
                    if len(parts) >= 2:  # 至少有主机和端口
                        host = parts[0]
                        port = parts[1]

                        # 检查是否有用户名和密码
                        if len(parts) >= 4:
                            username = parts[2]
                            password = parts[3]
                            proxy_url = f"http://{username}:{password}@{host}:{port}"
                        else:
                            proxy_url = f"http://{host}:{port}"

                        self.proxies.append({
                            "http": proxy_url,
                            "https": proxy_url
                        })

            print(f"[*] 已加载 {len(self.proxies)} 个代理")
        except Exception as e:
            print(f"[!] 加载代理配置失败: {e}")

    def set_proxy(self, index=None):
        """设置当前使用的代理"""
        if not self.proxies:
            print("[!] 代理列表为空，无法设置代理")
            self.session.proxies = {}
            return False

        if index is not None:
            # 使用指定索引的代理
            if 0 <= index < len(self.proxies):
                self.current_proxy_index = index
            else:
                print(f"[!] 指定的代理索引 {index} 超出范围，使用随机代理")
                self.current_proxy_index = random.randint(0, len(self.proxies) - 1)
        else:
            # 随机选择一个代理
            self.current_proxy_index = random.randint(0, len(self.proxies) - 1)

        # 设置代理
        self.session.proxies = self.proxies[self.current_proxy_index]
        print(f"[*] 已设置代理: {list(self.session.proxies.values())[0]}")
        return True

    def rotate_proxy(self):
        """随机轮换代理"""
        if not self.proxies:
            print("[!] 代理列表为空，无法轮换代理")
            return False

        # 随机选择一个不同的代理
        if len(self.proxies) > 1:
            # 如果有多个代理，确保选择一个不同的代理
            new_index = self.current_proxy_index
            while new_index == self.current_proxy_index:
                new_index = random.randint(0, len(self.proxies) - 1)
            self.current_proxy_index = new_index
        else:
            # 如果只有一个代理，仍然使用它
            self.current_proxy_index = 0

        # 设置代理
        self.session.proxies = self.proxies[self.current_proxy_index]
        print(f"[*] 已随机轮换到新代理: {list(self.session.proxies.values())[0]}")
        return True

    def set_cookies_from_list(self, cookies_list):
        """直接从列表设置 session cookies"""
        self.session.cookies.clear() # 清空旧 cookies
        if not cookies_list:
            print("[!] 传入的 cookie 列表为空。")
            return False
        try:
            # 先收集所有cookie，按名称分组，确保每个名称只有一个cookie
            cookie_dict = {}
            for cookie in cookies_list:
                cookie_name = cookie['name']
                # 如果有多个同名cookie，使用最后一个
                cookie_dict[cookie_name] = cookie

            # 设置去重后的cookies
            for cookie_name, cookie in cookie_dict.items():
                # requests session 需要 domain 参数，如果 cookie 中没有，尝试从 URL 推断或省略
                cookie_domain = cookie.get('domain')
                # 确保 domain 不为空字符串
                if cookie_domain == "":
                    cookie_domain = None
                # 如果 domain 以 '.' 开头，requests 可能不需要它
                if cookie_domain and cookie_domain.startswith('.'):
                     cookie_domain = cookie_domain[1:]

                self.session.cookies.set(
                    cookie['name'],
                    cookie['value'],
                    domain=cookie_domain,
                    path=cookie.get('path', '/') # 提供默认 path
                )

            print(f"[*] 已从列表加载 {len(cookie_dict)} 个唯一 cookies 到 session (原始列表长度: {len(cookies_list)})。")
            return True
        except Exception as e:
            import traceback
            print(f"[!] 从列表设置 cookie 失败: {e}")
            print(traceback.format_exc()) # 打印详细的错误堆栈信息
            return False

    def set_device_fingerprint_id(self, device_fingerprint_id):
        """设置设备指纹ID，用于支付请求"""
        if not device_fingerprint_id:
            print("[!] 设备指纹ID不能为空")
            return False

        self.device_fingerprint_id = device_fingerprint_id
        print(f"[*] 已设置设备指纹ID: {device_fingerprint_id[:10]}...")
        return True

    # 移除 reload_cookies 方法
    # def reload_cookies(self): ...

    def _graphql_request(self, operation_name, variables, query, return_headers=False): # 保留 return_headers 参数以备后用
       
        effective_user_agent = self.user_agent_override if self.user_agent_override else 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36'
        application_id = 'SWS1:SBR-DigConShpBk:fd34efe9a9' # 从日志获取

        headers = {
            "Content-Type": "application/json",
            "application-id": application_id,
            "User-Agent": effective_user_agent, # 使用实际生效的User-Agent
            "Accept": "*/*",
            "x-sabre-storefront": "VADX",
            "Referer": "https://book.virginaustralia.com/dx/VADX/",
            "pos": "hk-en",
            "channel": "",
            "journeyType": "one-way",
        }

        # --- Token and Execution ID Handling --- BEGIN ---
        # Strictly use the initial ibeopentoken for the session cookie
        # Clear any existing ibeopentoken from session.cookies
        for cookie in list(self.session.cookies):
            if cookie.name == 'ibeopentoken':
                try:
                    self.session.cookies.clear(domain=cookie.domain, path=cookie.path, name=cookie.name)
                except KeyError:
                    print(f"[!] Clearing ibeopentoken {cookie.name} failed, may have already been cleared.")
        
        # If an initial ibeopentoken was stored, add it back to the session
        if self.initial_ibeopentoken_value:
            self.session.cookies.set('ibeopentoken', self.initial_ibeopentoken_value, domain='book.virginaustralia.com', path='/')
            # print(f"[*] Ensured initial ibeopentoken is in session cookies.") # Optional log

        # Authorization header should use self.token (assumed to be set from initial ibeopentoken or other primary token source by ui.py)
        # MODIFIED FOR SCHEME 1
        if operation_name == "getBookingCart":
            basic_auth_credential = "Basic anNvbl91c2VyOmpzb25fcGFzc3dvcmQ=" 
            headers["Authorization"] = basic_auth_credential
            print(f"[*] Using Basic Auth for {operation_name}.")
        elif self.token: # For other operations, use Bearer token if available
            headers["Authorization"] = f"Bearer {str(self.token)}" # Explicitly cast to string
            # print(f"[*] Using self.token for Authorization header: {self.token[:10]}...") 
        else: # No token for other operations
            if "Authorization" in headers: # Remove if it was somehow set
                del headers["Authorization"]
            print(f"[!] WARNING: self.token is not set for {operation_name}. Authorization header will be missing or was removed.")


        # Execution ID header (self.execution_id should be set by ui.py from imported data)
        if self.execution_id:
            headers['execution'] = str(self.execution_id) # Explicitly cast to string
            # print(f"[*] Using self.execution_id for execution header: {self.execution_id}") # Optional log
        else:
            print("[!] WARNING: self.execution_id is not set. Execution header will be missing.")
        # --- Token and Execution ID Handling --- END ---

        if operation_name == "bookingAirSearch" and "airSearchInput" in variables:
            try:
                search_date_str = variables["airSearchInput"]["itineraryParts"][0]["when"]["date"]
                search_date_obj = datetime.strptime(search_date_str, "%Y-%m-%d")
                headers["activeMonth"] = search_date_obj.strftime("%m-%d-%Y")
                headers["date"] = search_date_obj.strftime("%m-%d-%Y")
            except Exception as e:
                print(f"[!] 无法解析搜索日期以设置 activeMonth/date: {e}")

        payload = {
            "operationName": operation_name,
            "variables": variables,
            "query": query
        }

        timeout_duration = 60 if operation_name == "bookingPurchase" else 30 # Increased default timeout slightly
        # max_retries = 3 # Removed
        # retry_count = 0 # Removed

        # --- DCSESSIONID Handling --- BEGIN ---
        temp_removed_dcsessionid_details = None
        # For bookingAirSearch with proxy, temporarily remove DCSESSIONID from session.cookies
        if operation_name == "bookingAirSearch" and self.use_proxy and self.proxies:
            for cookie in list(self.session.cookies):
                if cookie.name == 'DCSESSIONID':
                    temp_removed_dcsessionid_details = {'name': cookie.name, 'value': cookie.value, 'domain': cookie.domain, 'path': cookie.path}
                    try:
                        self.session.cookies.clear(domain=cookie.domain, path=cookie.path, name=cookie.name)
                        print(f"[*] Temporarily removed DCSESSIONID ({temp_removed_dcsessionid_details['value'][:10]}...) for proxy search.")
                    except KeyError:
                        print(f"[!] Clearing DCSESSIONID {cookie.name} failed, may have already been cleared.")
                    break # Assume only one DCSESSIONID
        else:
            # For other operations OR search without proxy, ensure saved DCSESSIONID is in session
            if self.saved_dcsessionid:
                current_session_dcsessionid = None
                for cookie in self.session.cookies:
                    if cookie.name == 'DCSESSIONID' and cookie.domain == self.saved_dcsessionid_domain: # Check domain too
                        current_session_dcsessionid = cookie.value
                        break
                if current_session_dcsessionid != self.saved_dcsessionid:
                    # Clear any potentially incorrect DCSESSIONID before setting the saved one
                    for cookie in list(self.session.cookies):
                        if cookie.name == 'DCSESSIONID' and cookie.domain == self.saved_dcsessionid_domain:
                             try:
                                self.session.cookies.clear(domain=cookie.domain, path=cookie.path, name=cookie.name)
                             except KeyError:
                                pass # ignore if already gone
                    print(f"[DEBUG APIClient] About to set DCSESSIONID. saved_dcsessionid: {self.saved_dcsessionid!r}, type: {type(self.saved_dcsessionid)}")
                    print(f"[DEBUG APIClient] saved_dcsessionid_domain: {self.saved_dcsessionid_domain!r}, type: {type(self.saved_dcsessionid_domain)}")
                    print(f"[DEBUG APIClient] saved_dcsessionid_path: {self.saved_dcsessionid_path!r}, type: {type(self.saved_dcsessionid_path)}")
                    
                    cookie_args = {'name': 'DCSESSIONID', 'value': self.saved_dcsessionid}
                    if self.saved_dcsessionid_domain is not None:
                        cookie_args['domain'] = self.saved_dcsessionid_domain
                    if self.saved_dcsessionid_path is not None:
                        cookie_args['path'] = self.saved_dcsessionid_path
                    
                    self.session.cookies.set(**cookie_args)
                    print(f"[*] Ensured saved DCSESSIONID ({self.saved_dcsessionid[:10]}...) is in session for non-proxy/booking op.")
        # --- DCSESSIONID Handling --- END ---

        # Log current DCSESSIONID state before request
        final_dcsessionid_in_session = None
        for cookie in self.session.cookies:
            if cookie.name == 'DCSESSIONID':
                final_dcsessionid_in_session = cookie.value
                break
        if final_dcsessionid_in_session:
            print(f"[*] DCSESSIONID in session before request: {final_dcsessionid_in_session[:10]}...")
        else:
            # This warning is more relevant if it's not a proxy search that intended to remove it
            if not (operation_name == "bookingAirSearch" and self.use_proxy and self.proxies):
                 print("[!] WARNING: No DCSESSIONID in session before non-proxy/booking request.")
            else:
                 print("[*] No DCSESSIONID in session for proxy search (as intended).")

        # Proxy setup / Direct connection based on operation type
        active_proxy_for_request = None
        original_session_proxies = self.session.proxies.copy() # Save original proxies

        if operation_name == "bookingAirSearch":
            if self.use_proxy and self.proxies:
                if not self.set_proxy(): # rotate_proxy is now part of set_proxy if no index given
                    print("[!] Failed to set proxy for search, attempting direct connection.")
                    self.session.proxies = {}
                else:
                    active_proxy_for_request = list(self.session.proxies.values())[0] if self.session.proxies else None
                    print(f"[*] Using proxy for search: {active_proxy_for_request}")
            else:
                self.session.proxies = {}
                print(f"[*] Using direct connection for search (proxy not enabled or not available).")
        else:
            if self.session.proxies: # If there were proxies (e.g. from a previous search)
                print(f"[*] Using direct connection for {operation_name} (clearing session proxies).")
            self.session.proxies = {}
        
        # The existing multiple ibeopentoken cleanup logic here can be removed 
        # as we now enforce the initial_ibeopentoken_value at the start of this method.
        # ibeopentoken_cookies = [] ... (REMOVE THIS BLOCK)

        # --- 详细日志记录请求信息 ---
        self._log_request_details(self.base_url, headers, self.session.cookies.get_dict(), payload, operation_name, active_proxy_for_request)
        # --- 日志结束 ---

        # current_cookies = self.session.cookies.copy() # No longer needed here, DCSESSIONID handled above

        # The DCSESSIONID check block below is also largely handled by the new logic above.
        # We can simplify it or remove it if the above is robust.
        # For now, let's comment out the redundant parts of the old DCSESSIONID check.
        # dcsessionid = None ... (OLD DCSESSIONID CHECK BLOCK - MOSTLY REDUNDANT)
        # ...
        # if dcsessionid: ...
        # else: print("[!] 警告: 当前会话中没有DCSESSIONID，这可能导致会话状态丢失") - Covered by new log above

        resp = None # Initialize resp
        # while retry_count <= max_retries: # Removed retry loop
        try:
            if operation_name in ["bookingAirSearch", "bookingAddItinerary", "bookingPurchase"]:
                if operation_name == "bookingAirSearch" and active_proxy_for_request:
                    print(f"\033[92m[*] Sending {operation_name} via proxy {active_proxy_for_request}... [0m")
                elif operation_name == "bookingAirSearch":
                    print(f"\033[92m[*] Sending {operation_name} directly... [0m")
                else:
                    print(f"[*] Sending {operation_name} directly...")
            
            resp = self.session.post(self.base_url, json=payload, headers=headers, timeout=timeout_duration)

            # Log non-200 status codes immediately for relevant operations
            if resp.status_code != 200 or operation_name in ["bookingAirSearch", "bookingAddItinerary", "bookingPurchase"]:
                if operation_name == "bookingAirSearch" and resp.status_code == 200:
                     # This specific green log for 200 on search can remain if desired, or be simplified
                    print(f"\033[92mResponse status code for {operation_name}: {resp.status_code}\033[0m")
                else:
                    print(f"Response status code for {operation_name}: {resp.status_code}")
            
      

        except requests.exceptions.RequestException as e:
            print(f"[!] RequestException during {operation_name}: {e}")
            # Restore proxies and DCSESSIONID before raising, in case they were modified for a search attempt
            self.session.proxies = original_session_proxies
            if temp_removed_dcsessionid_details:
                self.session.cookies.set(
                    temp_removed_dcsessionid_details['name'], 
                    temp_removed_dcsessionid_details['value'], 
                    domain=temp_removed_dcsessionid_details['domain'], 
                    path=temp_removed_dcsessionid_details['path']
                )
                print(f"[*] Restored temporarily removed DCSESSIONID ({temp_removed_dcsessionid_details['value'][:10]}...) after exception.")
            raise # Re-raise the exception immediately
        
    
        self.session.proxies = original_session_proxies # Restore session proxies to original state for next independent call

        # Restore DCSESSIONID if it was temporarily removed for a proxy search
        if temp_removed_dcsessionid_details:
       
            for cookie in list(self.session.cookies):
                if cookie.name == 'DCSESSIONID' and cookie.domain == temp_removed_dcsessionid_details['domain']:
                    try:
                        self.session.cookies.clear(domain=cookie.domain, path=cookie.path, name=cookie.name)
                    except KeyError:
                        pass # ignore
            self.session.cookies.set(temp_removed_dcsessionid_details['name'], temp_removed_dcsessionid_details['value'], domain=temp_removed_dcsessionid_details['domain'], path=temp_removed_dcsessionid_details['path'])
            print(f"[*] Restored temporarily removed DCSESSIONID ({temp_removed_dcsessionid_details['value'][:10]}...) after proxy search.")

        if resp is None: # Handles case where all retries failed with exceptions
            print(f"[!] Request {operation_name} ultimately failed after retries due to exceptions.")
            raise requests.exceptions.RequestException(f"{operation_name} failed after multiple retries.")

        # Process response headers for token/execution_id (Only if they weren't initially set)
        # This part is less critical if ui.py robustly sets self.token and self.execution_id from import
        auth_header = resp.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer ') and not self.token: # Only if self.token wasn't set from import
            new_token = auth_header.split('Bearer ')[1]
            if new_token:
                self.token = new_token # This might override a more reliable token from cookie import
                print(f"[*] Updated self.token from Authorization header: {new_token[:10]}...")

        execution_header = resp.headers.get('execution')
        if execution_header and not self.execution_id: # Only if self.execution_id wasn't set from import
            self.execution_id = execution_header
            print(f"[*] Updated self.execution_id from execution header: {execution_header}")

        if 'Set-Cookie' in resp.headers:
            print(f"[*] Response for {operation_name} included Set-Cookie header. Session cookies updated by requests library.")
            # The ibeopentoken is now strictly managed at the start of the request.
            # We no longer need to hunt for it here to update self.token unless self.token is still None.
            if not self.token:
                for cookie in self.session.cookies: # Check current session cookies (updated by requests)
                    if cookie.name == 'ibeopentoken': # Check for 'ibeopentoken' specifically
                        self.token = cookie.value
                        self.initial_ibeopentoken_value = cookie.value # If we get it here, it's our new 'initial'
                        print(f"[*] self.token updated from 'ibeopentoken' in Set-Cookie: {cookie.value[:10]}...")
                        break
        
        # Enhanced error handling for non-200 responses
        try:
            resp.raise_for_status()  # Will raise an HTTPError if the HTTP request returned an unsuccessful status code
            response_json = resp.json()
        except requests.exceptions.HTTPError as http_err:
            print(f"[!!!] HTTP error occurred in _graphql_request for {operation_name}: {http_err}")
            try:
                # Try to parse the response body for more detailed error information
                error_details = resp.json()
                print(f"[!!!] Server response (JSON error details): {json.dumps(error_details, indent=2)}")
                # Potentially re-raise with more info or return a structured error
                # For now, we'll let it be re-raised by the original raise_for_status if not caught later,
                # or allow specific handling below if it's a 500.
                response_json = error_details # Assign to response_json so downstream code can see errors
            except json.JSONDecodeError:
                # If the response isn't JSON, print the raw text
                print(f"[!!!] Server response (non-JSON): {resp.text}")
                response_json = {"errors": [{"message": f"HTTP error {resp.status_code} with non-JSON response for {operation_name}", "details": resp.text}]}
            # If it's a 500 error specifically, we might want to ensure it propagates clearly
            if resp.status_code == 500:
                 # Ensure that an exception is raised if not already, or that response_json reflects the error.
                 # The original raise_for_status() already does this.
                 # If we wanted to add more custom behavior for 500s, it would go here.
                 pass # raise_for_status() above already handles this.
            # For other client/server errors, we also let raise_for_status() handle it.
            # This block is mainly for logging the body.
            # We will re-raise the original error to ensure calling code knows about the failure
            raise http_err
        except json.JSONDecodeError as json_err:
            print(f"[!!!] JSON decode error in _graphql_request for {operation_name}: {json_err}")
            print(f"[!!!] Raw response text: {resp.text[:500]}...") # Log first 500 chars
            # This indicates a successful HTTP status but malformed JSON response
            response_json = {"errors": [{"message": f"JSON decode error for {operation_name}", "details": resp.text[:500]}]}
            # Depending on strictness, we might want to raise an error here too.
            # For now, return the structured error.
            # raise

        if return_headers:
            return response_json, resp.headers
        else:
            return response_json

    def _log_request_details(self, url, headers, cookies, payload, operation_name, proxy):
        """Helper method to log request details."""
        print(f"--- [REQUEST LOG for {operation_name}] ---")
        print(f"  URL: {url}")
        print(f"  Method: POST")
        if proxy:
            print(f"  Proxy: {proxy}")
        else:
            print(f"  Proxy: None")
        print(f"  Headers: {json.dumps(headers, indent=2)}")
        print(f"  Cookies: {json.dumps(cookies, indent=2)}")
        #为了避免敏感信息暴露过多，payload只记录operationName和部分variables结构
        logged_payload = {
            "operationName": payload.get("operationName"),
            "variables_structure": {k: (type(v).__name__ if not isinstance(v, (dict, list)) else ("dict" if isinstance(v, dict) else "list")) for k, v in payload.get("variables", {}).items()}
        }
        if "airSearchInput" in payload.get("variables", {}): # 特别记录搜索参数
            logged_payload["variables_airSearchInput"] = payload["variables"]["airSearchInput"]

        print(f"  Payload (summary): {json.dumps(logged_payload, indent=2, default=str)}")
        if operation_name == "BookingPurchase": # 对于支付操作，更谨慎处理payload日志
            print("  Payload (BookingPurchase): Variables contain sensitive payment data, not fully logged.")
        elif payload.get("variables"):
             print(f"  Payload Variables (full - BE CAREFUL WITH SENSITIVE DATA): {json.dumps(payload.get('variables'), indent=2, default=str)}")


        print(f"--- [END REQUEST LOG for {operation_name}] ---")

    def login(self):
        # token已通过playwright获取，此处无需登录
        pass

    def search_flights(self, origin, destination, date, cabin="Business", award_booking=True):
        query = """
        query bookingAirSearch($airSearchInput: CustomAirSearchInput) {
          bookingAirSearch(airSearchInput: $airSearchInput) {
            originalResponse
            __typename
          }
        }
        """
        variables = {
            "airSearchInput": {
                "cabinClass": cabin,  # 默认使用Business舱位
                "awardBooking": award_booking,
                "promoCodes": [],
                "searchType": "BRANDED",
                "itineraryParts": [
                    {
                        "from": {"useNearbyLocations": False, "code": origin},
                        "to": {"useNearbyLocations": False, "code": destination},
                        "when": {"date": date}
                    }
                ],
                "passengers": {"ADT": 1}
            }
        }

        # 以绿色显示搜索参数
        print(f"\033[92m[*] 搜索航班: {origin} -> {destination}, 日期: {date}, 舱位: {cabin}, 积分票: {award_booking}\033[0m")

        result = self._graphql_request("bookingAirSearch", variables, query)

        # 检查搜索结果并以绿色显示
        offers = result.get("data", {}).get("bookingAirSearch", {}).get("originalResponse", {}).get("unbundledOffers", [[]])[0]
        if offers:
            print(f"\033[92m[*] 搜索结果: 找到 {len(offers)} 个航班选项\033[0m")

            # 详细显示每个航班的舱位信息
            for i, offer in enumerate(offers):
                segments = offer.get("itineraryPart", [{}])[0].get("segments", [])
                offer_flight_nos = []
                cabin_classes = []

                for seg in segments:
                    flight = seg.get("flight", {})
                    flight_no = flight.get("flightNumber")
                    airline = flight.get("airlineCode")
                    if flight_no and airline:
                        offer_flight_nos.append(f"{airline}{flight_no}")

                    cabin_class = seg.get("cabinClass", "")
                    cabin_classes.append(cabin_class)

                print(f"\033[92m[*] 航班 {i+1}: {'+'.join(offer_flight_nos)}, 舱位: {', '.join(cabin_classes)}\033[0m")
        else:
            print(f"\033[92m[*] 搜索结果: 未找到航班\033[0m")

        return result

    def book_flight(self, selected_offer, passengers_info_list: list, award_amount_ref=None, cash_amount_ref=None, cash_currency_ref=None):
        """
        选择航班、获取购物车、添加行程、更新乘客信息，并准备支付。
        返回: (bool: success, dict_or_str: data_for_payment or error_message, str: error_code or None)
        Error codes: "INVALID_OFFER", "NO_HASH_CODE", "GET_CART_FAILED", 
                       "ADD_ITINERARY_FAILED", "UPDATE_PASSENGERS_FAILED", "CONFIRM_PAYMENT_FAILED", "EXCEPTION"
        """
        try:
            # 存储传入的金额参考
            self.confirmed_award_amount = int(award_amount_ref) if award_amount_ref is not None else None
            self.confirmed_cash_amount = float(cash_amount_ref) if cash_amount_ref is not None else None
            self.confirmed_cash_currency = cash_currency_ref
            print(f"[*] book_flight: 接收到的参考金额 - Award: {self.confirmed_award_amount}, Cash: {self.confirmed_cash_amount} {self.confirmed_cash_currency}")

            if not isinstance(selected_offer, dict):
                print("[!] selected_offer 不是一个有效的字典类型。")
                return False, "selected_offer is not a valid dictionary.", "INVALID_OFFER"

            # 步骤 1: 提取 shoppingBasketHashCode
            current_shopping_basket_hash_code = selected_offer.get('shoppingBasketHashCode')
            if not current_shopping_basket_hash_code:
                try:
                    brand_offers = selected_offer.get('brandedResults', {}).get('itineraryPartBrands', [{}])[0].get('brandOffers', [])
                    if brand_offers and isinstance(brand_offers, list) and len(brand_offers) > 0:
                        current_shopping_basket_hash_code = brand_offers[0].get('shoppingBasketHashCode')
                        if current_shopping_basket_hash_code:
                            print(f"[*] 从 brandOffers 中提取到 shoppingBasketHashCode: {current_shopping_basket_hash_code}")
                except Exception as e_extract:
                     print(f"[!] 尝试从 brandOffers 提取 shoppingBasketHashCode 时出错: {e_extract}")
            
            if not current_shopping_basket_hash_code:
                err_msg = "无法从 selected_offer 中提取 'shoppingBasketHashCode'"
                print(f"[!] {err_msg}")
                print(f"[*] selected_offer (partial): {str(selected_offer)[:500]}...")
                return False, err_msg, "NO_HASH_CODE"
            
            self.shopping_basket_hash_code = str(current_shopping_basket_hash_code)
            print(f"[*] 选定航班的 shoppingBasketHashCode: {self.shopping_basket_hash_code}")

            # 步骤 3: 添加行程
            # add_itinerary 需要纯哈希码字符串
            print(f"[*] 准备调用 add_itinerary with hash: {self.shopping_basket_hash_code}")
            add_success, add_data_or_msg, add_err_code = self.add_itinerary(self.shopping_basket_hash_code)
            if not add_success:
                print(f"[!] 添加行程失败: {add_data_or_msg}")
                return False, f"Add itinerary failed: {add_data_or_msg}", add_err_code
            
            # add_itinerary 成功时返回的是 bookingAddItinerary 的数据部分
            # 我们需要从中更新 shoppingBasketHashCode (如果API返回了新的)
            if isinstance(add_data_or_msg, dict):
                original_response_itinerary = add_data_or_msg.get("originalResponse", {})
                updated_hash_code = original_response_itinerary.get("shoppingBasketHashCode")
                if updated_hash_code and str(updated_hash_code) != self.shopping_basket_hash_code:
                    print(f"[*] shoppingBasketHashCode 从 {self.shopping_basket_hash_code} 更新为 {updated_hash_code} (来自 addItinerary 响应)")
                    self.shopping_basket_hash_code = str(updated_hash_code)
            else: # Should not happen if add_success is True and add_itinerary returns dict on success
                print("[!] 警告: add_itinerary 成功但未返回预期的字典数据。")

            # 步骤 2: 获取购物车状态
            print(f"[*] 添加行程后，准备调用 _get_booking_cart")
            cart_success, cart_msg, cart_err_code = self._get_booking_cart()
            if not cart_success:
                print(f"[!] _get_booking_cart (after add_itinerary) 失败: {cart_msg}")
                # 根据业务逻辑，这里可能需要决定是否这是一个致命错误
                # 如果后续步骤依赖 cart_msg 中的特定数据，则可能是致命的
                # 如果只是为了确认状态，可以考虑记录警告并继续，或者根据 cart_err_code 决定
                return False, f"Get cart (after add_itinerary) failed: {cart_msg}", cart_err_code
            print(f"[*] _get_booking_cart (after add_itinerary) 成功.")

            # 步骤 4: 更新乘客信息
            print("[*] 更新乘客信息中...")
            update_success, update_msg, update_err_code = self.update_passengers(passengers_info_list)
            if not update_success:
                print(f"[!] 更新乘客信息失败: {update_msg}")
                return False, f"Update passengers failed: {update_msg}", update_err_code

            # 步骤 5: 确认支付详情 
            # 金额已由 self.confirmed_award_amount 等属性从传入参数设置
            if self.confirmed_award_amount is None: # 确保金额已有效设置
                err_msg = "book_flight: 确认支付详情前，积分金额 (self.confirmed_award_amount) 未设置。"
                print(f"[!] {err_msg}")
                return False, err_msg, "CONFIRM_AWARD_NOT_SET_PRE_CALL"

            print(f"[*] 调用 _confirm_award_payment_details 确认 {self.confirmed_award_amount} 积分 (预设现金 {self.confirmed_cash_amount} {self.confirmed_cash_currency})...")
            confirm_success, confirm_data_or_msg, confirm_err_code = self._confirm_award_payment_details() # 不再传递参数
            if not confirm_success:
                print(f"[!] _confirm_award_payment_details 失败: {confirm_data_or_msg}")
                return False, f"Confirm payment details failed: {confirm_data_or_msg}", confirm_err_code
            
            # self.confirmed_cash_amount 和 self.confirmed_cash_currency 应该在 _confirm_award_payment_details 内部被实际API响应更新
            # confirm_data_or_msg 在成功时可能是包含更新后金额的字典，或只是一条成功消息。
            # 我们依赖 _confirm_award_payment_details 内部更新 self.confirmed_cash_amount/currency

            print(f"[*] 航班选择、乘客信息更新、支付详情确认完成。")
            print(f"[*] 最终确认的支付组合: {self.confirmed_award_amount} 积分 + {self.confirmed_cash_amount} {self.confirmed_cash_currency}")

            self.current_passengers_info = passengers_info_list # 保存当前乘客信息以备支付使用
            
            # 准备给 make_payment 的数据 (make_payment 自身会读取 self.confirmed_... 金额)
            # 此处返回的 "data" 可以是一个简单的成功指示或包含一些上下文信息，如果需要的话
            return True, {"message": "Pre-payment steps completed successfully.", "confirmed_payment": {"award": self.confirmed_award_amount, "cash": self.confirmed_cash_amount, "currency": self.confirmed_cash_currency}}, None 

        except Exception as e:
            print(f"[!] book_flight 执行过程中发生意外异常: {e}")
            import traceback
            traceback.print_exc()
            return False, f"Exception during pre-payment steps: {str(e)}", "EXCEPTION"

    def _confirm_award_payment_details(self): # 移除 award_amount_to_confirm 参数
        """
        调用 bookingPaymentDetails mutation 来确认用户选择的积分支付金额。
        更新 self.confirmed_cash_amount 和 self.confirmed_cash_currency。
        返回: (bool: success, str_or_dict: message or data_with_amounts)
        """
        award_amount_to_use_in_gql = self.confirmed_award_amount
        if award_amount_to_use_in_gql is None:
            print("[!] _confirm_award_payment_details: self.confirmed_award_amount 未设置! 这是一个关键错误。")
            return False, "Internal error: Award amount not set for confirmation.", "CONFIRM_AWARD_NOT_SET"

        print(f"[*] --- 开始执行 _confirm_award_payment_details (确认积分支付: {award_amount_to_use_in_gql}) ---")
        # ... (query and variables setup as before) ...
        operation_name = "bookingPaymentDetails"
        query = """
        mutation bookingPaymentDetails($paymentType: String!, $paymentDetailsInput: PaymentDetailsInput) {
          b2cPaymentDetails: bookingPaymentDetails(
            paymentType: $paymentType
            paymentDetailsInput: $paymentDetailsInput
          ) {
            originalResponse
            __typename
          }
        }
        """
        variables = {
            "paymentType": "AWARD",
            "paymentDetailsInput": {
                "awardDetailsSelection": {
                    "fareAmount": {
                        "amount": int(award_amount_to_use_in_gql), # 使用实例属性
                        "currency": "FFCURRENCY"
                    },
                    "ancillaryToggleSelections": [] 
                }
            }
        }
        try:
            print(f"[*] bookingPaymentDetails 请求 variables: {json.dumps(variables, indent=2)}")
            response_data = self._graphql_request(operation_name, variables, query)
            # print(f"[*] bookingPaymentDetails 响应 (部分): {json.dumps(response_data, indent=2, default=str)[:500]}...")

            b2c_payment_details = response_data.get("data", {}).get("b2cPaymentDetails", {})
            original_response = b2c_payment_details.get("originalResponse", {})

            if not original_response:
                err_msg = "确认支付详情失败：响应中缺少 originalResponse。"
                print(f"[!] {err_msg} Response: {json.dumps(response_data)}")
                return False, err_msg

            current_position_index = None
            fare_slider_info = original_response.get("fareSlider")
            if fare_slider_info and "currentPositionIndex" in fare_slider_info:
                current_position_index = fare_slider_info.get("currentPositionIndex")
                print(f"[*] 从 fareSlider 获取到 currentPositionIndex: {current_position_index}")
            # ... (rest of current_position_index and messages check logic as before) ...
            
            confirmed_successfully = False
            if current_position_index == 0:
                print(f"[*] 支付详情 (bookingPaymentDetails for AWARD) 确认成功 (currentPositionIndex is 0)。")
                confirmed_successfully = True
            elif current_position_index is not None:
                # Log warning but consider it a potential success path if amounts can be parsed
                print(f"[*] 注意: 支付组合确认currentPositionIndex: {current_position_index} (期望0)，但将尝试解析金额。")
                confirmed_successfully = True # Tentative success
            else: # currentPositionIndex is None
                messages = original_response.get("messages", [])
                errors_in_messages = [m for m in messages if isinstance(m, dict) and m.get("type", "").upper() == "ERROR"]
                if not errors_in_messages:
                    print(f"[*] 支付详情 (bookingPaymentDetails for AWARD) 可能已成功 (currentPositionIndex 未明确为0，但messages为空)。")
                    confirmed_successfully = True
                else:
                    error_text_from_msg = errors_in_messages[0].get("message", "未知错误")
                    err_msg = f"确认支付详情失败 (messages包含错误): {error_text_from_msg}"
                    print(f"[!] {err_msg}")
                    return False, err_msg
            
            if not confirmed_successfully: # Should not be reached if logic above is complete
                 return False, "Confirmation logic path error."


            # 尝试从确认响应中提取实际的现金部分并更新实例变量
            initial_confirmed_cash = self.confirmed_cash_amount
            initial_confirmed_currency = self.confirmed_cash_currency
            cash_amount_updated_from_response = False
            try:
                price_info = original_response.get("price", {})
                total_price_alternatives = price_info.get("total", {}).get("alternatives", [])
                # ... (logic for parsing alternatives and awardMoneyTicks as before, updating self.confirmed_cash_amount and self.confirmed_cash_currency) ...
                # Ensure cash_amount_updated_from_response is set to True if successful
                
                # Parsing logic from previous good version:
                if total_price_alternatives and isinstance(total_price_alternatives, list) and len(total_price_alternatives) > 0:
                    first_element = total_price_alternatives[0]
                    payment_combination_list = []
                    if isinstance(first_element, list): 
                        if len(first_element) > 0: payment_combination_list = first_element
                    elif isinstance(first_element, dict): 
                        payment_combination_list = total_price_alternatives

                    if payment_combination_list:
                        for item in payment_combination_list:
                            if isinstance(item, dict) and item.get("currency") != "FFCURRENCY":
                                self.confirmed_cash_amount = float(item.get("amount"))
                                self.confirmed_cash_currency = item.get("currency")
                                cash_amount_updated_from_response = True; break
                
                if not cash_amount_updated_from_response and current_position_index == 0:
                    award_money_ticks = fare_slider_info.get("awardMoneyTicks", []) if fare_slider_info else []
                    if award_money_ticks and isinstance(award_money_ticks, list) and len(award_money_ticks) > 0:
                        first_tick = award_money_ticks[0]
                        if isinstance(first_tick, dict):
                            money_amount_info = first_tick.get("moneyAmount")
                            if isinstance(money_amount_info, dict):
                                self.confirmed_cash_amount = float(money_amount_info.get("amount"))
                                self.confirmed_cash_currency = money_amount_info.get("currency")
                                cash_amount_updated_from_response = True
            except Exception as e_parse:
                print(f"[!] 解析 bookingPaymentDetails 响应以更新现金部分时出错: {e_parse}。将使用预设/先前值。")

            if cash_amount_updated_from_response:
                if self.confirmed_cash_amount != initial_confirmed_cash or self.confirmed_cash_currency != initial_confirmed_currency:
                    print(f"[*] 确认的现金部分已从 API 更新为: {self.confirmed_cash_amount} {self.confirmed_cash_currency}")
                else:
                    print(f"[*] API返回的现金部分与预设值相同: {self.confirmed_cash_amount} {self.confirmed_cash_currency}")
            else:
                print(f"[!] 未能从API响应更新现金部分。将使用预设/先前值: {self.confirmed_cash_amount} {self.confirmed_cash_currency}")
            
            return True, {"message": "Payment details confirmed.", "award": self.confirmed_award_amount, "cash": self.confirmed_cash_amount, "currency": self.confirmed_cash_currency}, None

        except Exception as e:
            err_msg = f"_confirm_award_payment_details 执行失败: {e}"
            print(f"[!] {err_msg}")
            import traceback
            traceback.print_exc() # For detailed debugging
            return False, err_msg, "CONFIRM_EXCEPTION"

    def make_payment(self, payment_config_dict: dict, passengers_info_list: list, award_amount_ref: int, cash_amount_ref: float, cash_currency_ref: str="AUD", use_points_for_tax_val: bool=False):
        """
        执行支付操作。
        实际支付金额将使用 self.confirmed_award_amount, self.confirmed_cash_amount, self.confirmed_cash_currency.
        返回: (bool: success, dict_or_str: response_data or error_message, str: error_code or None)
        Error codes: "PAYMENT_PREPROCESSING_ERROR", "NETWORK_ERROR", "PAYMENT_BUSINESS_ERROR", "PAYMENT_EXCEPTION"
        """
        try:
            print("[*] --- 开始执行 make_payment (HAR结构对齐) ---")
            
            actual_award = getattr(self, 'confirmed_award_amount', None) or award_amount_ref
            actual_cash = getattr(self, 'confirmed_cash_amount', None) or cash_amount_ref
            actual_currency = getattr(self, 'confirmed_cash_currency', None) or cash_currency_ref

            if getattr(self, 'confirmed_award_amount', None) is None:
                print("[!] 警告: self.confirmed_award_amount 未有效设置，make_payment 将使用传入的参考值。")
            
            print(f"[*] make_payment 使用的支付金额: {actual_award} FFCURRENCY + {actual_cash} {actual_currency}")

            # ... (payment_objects, billing_data_payload, fraud_net_data_payload, etc. setup as before, using actual_award, actual_cash, actual_currency) ...
            # Ensure passengers_info_list is used for holder_name_fallback and phone_number_val if billing_address is missing details
            
            payment_objects = [{"@type": "AWARD", "amount": {"amount": int(actual_award), "currency": "FFCURRENCY"}, "paymentCode": "AWARD"}]
            if not use_points_for_tax_val and actual_cash > 0.0:
                card_number = payment_config_dict.get("card_number")
                expiry_month_str = payment_config_dict.get("expiry_month")
                expiry_year_str = payment_config_dict.get("expiry_year")
                cvv = payment_config_dict.get("cvv")
                if not (card_number and expiry_month_str and expiry_year_str and cvv):
                    return False, "信用卡信息不完整", "PAYMENT_CARD_INCOMPLETE"
                # ... (card validation and card_code_val determination as before) ...
                holder_name_fallback = f"{passengers_info_list[0].get('first_name', 'Test')} {passengers_info_list[0].get('last_name', 'User')}"
                holder_name = payment_config_dict.get("billing_address", {}).get("holder_name", holder_name_fallback)
                card_code_val = "VI" # Default
                if card_number.startswith("4"): card_code_val = "VI"
                elif card_number.startswith("51") or card_number.startswith("52") or card_number.startswith("53") or card_number.startswith("54") or card_number.startswith("55"): card_code_val = "CA"
                elif card_number.startswith("34") or card_number.startswith("37"): card_code_val = "AX"

                payment_objects.append({
                    "@type": "CREDIT_CARD", "number": card_number, "cvc": cvv, "holderName": holder_name,
                    "expirationDate": f"{int(expiry_year_str)}-{int(expiry_month_str):02d}", "cardCode": card_code_val,
                    "amount": {"amount": float(actual_cash), "currency": actual_currency}, "paymentCode": card_code_val
                })
            
            billing_address_conf = payment_config_dict.get("billing_address", {})
            phone_fallback = passengers_info_list[0].get("phone", "0400000000") if passengers_info_list else "0400000000"
            phone_number_val = billing_address_conf.get("phone_number", phone_fallback)
            # ... (rest of billing_data_payload, fraud_net_data_payload, remarks_and_ssrs_payload setup as before) ...
            billing_data_payload = {
                "street1": billing_address_conf.get("street", "123 Test St"),
                "city": billing_address_conf.get("city", "Sydney"),
                "country": billing_address_conf.get("country_code", "AU"), # Ensure this is country code like AU, not name
                "phone": {"countryCode": str(billing_address_conf.get("phone_country_code", "61")), "number": "".join(filter(str.isdigit, str(phone_number_val)))}
            }
            device_fingerprint_to_use = getattr(self, 'device_fingerprint_id', f"va-placeholder-fingerprint-{int(time.time())}")
            fraud_net_data_payload = {"deviceFingerPrintId": device_fingerprint_to_use}
            remarks_and_ssrs_payload = {"remarks": ["h-dxr/flow/b2c-desktop", "h-dxr/channel/none"]}
            payment_required_gql_var = True if actual_cash > 0.0 else False

            purchase_variables = {
                "payment": payment_objects, "billingData": billing_data_payload,
                "paymentRequired": payment_required_gql_var, "languageForBooking": "en_GB",
                "fraudNetData": fraud_net_data_payload, "remarksAndSSRs": remarks_and_ssrs_payload
            }
            # print(f"[*] BookingPurchase Variables: {json.dumps(purchase_variables, indent=2, default=str)}") # Keep for debugging if needed

            query = """
                mutation BookingPurchase(...) { ... } # as before
            """
            # (Query from previous version)
            query = """
                mutation BookingPurchase(
                    $payment: [JSONObject!]!,
                    $billingData: JSONObject,
                    $paymentRequired: Boolean,
                    $languageForBooking: String,
                    $fraudNetData: JSONObject,
                    $remarksAndSSRs: JSONObject
                ) {
                    bookingPurchase(
                        payment: $payment,
                        billingData: $billingData,
                        paymentRequired: $paymentRequired,
                        languageForBooking: $languageForBooking,
                        fraudNetData: $fraudNetData,
                        remarksAndSSRs: $remarksAndSSRs
                    ) {
                        originalResponse 
                        __typename
                    }
                }
            """
            response_data = self._graphql_request("BookingPurchase", purchase_variables, query)
            # print(f"[*] BookingPurchase 响应 (部分): {json.dumps(response_data, indent=2, default=str)[:500]}...")

            final_purchase_result = response_data.get("data", {}).get("bookingPurchase")
            if final_purchase_result is None:
                error_msg = "bookingPurchase in response data was null."
                # Attempt to get more specific error from GQL errors or extensions
                if response_data.get("errors"): error_msg += f" GQL Errors: {json.dumps(response_data['errors'])}"
                elif response_data.get("extensions", {}).get("errors"): error_msg += f" GQL Ext Errors: {json.dumps(response_data['extensions']['errors'])}"
                print(f"[!] {error_msg}")
                # print(f"[!] Full response for null bookingPurchase: {json.dumps(response_data, indent=2)}")
                return False, error_msg, "PAYMENT_GQL_NULL_PURCHASE"
            
            original_response_content = final_purchase_result.get("originalResponse")
            if original_response_content and isinstance(original_response_content, dict):
                messages = original_response_content.get("messages", [])
                errors_in_response = [m for m in messages if isinstance(m, dict) and m.get("type", "").lower() == "error"]
                if errors_in_response:
                    first_error_text = errors_in_response[0].get('text', 'Unknown business error')
                    error_msg = f"Payment failed (business errors): {first_error_text}"
                    print(f"[!] {error_msg} Full messages: {json.dumps(errors_in_response, indent=2)}")
                    return False, error_msg, "PAYMENT_BUSINESS_ERROR"
                
                # Successful payment if no errors in messages and originalResponse is present
                print("[*] Payment successful (based on originalResponse and no errors in messages).")
                booking_ref = original_response_content.get("bookingReference") # Extract booking reference
                return True, {"purchase_response": response_data, "booking_reference": booking_ref}, None
            
            else: # bookingPurchase not null, but no originalResponse or not a dict
                error_msg = "bookingPurchase response structure unexpected (missing originalResponse or not a dict)."
                print(f"[!] {error_msg} Full response: {json.dumps(response_data, indent=2)}")
                return False, error_msg, "PAYMENT_UNEXPECTED_RESPONSE"

        except ValueError as ve: # For things like card validation
            print(f"[!] Payment pre-processing error: {ve}")
            return False, str(ve), "PAYMENT_PREPROCESSING_ERROR"
        # _graphql_request should raise requests.exceptions.RequestException for network issues
        # For other unexpected exceptions:
        except Exception as e:
            print(f"[!] make_payment 过程中发生意外错误: {e}")
            import traceback
            traceback.print_exc()
            return False, f"Unexpected error during payment: {str(e)}", "PAYMENT_UNHANDLED_EXCEPTION"

    def _get_booking_cart(self):
        """
        发送 getBookingCart 请求以在会话中建立或确认购物车状态。
        返回: (bool: success, str: message_or_error_details)
        """
        operation_name = "getBookingCart"
        variables = {} 
        
        print("[*] 发送 getBookingCart 请求...")
        try:
            response_data = self._graphql_request(
                operation_name=operation_name,
                variables=variables,
                query=GET_BOOKING_CART_QUERY 
            )

            if response_data and response_data.get("data") and response_data["data"].get("getBookingCart"):
                original_response = response_data["data"]["getBookingCart"].get("originalResponse", {})
                messages = original_response.get("messages", [])
                
                if not messages: 
                    print("[+] getBookingCart 请求成功，购物车状态已更新/确认。")
                    return True, "GetBookingCart successful.", None
                else:
                    error_msg = f"getBookingCart 请求返回了消息: {messages}"
                    print(f"[!] {error_msg}")
                    return False, error_msg, "GET_CART_MESSAGES"
            elif response_data and response_data.get("errors"):
                 gql_errors = response_data.get('errors')
                 error_msg = f"getBookingCart 请求失败 (GraphQL errors): {gql_errors}"
                 print(f"[!] {error_msg}")
                 return False, error_msg, "GET_CART_GQL_ERROR"
            else:
                error_msg = "getBookingCart 请求失败: 未知错误或响应格式不正确"
                print(f"[!] {error_msg}")
                return False, error_msg, "GET_CART_UNEXPECTED_RESPONSE"

        except Exception as e:
            error_msg = f"调用 getBookingCart 时发生异常: {e}"
            print(f"[!] {error_msg}")
            import traceback
            traceback.print_exc()
            return False, error_msg, "GET_CART_EXCEPTION"

    def add_itinerary(self, flight_selection_id_val: str):
        """
        将选定的航班添加到行程中。
        参数 flight_selection_id_val: 纯哈希码字符串。
        返回: (bool: success, dict_or_str: response_data or error_message)
        """
        print(f"[DEBUG APIClient] add_itinerary called. execution_id: {self.execution_id}, token: {self.token}") # Added debug log
        operation_name = "bookingAddItinerary"
        # 格式化为 "selectFlights=HASHCODE"
        select_flights_payload_str = f"selectFlights={flight_selection_id_val}"
        variables = {"selectFlights": select_flights_payload_str}
        
        query = """
            query bookingAddItinerary($selectFlights: String!) {
              bookingAddItinerary(selectFlights: $selectFlights) {
                originalResponse
                __typename
              }
            }
        """
        print(f"[*] 发送 bookingAddItinerary 请求, selectFlights: '{select_flights_payload_str}'")
        try:
            response_data = self._graphql_request(operation_name, variables, query)
            
            # 详细检查响应内容
            booking_add_itinerary_data = response_data.get("data", {}).get("bookingAddItinerary")
            if booking_add_itinerary_data and isinstance(booking_add_itinerary_data, dict):
                original_response_itinerary = booking_add_itinerary_data.get("originalResponse", {})
                itinerary_messages = original_response_itinerary.get("messages", [])
                itinerary_errors = [msg for msg in itinerary_messages if isinstance(msg, dict) and msg.get("type", "").upper() == "ERROR"]

                if itinerary_errors:
                    error_details_itinerary = itinerary_errors[0].get("message", "添加行程操作返回了错误消息")
                    print(f"[!] 添加行程业务逻辑错误 (来自messages): {error_details_itinerary}")
                    return False, error_details_itinerary, "ADD_ITIN_BUSINESS_ERROR"
                elif not original_response_itinerary:
                    print("[!] 添加行程失败，响应中缺少 originalResponse。")
                    return False, "Add itinerary failed, missing originalResponse.", "ADD_ITIN_NO_ORIGINAL_RESPONSE"
                
                print("[*] 添加行程成功 (基于messages检查)。")
                # 返回原始的 bookingAddItinerary 数据部分，因为 book_flight 可能需要用它更新 shoppingBasketHashCode
                return True, booking_add_itinerary_data, None 
            
            elif response_data.get("errors"):
                gql_errors = response_data.get("errors")
                error_msg = f"bookingAddItinerary GQL errors: {gql_errors}"
                print(f"[!] {error_msg}")
                return False, error_msg, "ADD_ITIN_GQL_ERROR"
            else:
                error_msg = f"'bookingAddItinerary' in response data was null or not a dict. Response: {json.dumps(response_data)}"
                print(f"[!] {error_msg}")
                return False, error_msg, "ADD_ITIN_UNEXPECTED_RESPONSE"

        except Exception as e:
            error_msg = f"调用 bookingAddItinerary 时发生异常: {e}"
            print(f"[!] {error_msg}")
            return False, error_msg, "ADD_ITIN_EXCEPTION"
            
    def update_passengers(self, passengers_info_list: list):
        """
        更新乘客信息。
        返回: (bool: success, str: message_or_error_details)
        """
        # (原有乘客信息构建和验证逻辑...)
        if not isinstance(passengers_info_list, list):
            passengers_info_list = [passengers_info_list] # Ensure it's a list

        required_keys = ["email", "phone", "first_name", "last_name", "title", "gender", "dob"]
        for i, p_info in enumerate(passengers_info_list):
            if not all(key in p_info for key in required_keys):
                missing = [key for key in required_keys if key not in p_info]
                err_msg = f"乘客 {i+1} 信息缺失以下字段: {', '.join(missing)}"
                print(f"[!] {err_msg}")
                return False, err_msg, "UPDATE_PAX_MISSING_FIELDS"
        
        contact_email = passengers_info_list[0].get("email")
        contact_phone = passengers_info_list[0].get("phone")
        passengers_gql_list = []
        for i, p_info in enumerate(passengers_info_list):
            passengers_gql_list.append({
                "passengerIndex": i + 1,
                "passengerDetails": {"firstName": p_info.get("first_name"), "lastName": p_info.get("last_name"), "prefix": p_info.get("title")},
                "passengerInfo": {"gender": p_info.get("gender"), "dateOfBirth": p_info.get("dob"), "type": "ADT", "emails": [contact_email], "phones": [{"type": "MOBILE", "number": contact_phone}]},
                "preferences": {"specialPreferences": {"specialRequests": []}, "frequentFlyer": []}
            })
        variables = {
            "contact": {"emails": [contact_email], "phones": [{"type": "MOBILE", "number": contact_phone}]},
            "passengers": passengers_gql_list
        }
        query = """
        mutation bookingUpdatePassengers($contact: ContactDetailsInput, $passengers: [PassengerInput]) {
          bookingUpdatePassengers(contact: $contact, passengers: $passengers) {
            originalResponse
            __typename
          }
        }
        """
        operation_name = "bookingUpdatePassengers"
        print(f"[*] 发送 {operation_name} 请求...")
        try:
            response_data = self._graphql_request(operation_name, variables, query)
            # 详细检查响应内容
            update_pass_data = response_data.get("data", {}).get("bookingUpdatePassengers")
            if update_pass_data and isinstance(update_pass_data, dict):
                original_response_update = update_pass_data.get("originalResponse", {})
                update_messages = original_response_update.get("messages", [])
                update_errors = [msg for msg in update_messages if isinstance(msg, dict) and msg.get("type", "").upper() == "ERROR"]

                if update_errors:
                    error_details_update = update_errors[0].get("message", "更新乘客信息操作返回了错误消息")
                    print(f"[!] 更新乘客信息业务逻辑错误 (来自messages): {error_details_update}")
                    return False, error_details_update, "UPDATE_PAX_BUSINESS_ERROR"
                elif not original_response_update:
                    print("[!] 更新乘客信息失败，响应中缺少 originalResponse。")
                    return False, "Update passengers failed, missing originalResponse.", "UPDATE_PAX_NO_ORIGINAL_RESPONSE"
                
                print("[*] 更新乘客信息成功 (基于messages检查)。")
                return True, "Update passengers successful.", None
            
            elif response_data.get("errors"):
                error_msg = f"update_passengers GQL errors: {response_data.get('errors')}"
                print(f"[!] {error_msg}")
                return False, error_msg, "UPDATE_PAX_GQL_ERROR"
            else:
                error_msg = f"update_passengers: 'bookingUpdatePassengers' was null or not dict. Response: {json.dumps(response_data)}"
                print(f"[!] {error_msg}")
                return False, error_msg, "UPDATE_PAX_UNEXPECTED_RESPONSE"
                
        except Exception as e:
            error_msg = f"调用 bookingUpdatePassengers 时发生异常: {e}"
            print(f"[!] {error_msg}")
            return False, error_msg, "UPDATE_PAX_EXCEPTION"
