# Virgin Australia API Recorder

这是一个Firefox扩展，专门用于记录Virgin Australia官网的关键API请求，帮助分析预订流程。

## 功能特点

- **选择性记录**：只记录关键的GraphQL API请求，过滤掉无关请求
- **详细信息捕获**：记录请求方法、URL、请求头、请求体和响应
- **参数分析**：分析并突出显示关键参数，如何传递以及它们之间的关系
- **流程可视化**：以时间线形式展示完整的预订流程
- **导出功能**：将记录导出为JSON格式，方便后续分析

## 安装方法

### 临时安装（开发模式）

1. 打开Firefox浏览器
2. 在地址栏输入 `about:debugging`
3. 点击"此Firefox"
4. 点击"临时载入附加组件"
5. 选择扩展目录中的`manifest.json`文件

### 正式安装

1. 打开Firefox浏览器
2. 在地址栏输入 `about:addons`
3. 点击齿轮图标，选择"从文件安装附加组件"
4. 选择打包好的`.xpi`文件

## 使用方法

1. **开始记录**：
   - 点击扩展图标打开弹出窗口
   - 点击"开始记录"按钮
   - 扩展图标上会显示"REC"标志，表示正在记录

2. **执行预订流程**：
   - 在Virgin Australia官网上执行您想要分析的操作
   - 例如：搜索航班、选择航班、添加乘客信息、支付等

3. **查看记录**：
   - 再次点击扩展图标
   - 在弹出窗口中可以看到记录的API请求列表
   - 点击任意请求可以查看详细信息

4. **停止记录**：
   - 点击"停止记录"按钮结束记录
   - 记录会保存在扩展的存储中

5. **导出记录**：
   - 点击"导出记录"按钮
   - 记录会以JSON格式保存到您的计算机上

## 记录的关键API

扩展会记录以下关键API操作：

- `bookingAirSearch` - 搜索航班
- `getBookingCart` - 获取购物车状态
- `bookingAddItinerary` - 添加行程到购物车
- `bookingUpdatePassengers` - 更新乘客信息
- `bookingPaymentDetails` - 支付详情
- `bookingPurchase` - 完成购买
- `fetchBookingCombinableFop` - 获取可用支付方式
- `signIn` - 登录
- `dcmcInit` - 初始化

## 导出数据格式

导出的JSON文件包含以下信息：

```json
{
  "timestamp": "2025-05-18T03:45:12.345Z",
  "records": [
    {
      "id": "request-123",
      "timestamp": "2025-05-18T03:40:12.345Z",
      "url": "https://book.virginaustralia.com/api/graphql",
      "method": "POST",
      "operationName": "bookingAirSearch",
      "variables": { ... },
      "query": "...",
      "headers": { ... },
      "cookies": { ... },
      "statusCode": 200,
      "statusLine": "HTTP/1.1 200 OK"
    },
    // 更多记录...
  ],
  "summary": {
    "totalRequests": 10,
    "operationCounts": {
      "bookingAirSearch": 3,
      "getBookingCart": 2,
      // ...
    },
    "successRates": {
      "bookingAirSearch": 100,
      "getBookingCart": 50,
      // ...
    },
    "flowSequence": [
      {
        "operation": "bookingAirSearch",
        "timestamp": "2025-05-18T03:40:12.345Z"
      },
      // ...
    ]
  }
}
```

## 注意事项

- 扩展只能记录请求头和请求体，无法直接记录响应体（Firefox扩展限制）
- 扩展只在Virgin Australia官网域名下工作
- 记录会保存在浏览器的本地存储中，关闭浏览器后仍然保留
- 清除浏览器数据可能会导致记录丢失

## 隐私说明

- 所有数据仅存储在您的本地浏览器中
- 扩展不会向任何服务器发送数据
- 导出的数据文件包含敏感信息，请妥善保管
