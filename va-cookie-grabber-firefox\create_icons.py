import base64
import os

# 创建一个简单的红色图标，带有 "VA" 文字
def create_icon(size, output_path):
    from PIL import Image, ImageDraw, ImageFont
    
    # 创建一个红色背景的图像
    img = Image.new('RGBA', (size, size), color=(228, 0, 0, 255))
    draw = ImageDraw.Draw(img)
    
    # 尝试加载字体，如果失败则使用默认字体
    try:
        font_size = int(size * 0.6)
        font = ImageFont.truetype("arial.ttf", font_size)
    except IOError:
        font = ImageFont.load_default()
    
    # 在图像中央绘制 "VA" 文字
    text = "VA"
    text_width, text_height = draw.textsize(text, font=font)
    position = ((size - text_width) // 2, (size - text_height) // 2)
    draw.text(position, text, fill=(255, 255, 255, 255), font=font)
    
    # 保存图像
    img.save(output_path)
    print(f"Created icon: {output_path}")

def main():
    # 确保 images 目录存在
    if not os.path.exists("images"):
        os.makedirs("images")
    
    # 创建不同尺寸的图标
    sizes = [16, 48, 128]
    for size in sizes:
        output_path = f"images/icon{size}.png"
        create_icon(size, output_path)

if __name__ == "__main__":
    main()
