# -*-coding: Utf-8 -*-
import re
import sys
import json
import os # Added import
import time
import random
from ctypes import *
from loguru import logger
# --- BEGIN Loguru Reconfiguration ---
try:
    logger.remove() # Attempt to remove default handlers
    # Output to a file instead of sys.stderr
    log_file_path = os.path.join(os.path.dirname(__file__), "bestV8_demo.log") 
    logger.add(log_file_path, rotation="10 MB", enqueue=True, level="DEBUG", format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>")
    # logger.add(sys.stderr, enqueue=True, level="DEBUG", format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>")
except ValueError: # Handles case where no handlers were configured initially to remove
    pass # Or logger.add(...) if you want to ensure one is added regardless
# --- END Loguru Reconfiguration ---
from urllib.parse import urlparse
from curl_cffi import requests
from contextlib import contextmanager # Added import


session = requests.Session(impersonate="chrome101")


@contextmanager
def suppress_stdout_stderr():
    """A context manager that redirects stdout and stderr to devnull"""
    original_stdout_fd = -1
    original_stderr_fd = -1
    saved_stdout_fd = -1
    saved_stderr_fd = -1
    devnull_fd = -1

    try:
        original_stdout_fd = sys.stdout.fileno()
        original_stderr_fd = sys.stderr.fileno()

        saved_stdout_fd = os.dup(original_stdout_fd)
        saved_stderr_fd = os.dup(original_stderr_fd)

        devnull_fd = os.open(os.devnull, os.O_RDWR)

        os.dup2(devnull_fd, original_stdout_fd)
        os.dup2(devnull_fd, original_stderr_fd)
        
        yield
    except Exception: # Broad exception to ensure cleanup if setup fails
        pass # Or log to a file if necessary, but avoid printing to console
    finally:
        # Restore stdout and stderr
        if saved_stdout_fd != -1 and original_stdout_fd != -1:
            try:
                os.dup2(saved_stdout_fd, original_stdout_fd)
            except OSError: pass # Ignore errors during restoration if FDs are already bad
        if saved_stderr_fd != -1 and original_stderr_fd != -1:
            try:
                os.dup2(saved_stderr_fd, original_stderr_fd)
            except OSError: pass # Ignore errors during restoration

        # Close the saved file descriptors and devnull
        if saved_stdout_fd != -1:
            try: os.close(saved_stdout_fd)
            except OSError: pass
        if saved_stderr_fd != -1:
            try: os.close(saved_stderr_fd)
            except OSError: pass
        if devnull_fd != -1:
            try: os.close(devnull_fd)
            except OSError: pass


class Reese84Resolve:
    def __init__(self, src):
        self.src = src
        # Construct path relative to the script file
        script_dir = os.path.dirname(os.path.abspath(__file__)) # Use absolute path for robustness
        js_file_path = os.path.join(script_dir, 'reese84.js')
        try:
            with open(js_file_path, 'r', encoding='utf-8') as f:
                self.code = f.read()
        except FileNotFoundError:
            logger.error(f"Reese84Resolve: reese84.js not found at {js_file_path}")
            self.code = None
        self.parsed_url = urlparse(self.src)
        self.aih = "P3zzFvDlRg/eIxIRb+nwJEpB63XRusRCTISFW2aZ2Ds="


    def btv8(self, code):
        # Construct path relative to the script file
        script_dir = os.path.dirname(os.path.abspath(__file__))
        lib_path = None # Initialize
        if sys.platform == "darwin":
            # Assuming the dylib is in the same directory as the script
            lib_path = os.path.join(script_dir, "bestV8_mac_m.dylib") # Corrected path
        elif sys.platform == "linux":
            # Assuming the .so is in the same directory as the script
            lib_path = os.path.join(script_dir, "bestV8_x64.so") # Corrected path
        elif sys.platform == "win32":
            # Assuming the .dll is in the same directory as the script
            lib_path = os.path.join(script_dir, "bestV8_win64.dll") # Corrected path
        else:
            raise Exception(f"Unknown system platform for bestV8: {sys.platform}")

        if not lib_path or not os.path.exists(lib_path):
            logger.error(f"V8 library not found at expected path: {lib_path}")
            raise FileNotFoundError(f"V8 library not found: {lib_path}")

        try:
            v8 = cdll.LoadLibrary(lib_path)
        except OSError as e_dll:
            logger.error(f"Failed to load V8 library from {lib_path}: {e_dll}")
            raise

        ret = create_string_buffer(250000) # Increased buffer size
        v8.runJs.argtypes = [c_char_p, c_char_p] # Use list for argtypes
        v8.runJs.restype = None 
        
        with suppress_stdout_stderr():
            v8.runJs(code.encode('utf8'), ret)
        rtn = ret.value.rstrip(b"\x00").decode(encoding='utf-8', errors='ignore')
        return rtn

    def resolve(self, proxy_to_use=None):
        if not self.code:
            logger.error("Reese84Resolve.resolve: reese84.js code not loaded. Cannot generate token.")
            return {'status': False, 'data': 'reese84.js not loaded', 'ua': ''}
        if not self.aih:
            logger.error("Reese84Resolve.resolve: AIH not set in Reese84Resolve")
            return {'status': False, 'data': 'AIH not set in Reese84Resolve', 'ua': ''}

        domain = self.parsed_url.netloc
        base_url = self.parsed_url.scheme + "://" + domain
        js_execution_code = self.code + f'\r\nget_reese84("{self.src}", "{self.aih}");'
        
        logger.debug(f"Reese84Resolve: Attempting to generate reese84 for src: {self.src} (AIH: {self.aih[:10]}...)")
        start_time = time.perf_counter()
        try:
            ctx_json_str = self.btv8(js_execution_code)
        except Exception as e_v8:
            logger.error(f"Reese84Resolve: Error during btv8 execution (JS to C call): {e_v8}")
            return {'status': False, 'data': f'V8 execution error: {e_v8}', 'ua': ''}
        end_time = time.perf_counter()
        logger.debug(f"Reese84Resolve: JS execution for reese84 params took {end_time - start_time:.6f} s")

        if not ctx_json_str or ctx_json_str.strip() == "":
            logger.error("Reese84Resolve: btv8 returned empty or whitespace string.")
            return {'status': False, 'data': 'V8 execution returned empty result', 'ua': ''}
        try:
            ctx = json.loads(ctx_json_str)
        except json.JSONDecodeError as e_json:
            logger.error(f"Reese84Resolve: Failed to parse JSON from btv8 output. Output: {ctx_json_str[:200]}... Error: {e_json}")
            return {'status': False, 'data': f'JSON parse error from V8: {e_json}', 'ua': ''}

        if not ctx.get('ua') or not ctx.get('p'):
            logger.error(f"Reese84Resolve: btv8 output missing essential fields (ua or p). Output: {ctx}")
            return {'status': False, 'data': 'V8 output missing essential fields', 'ua': ctx.get('ua', '')}

        headers = {
            'accept': 'application/json; charset=utf-8',
            'content-type': 'text/plain; charset=utf-8',
            'user-agent': ctx['ua'],
            'referer': base_url,
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
        }
        params = {'d': domain}
        data_payload = {
            "solution": {
                "interrogation": {"p": ctx['p'], "st": ctx['st'], "sr": ctx['sr'], "cr": ctx['cr'], "og": ctx.get("og", 1)},
                "version": "beta"
            },
            "old_token": None,
            "error": None,
            "performance": {"interrogation": random.randint(200, 900)}
        }
        json_data_to_send = json.dumps(data_payload, separators=(',', ':'))

        original_proxies = session.proxies
        current_proxy_for_log = "None"
        if proxy_to_use and isinstance(proxy_to_use, dict):
            session.proxies = proxy_to_use
            current_proxy_for_log = proxy_to_use.get('http', proxy_to_use.get('https', 'SpecificProxyInvalidFormat'))
            # logger.debug(f"Reese84Resolve: Using proxy for Akamai POST: {current_proxy_for_log}")
        else:
            session.proxies = {}
            # logger.debug(f"Reese84Resolve: No specific proxy for Akamai POST. Using session's default proxy: {original_proxies.get('http') if original_proxies else 'None'}")
            current_proxy_for_log = original_proxies.get('http') if original_proxies else 'None'
        
        status = False; response_data = None; response_text = ""
        try:
            logger.debug(f"Reese84Resolve: POSTing to Akamai URL: {self.src} with params: {params}")
            resp = session.post(url=self.src, params=params, headers=headers, data=json_data_to_send, timeout=25)
            response_text = resp.text 
            if resp.status_code == 200:
                status = True
                try: response_data = resp.json(); logger.debug(f"Reese84Resolve: Akamai POST successful. Response: {response_data}")
                except json.JSONDecodeError as e_r_json: status=False; response_data=f"Akamai response not valid JSON. Text: {response_text[:100]}... Error: {e_r_json}"; logger.error(f"Reese84Resolve: {response_data}")
            else:
                response_data = f"Akamai POST failed. Status: {resp.status_code}. Response: {response_text[:200]}..."
                logger.error(f"Reese84Resolve: {response_data}")
        except requests.errors.RequestsError as e_req: 
            err_code_match = re.search(r"ErrCode: (\d+)", str(e_req))
            err_code = err_code_match.group(1) if err_code_match else "N/A"
            detailed_error = str(e_req)
            logger.error(f'Reese84Resolve: Akamai POST RequestException. ErrCode: {err_code}, Proxy: {current_proxy_for_log}, Details: {detailed_error}')
            response_data = f'AkamaiPOST ErrCode:{err_code} {detailed_error}'
        except Exception as e_gen:
            logger.error(f'Reese84Resolve: Generic error during Akamai POST: {e_gen}. Proxy used: {current_proxy_for_log}')
            response_data = f'Generic error during Akamai POST: {e_gen}'
        finally:
            session.proxies = original_proxies 
            # logger.debug(f"Reese84Resolve: Restored original proxies for demo.session: {original_proxies.get('http') if original_proxies else 'None'}")

        return {'status': status, 'data': response_data, 'ua': ctx.get('ua', '')}



def main():
    base_url = 'https://book.virginaustralia.com/side-you-ares-may-Exit-sition-Alaruern-Naugmen-G?d=book.virginaustralia.com'
    resolver = Reese84Resolve(base_url)
    get_ck_reese84 = resolver.resolve()
    reese84_resolve_result = get_ck_reese84['status']
    if reese84_resolve_result:
        reese84 = get_ck_reese84["data"]["token"]
        return reese84
    else:
        logger.error('获取 reese84 失败')


if __name__ == '__main__':
    reese84 = main()

    cookies = {
        'reese84': reese84
    }


    headers = {
        "accept": "*/*",
        "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
        "adrum": "isAjax:true",
        "cache-control": "no-cache",
        "content-type": "application/json",
        "dc-url;": "",
        "origin": "https://book.virginaustralia.com",
        "pragma": "no-cache",
        "priority": "u=1, i",
        "referer": "https://book.virginaustralia.com/dx/VADX/",
        "sec-ch-ua": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "ssgtoken": "undefined",
        "ssotoken": "undefined",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36",
        "x-sabre-storefront": "VADX"
    }

    url = "https://book.virginaustralia.com/api/graphql"
    data = {
        "operationName": "bookingAirSearch",
        "variables": {
            "airSearchInput": {
                "cabinClass": "First",
                "awardBooking": True,
                "promoCodes": [],
                "searchType": "BRANDED",
                "itineraryParts": [
                    {
                        "from": {
                            "useNearbyLocations": False,
                            "code": "HND"
                        },
                        "to": {
                            "useNearbyLocations": False,
                            "code": "SEA"
                        },
                        "when": {
                            "date": "2025-06-04"
                        }
                    }
                ],
                "passengers": {
                    "ADT": 1
                }
            }
        },
        "extensions": {},
        "query": "query bookingAirSearch($airSearchInput: CustomAirSearchInput) {\n  bookingAirSearch(airSearchInput: $airSearchInput) {\n    originalResponse\n    __typename\n  }\n}"
    }
    data = json.dumps(data, separators=(',', ':'))
    response = session.post(url, headers=headers, cookies=cookies, data=data)

    logger.success(response)
    logger.success(response.text)
