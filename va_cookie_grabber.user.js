// ==UserScript==
// @name         Virgin Australia Cookie Grabber Plus
// @namespace    http://tampermonkey.net/
// @version      1.3
// @description  获取Virgin Australia网站的cookies、token、execution ID和设备指纹ID，并自动延长会话（必须登录后使用，token对出票至关重要）
// <AUTHOR>
// @match        https://book.virginaustralia.com/dx/VADX/*
// @match        https://www.virginaustralia.com/*
// @match        https://experience.virginaustralia.com/*
// @match        https://book.virginaustralia.com/*
// @grant        GM_setClipboard
// @grant        GM_registerMenuCommand
// @grant        GM_notification
// @grant        GM_setValue
// @grant        GM_getValue
// ==/UserScript==

(function() {
    'use strict';

    // 创建一个浮动按钮
    function createFloatingButton() {
        const button = document.createElement('button');
        button.textContent = '获取Cookie和Token';
        button.style.position = 'fixed';
        button.style.top = '10px';
        button.style.right = '10px';
        button.style.zIndex = '9999';
        button.style.padding = '10px';
        button.style.backgroundColor = '#e40000'; // VA红色
        button.style.color = 'white';
        button.style.border = 'none';
        button.style.borderRadius = '5px';
        button.style.cursor = 'pointer';
        button.style.fontWeight = 'bold';
        button.style.boxShadow = '0 2px 5px rgba(0,0,0,0.3)';

        button.addEventListener('click', grabCookiesAndToken);

        document.body.appendChild(button);

        // 创建状态指示器
        const statusIndicator = document.createElement('div');
        statusIndicator.id = 'va-session-status';
        statusIndicator.style.position = 'fixed';
        statusIndicator.style.bottom = '10px';
        statusIndicator.style.right = '10px';
        statusIndicator.style.zIndex = '9999';
        statusIndicator.style.padding = '5px 10px';
        statusIndicator.style.backgroundColor = '#4CAF50'; // 绿色
        statusIndicator.style.color = 'white';
        statusIndicator.style.border = 'none';
        statusIndicator.style.borderRadius = '3px';
        statusIndicator.style.fontSize = '12px';
        statusIndicator.style.fontWeight = 'bold';
        statusIndicator.style.boxShadow = '0 2px 5px rgba(0,0,0,0.3)';
        statusIndicator.textContent = '会话监控已启动';

        document.body.appendChild(statusIndicator);
    }

    // 获取设备指纹ID
    function getDeviceFingerprint() {
        // 尝试从各种可能的来源获取设备指纹ID
        let deviceFingerprint = null;

        // 1. 尝试从localStorage获取
        try {
            // 常见的指纹存储键名
            const possibleKeys = [
                'deviceFingerprint',
                'deviceFingerprintId',
                'dfp',
                'dfpId',
                'fingerprint',
                'fpid',
                'deviceId'
            ];

            for (const key of possibleKeys) {
                const value = localStorage.getItem(key);
                if (value && value.length > 8) {
                    deviceFingerprint = value;
                    console.log(`[VA Cookie Grabber] 从localStorage[${key}]获取到设备指纹: ${value}`);
                    break;
                }
            }
        } catch (e) {
            console.error('[VA Cookie Grabber] 从localStorage获取设备指纹失败:', e);
        }

        // 2. 尝试从sessionStorage获取
        if (!deviceFingerprint) {
            try {
                const possibleKeys = [
                    'deviceFingerprint',
                    'deviceFingerprintId',
                    'dfp',
                    'dfpId',
                    'fingerprint',
                    'fpid',
                    'deviceId'
                ];

                for (const key of possibleKeys) {
                    const value = sessionStorage.getItem(key);
                    if (value && value.length > 8) {
                        deviceFingerprint = value;
                        console.log(`[VA Cookie Grabber] 从sessionStorage[${key}]获取到设备指纹: ${value}`);
                        break;
                    }
                }
            } catch (e) {
                console.error('[VA Cookie Grabber] 从sessionStorage获取设备指纹失败:', e);
            }
        }

        // 3. 尝试从全局变量获取
        if (!deviceFingerprint && window.deviceFingerprint) {
            deviceFingerprint = window.deviceFingerprint;
            console.log(`[VA Cookie Grabber] 从全局变量获取到设备指纹: ${deviceFingerprint}`);
        }

        // 4. 尝试从页面元素获取
        if (!deviceFingerprint) {
            // 查找可能包含设备指纹的隐藏输入字段
            const hiddenInputs = document.querySelectorAll('input[type="hidden"]');
            for (const input of hiddenInputs) {
                if (input.name && (
                    input.name.includes('fingerprint') ||
                    input.name.includes('device') ||
                    input.name.includes('dfp')
                )) {
                    deviceFingerprint = input.value;
                    console.log(`[VA Cookie Grabber] 从隐藏输入字段获取到设备指纹: ${deviceFingerprint}`);
                    break;
                }
            }
        }

        // 5. 如果还是没有找到，生成一个随机ID
        if (!deviceFingerprint) {
            deviceFingerprint = `va${Date.now()}${Math.floor(Math.random() * 10000)}`;
            console.log(`[VA Cookie Grabber] 未找到设备指纹，生成随机ID: ${deviceFingerprint}`);
        }

        return deviceFingerprint;
    }

    // 获取浏览器信息
    function getBrowserInfo() {
        return {
            userAgent: navigator.userAgent,
            language: navigator.language,
            platform: navigator.platform,
            screenWidth: window.screen.width,
            screenHeight: window.screen.height,
            screenColorDepth: window.screen.colorDepth,
            timeZoneOffset: new Date().getTimezoneOffset(),
            javaEnabled: navigator.javaEnabled ? navigator.javaEnabled() : false,
            cookieEnabled: navigator.cookieEnabled
        };
    }

    // 检测并处理会话延长弹窗
    function handleSessionExtension() {
        // 检测"Yes, extend"按钮
        const extendButtons = Array.from(document.querySelectorAll('button, a')).filter(el =>
            el.textContent && el.textContent.trim().toLowerCase().includes('yes, extend')
        );

        if (extendButtons.length > 0) {
            console.log('检测到会话延长弹窗，自动点击"Yes, extend"按钮');

            // 更新状态指示器
            const statusIndicator = document.getElementById('va-session-status');
            if (statusIndicator) {
                statusIndicator.style.backgroundColor = '#FF9800'; // 橙色
                statusIndicator.textContent = '正在延长会话...';

                // 2秒后恢复绿色
                setTimeout(() => {
                    statusIndicator.style.backgroundColor = '#4CAF50'; // 绿色
                    statusIndicator.textContent = '会话已延长';

                    // 再过5秒恢复正常状态
                    setTimeout(() => {
                        statusIndicator.textContent = '会话监控已启动';
                    }, 5000);
                }, 2000);
            }

            // 点击延长按钮
            extendButtons[0].click();

            // 记录最后一次延长会话的时间
            GM_setValue('last-session-extension', Date.now());

            // 延长会话后，尝试重新获取token和cookies
            setTimeout(grabCookiesAndToken, 3000); // 延迟3秒后重新获取
        }
    }

    // 获取cookies、token和execution ID
    function grabCookiesAndToken() {
        try {
            // 获取所有cookies
            const cookies = document.cookie.split(';').map(cookie => {
                const [name, ...value] = cookie.trim().split('=');
                return {
                    name: name,
                    value: value.join('='),
                    domain: '.virginaustralia.com',
                    path: '/'
                };
            });

            // 获取token (ibeopentoken 或 ssotoken)
            let token = null;

            // 根据分析报告，ibeopentoken是主要认证token，优先查找
            console.log('正在查找token...');

            // 1. 首先从cookie中查找
            for (const cookie of cookies) {
                if (cookie.name.toLowerCase() === 'ibeopentoken') {
                    token = cookie.value;
                    console.log('找到ibeopentoken:', token);
                    break;
                } else if (cookie.name.toLowerCase() === 'ssotoken') {
                    token = cookie.value;
                    console.log('找到ssotoken:', token);
                    break;
                } else if (cookie.name.toLowerCase() === 'ssgtoken') {
                    token = cookie.value;
                    console.log('找到ssgtoken:', token);
                    break;
                }
            }

            // 2. 尝试从页面元素中查找ibeopentoken
            if (!token) {
                try {
                    const tokenElement = document.getElementById('ibeopentoken');
                    if (tokenElement && tokenElement.value) {
                        token = tokenElement.value;
                        console.log('从页面元素#ibeopentoken找到token:', token);
                    }
                } catch (e) {
                    console.log('从页面元素查找token失败:', e);
                }
            }

            // 如果没有找到token，尝试从localStorage获取
            if (!token) {
                try {
                    const localStorageToken = localStorage.getItem('ibeopentoken') ||
                                             localStorage.getItem('ssotoken') ||
                                             localStorage.getItem('ssgtoken');
                    if (localStorageToken) {
                        token = localStorageToken;
                        console.log('从localStorage找到token:', token);
                    }
                } catch (e) {
                    console.log('读取localStorage失败:', e);
                }
            }

            // 如果仍然没有找到token，尝试从页面中提取
            if (!token) {
                try {
                    // 方法1: 检查window对象中是否有token相关的变量
                    if (window.ibeopentoken) {
                        token = window.ibeopentoken;
                        console.log('从window.ibeopentoken找到token:', token);
                    } else if (window.ssotoken) {
                        token = window.ssotoken;
                        console.log('从window.ssotoken找到token:', token);
                    } else if (window.ssgtoken) {
                        token = window.ssgtoken;
                        console.log('从window.ssgtoken找到token:', token);
                    }

                    // 方法2: 尝试从sessionStorage中获取
                    if (!token) {
                        const sessionToken = sessionStorage.getItem('ibeopentoken') ||
                                           sessionStorage.getItem('ssotoken') ||
                                           sessionStorage.getItem('ssgtoken');
                        if (sessionToken) {
                            token = sessionToken;
                            console.log('从sessionStorage找到token:', token);
                        }
                    }

                    // 方法3: 尝试从页面中的script标签中提取
                    if (!token) {
                        const scripts = document.querySelectorAll('script');
                        for (const script of scripts) {
                            const content = script.textContent || '';
                            const tokenMatch = content.match(/(ibeopentoken|ssotoken|ssgtoken|token|auth)[\s]*=[\s]*['"](.*?)['"]/i);
                            if (tokenMatch && tokenMatch[2]) {
                                token = tokenMatch[2];
                                console.log(`从script标签中找到${tokenMatch[1]}:`, token);
                                break;
                            }
                        }
                    }

                    // 方法4: 尝试从网络请求中获取token
                    if (!token) {
                        console.log('尝试监听网络请求以获取token...');
                        // 创建一个提示，告诉用户刷新页面或点击一些链接可能有助于获取token
                        alert('未找到token。请尝试以下操作后再次点击获取按钮：\n\n1. 刷新页面\n2. 点击页面上的"我的账户"或其他链接\n3. 确保您已完全登录到Virgin Australia账户');
                    }

                    // 方法5: 尝试从页面上的隐藏输入字段中获取
                    if (!token) {
                        const hiddenInputs = document.querySelectorAll('input[type="hidden"]');
                        for (const input of hiddenInputs) {
                            if (input.name && (input.name.toLowerCase().includes('token') ||
                                              input.name.toLowerCase().includes('auth'))) {
                                token = input.value;
                                console.log(`从隐藏输入字段${input.name}中找到可能的token:`, token);
                                break;
                            }
                        }
                    }

                    // 方法6: 尝试从meta标签中获取
                    if (!token) {
                        const metaTags = document.querySelectorAll('meta');
                        for (const meta of metaTags) {
                            if (meta.name && (meta.name.toLowerCase().includes('token') ||
                                             meta.name.toLowerCase().includes('auth'))) {
                                token = meta.content;
                                console.log(`从meta标签${meta.name}中找到可能的token:`, token);
                                break;
                            }
                        }
                    }

                    // 方法7: 尝试从data属性中获取
                    if (!token) {
                        const elementsWithDataAttrs = document.querySelectorAll('[data-token], [data-auth], [data-ssotoken]');
                        for (const el of elementsWithDataAttrs) {
                            const dataToken = el.dataset.token || el.dataset.auth || el.dataset.ssotoken;
                            if (dataToken) {
                                token = dataToken;
                                console.log('从data属性中找到可能的token:', token);
                                break;
                            }
                        }
                    }
                } catch (e) {
                    console.log('尝试从页面提取token失败:', e);
                }
            }

            // 如果仍然没有找到token，添加警告信息
            if (!token) {
                console.warn('警告: 未能找到token。出票操作需要有效的token，否则系统无法知道使用哪个账户出票。');
                alert('警告: 未能找到token！\n\n出票操作需要有效的token，否则系统无法知道使用哪个账户出票。\n\n请确保您已完全登录到Virgin Australia账户，并尝试在不同的页面（如账户页面）再次获取。');
            }

            // 获取execution ID
            let executionId = null;
            const url = window.location.href;
            const hashPart = url.split('#')[1] || '';
            const queryParams = new URLSearchParams(hashPart.includes('?') ? hashPart.split('?')[1] : hashPart);
            executionId = queryParams.get('execution');

            // 获取设备指纹ID和浏览器信息
            const deviceFingerprintId = getDeviceFingerprint();
            const browserInfo = getBrowserInfo();

            // 创建结果对象
            const result = {
                cookies: cookies,
                token: token,
                execution_id: executionId,
                device_fingerprint_id: deviceFingerprintId,
                browser_info: browserInfo,
                timestamp: new Date().toISOString(),
                url: window.location.href
            };

            // 将结果转换为JSON并复制到剪贴板
            const resultJson = JSON.stringify(result, null, 2);
            GM_setClipboard(resultJson);

            // 显示通知
            if (token && executionId && deviceFingerprintId) {
                GM_notification({
                    title: '成功获取所有必要数据',
                    text: `已获取${cookies.length}个cookies，包含token、execution ID和设备指纹ID。\n出票所需的全部数据已获取成功！`,
                    timeout: 5000
                });
            } else {
                let missingItems = [];
                if (!token) missingItems.push("token");
                if (!executionId) missingItems.push("execution ID");
                if (!deviceFingerprintId) missingItems.push("设备指纹ID");

                if (missingItems.length === 1) {
                    GM_notification({
                        title: '部分成功 - 缺少' + missingItems[0],
                        text: `已获取${cookies.length}个cookies，但缺少${missingItems[0]}。\n请尝试在其他页面重新获取。`,
                        timeout: 5000
                    });
                } else {
                    GM_notification({
                        title: '部分成功 - 缺少多项数据',
                        text: `已获取${cookies.length}个cookies，但缺少${missingItems.join('、')}。\n请确保已登录并尝试在其他页面重新获取。`,
                        timeout: 8000
                    });
                }
            }

            // 在控制台输出结果
            console.log('Virgin Australia Cookie Grabber Plus 结果:', result);

            // 添加会话监控状态信息
            const lastExtension = GM_getValue('last-session-extension', 0);
            const now = Date.now();
            const timeSinceLastExtension = now - lastExtension;
            const minutesSinceLastExtension = Math.floor(timeSinceLastExtension / (60 * 1000));

            if (lastExtension > 0) {
                console.log(`上次会话延长时间: ${new Date(lastExtension).toLocaleString()}`);
                console.log(`距离上次会话延长已过去: ${minutesSinceLastExtension} 分钟`);
            } else {
                console.log('尚未检测到会话延长操作');
            }

            // 创建一个临时文本区域显示结果
            showResultPopup(resultJson, lastExtension);
        } catch (error) {
            console.error('获取Cookie和Token时出错:', error);
            GM_notification({
                title: '错误',
                text: '获取Cookie和Token时出错: ' + error.message,
                timeout: 5000
            });
        }
    }

    // 显示结果弹窗
    function showResultPopup(resultJson, lastExtension = 0) {
        // 创建弹窗容器
        const popup = document.createElement('div');
        popup.style.position = 'fixed';
        popup.style.top = '50%';
        popup.style.left = '50%';
        popup.style.transform = 'translate(-50%, -50%)';
        popup.style.backgroundColor = 'white';
        popup.style.padding = '20px';
        popup.style.borderRadius = '5px';
        popup.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.5)';
        popup.style.zIndex = '10000';
        popup.style.maxWidth = '80%';
        popup.style.maxHeight = '80%';
        popup.style.overflow = 'auto';

        // 创建标题
        const title = document.createElement('h3');
        title.textContent = 'Cookie和Token数据 (已复制到剪贴板)';
        title.style.marginTop = '0';
        popup.appendChild(title);

        // 添加状态信息
        const statusDiv = document.createElement('div');
        statusDiv.style.marginBottom = '10px';
        statusDiv.style.padding = '8px';
        statusDiv.style.borderRadius = '4px';

        // 检查缺少的数据项
        let missingItems = [];
        if (resultJson.includes('"token":null') || !resultJson.includes('"token"')) {
            missingItems.push("token");
        }
        if (resultJson.includes('"execution_id":null') || !resultJson.includes('"execution_id"')) {
            missingItems.push("execution ID");
        }
        if (resultJson.includes('"device_fingerprint_id":null') || !resultJson.includes('"device_fingerprint_id"')) {
            missingItems.push("设备指纹ID");
        }

        if (missingItems.length > 0) {
            if (missingItems.includes("token")) {
                // token缺失是最严重的问题
                statusDiv.style.backgroundColor = '#ffcccc';
                statusDiv.style.color = '#cc0000';
                statusDiv.innerHTML = '<strong>警告：未找到token！</strong><br>出票操作必须有token，否则系统无法知道使用哪个账户出票。<br>请确保您已完全登录到Virgin Australia账户。';
            } else if (missingItems.includes("execution ID")) {
                // execution ID缺失是次严重的问题
                statusDiv.style.backgroundColor = '#fff3cd';
                statusDiv.style.color = '#856404';
                statusDiv.innerHTML = '<strong>警告：未找到execution ID！</strong><br>API请求需要execution ID才能正常工作。<br>请尝试在其他页面重新获取。';
            } else {
                // 只缺少设备指纹ID
                statusDiv.style.backgroundColor = '#fff3cd';
                statusDiv.style.color = '#856404';
                statusDiv.innerHTML = '<strong>警告：未找到设备指纹ID！</strong><br>已生成随机设备指纹ID，但使用真实设备指纹可能更可靠。';
            }
        } else {
            statusDiv.style.backgroundColor = '#d4edda';
            statusDiv.style.color = '#155724';
            statusDiv.innerHTML = '<strong>成功：已获取所有必要数据！</strong><br>包含token、execution ID和设备指纹ID，可以进行出票操作。';
        }

        popup.appendChild(statusDiv);

        // 添加会话监控信息
        const sessionDiv = document.createElement('div');
        sessionDiv.style.marginBottom = '10px';
        sessionDiv.style.padding = '8px';
        sessionDiv.style.borderRadius = '4px';
        sessionDiv.style.backgroundColor = '#e6f7ff';
        sessionDiv.style.color = '#0066cc';

        sessionDiv.innerHTML = '<strong>会话监控状态：</strong><br>';

        if (lastExtension > 0) {
            const now = Date.now();
            const timeSinceLastExtension = now - lastExtension;
            const minutesSinceLastExtension = Math.floor(timeSinceLastExtension / (60 * 1000));

            sessionDiv.innerHTML += `上次会话延长时间: ${new Date(lastExtension).toLocaleString()}<br>`;
            sessionDiv.innerHTML += `距离上次会话延长已过去: ${minutesSinceLastExtension} 分钟<br>`;
        } else {
            sessionDiv.innerHTML += '会话监控已启动，尚未检测到会话延长操作<br>';
        }

        sessionDiv.innerHTML += '脚本将自动检测并点击"Yes, extend"按钮以保持会话活跃';

        popup.appendChild(sessionDiv);

        // 创建文本区域
        const textarea = document.createElement('textarea');
        textarea.value = resultJson;
        textarea.style.width = '100%';
        textarea.style.height = '300px';
        textarea.style.marginBottom = '10px';
        textarea.style.padding = '5px';
        textarea.style.fontFamily = 'monospace';
        popup.appendChild(textarea);

        // 创建关闭按钮
        const closeButton = document.createElement('button');
        closeButton.textContent = '关闭';
        closeButton.style.padding = '5px 10px';
        closeButton.style.backgroundColor = '#e40000';
        closeButton.style.color = 'white';
        closeButton.style.border = 'none';
        closeButton.style.borderRadius = '3px';
        closeButton.style.cursor = 'pointer';
        closeButton.addEventListener('click', () => {
            document.body.removeChild(popup);
        });
        popup.appendChild(closeButton);

        document.body.appendChild(popup);
    }

    // 添加菜单命令
    GM_registerMenuCommand('获取Cookie和Token', grabCookiesAndToken);

    // 初始化函数
    function init() {
        // 创建浮动按钮和状态指示器
        createFloatingButton();

        // 启动定期检查会话状态
        setInterval(handleSessionExtension, 30000); // 每30秒检查一次

        // 首次检查
        setTimeout(handleSessionExtension, 5000); // 页面加载5秒后进行首次检查

        console.log('Virgin Australia Cookie Grabber Plus 已初始化，会话监控已启动');
    }

    // 页面加载完成后初始化
    if (document.readyState === 'complete') {
        init();
    } else {
        window.addEventListener('load', init);
    }
})();
