window = globalThis;
const rdm = (min, max) => Math.floor(Math.random() * (max - min + 1)) + min;
const env = {};
!function (e) {
  var base64EncodeChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
  var base64DecodeChars = new Array(-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, -1, -1, -1, 63, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, -1, -1, -1, -1, -1, -1, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, -1, -1, -1, -1, -1);
  function base64encode(str) {
    var out, i, len;
    var c1, c2, c3;
    len = str.length;
    i = 0;
    out = "";
    while (i < len) {
      c1 = str.charCodeAt(i++) & 0xff;
      if (i == len) {
        out += base64EncodeChars.charAt(c1 >> 2);
        out += base64EncodeChars.charAt((c1 & 0x3) << 4);
        out += "==";
        break;
      }
      c2 = str.charCodeAt(i++);
      if (i == len) {
        out += base64EncodeChars.charAt(c1 >> 2);
        out += base64EncodeChars.charAt((c1 & 0x3) << 4 | (c2 & 0xF0) >> 4);
        out += base64EncodeChars.charAt((c2 & 0xF) << 2);
        out += "=";
        break;
      }
      c3 = str.charCodeAt(i++);
      out += base64EncodeChars.charAt(c1 >> 2);
      out += base64EncodeChars.charAt((c1 & 0x3) << 4 | (c2 & 0xF0) >> 4);
      out += base64EncodeChars.charAt((c2 & 0xF) << 2 | (c3 & 0xC0) >> 6);
      out += base64EncodeChars.charAt(c3 & 0x3F);
    }
    return out;
  }
  function base64decode(str) {
    var c1, c2, c3, c4;
    var i, len, out;
    len = str.length;
    i = 0;
    out = "";
    while (i < len) {
      do {
        c1 = base64DecodeChars[str.charCodeAt(i++) & 0xff];
      } while (i < len && c1 == -1);
      if (c1 == -1) break;
      do {
        c2 = base64DecodeChars[str.charCodeAt(i++) & 0xff];
      } while (i < len && c2 == -1);
      if (c2 == -1) break;
      out += String.fromCharCode(c1 << 2 | (c2 & 0x30) >> 4);
      do {
        c3 = str.charCodeAt(i++) & 0xff;
        if (c3 == 61) return out;
        c3 = base64DecodeChars[c3];
      } while (i < len && c3 == -1);
      if (c3 == -1) break;
      out += String.fromCharCode((c2 & 0XF) << 4 | (c3 & 0x3C) >> 2);
      do {
        c4 = str.charCodeAt(i++) & 0xff;
        if (c4 == 61) return out;
        c4 = base64DecodeChars[c4];
      } while (i < len && c4 == -1);
      if (c4 == -1) break;
      out += String.fromCharCode((c3 & 0x03) << 6 | c4);
    }
    return out;
  }
  function utf16to8(str) {
    var out, i, len, c;
    out = "";
    len = str.length;
    for (i = 0; i < len; i++) {
      c = str.charCodeAt(i);
      if (c >= 0x0001 && c <= 0x007F) {
        out += str.charAt(i);
      } else if (c > 0x07FF) {
        out += String.fromCharCode(0xE0 | c >> 12 & 0x0F);
        out += String.fromCharCode(0x80 | c >> 6 & 0x3F);
        out += String.fromCharCode(0x80 | c >> 0 & 0x3F);
      } else {
        out += String.fromCharCode(0xC0 | c >> 6 & 0x1F);
        out += String.fromCharCode(0x80 | c >> 0 & 0x3F);
      }
    }
    return out;
  }
  function utf8to16(str) {
    var out, i, len, c;
    var char2, char3;
    out = "";
    len = str.length;
    i = 0;
    while (i < len) {
      c = str.charCodeAt(i++);
      switch (c >> 4) {
        case 0:
        case 1:
        case 2:
        case 3:
        case 4:
        case 5:
        case 6:
        case 7:
          out += str.charAt(i - 1);
          break;
        case 12:
        case 13:
          char2 = str.charCodeAt(i++);
          out += String.fromCharCode((c & 0x1F) << 6 | char2 & 0x3F);
          break;
        case 14:
          char2 = str.charCodeAt(i++);
          char3 = str.charCodeAt(i++);
          out += String.fromCharCode((c & 0x0F) << 12 | (char2 & 0x3F) << 6 | (char3 & 0x3F) << 0);
          break;
      }
    }
    return out;
  }
  function CharToHex(str) {
    var out, i, len, c, h;
    out = "";
    len = str.length;
    i = 0;
    while (i < len) {
      c = str.charCodeAt(i++);
      h = c.toString(16);
      if (h.length < 2) h = "0" + h;
      out += "\\x" + h + " ";
      if (i > 0 && i % 8 == 0) out += "\r\n";
    }
    return out;
  }
  this.atob = base64decode, this.btoa = base64encode;
}(this);
(function () {
  function add(x, y) {
    return (x & 0x7FFFFFFF) + (y & 0x7FFFFFFF) ^ x & 0x80000000 ^ y & 0x80000000;
  }
  ;
  function SHA1hex(num) {
    var sHEXChars = "0123456789abcdef";
    var str = "";
    for (var j = 7; j >= 0; j--) str += sHEXChars.charAt(num >> j * 4 & 0x0F);
    return str;
  }
  ;
  function AlignSHA1(sIn) {
    var nblk = (sIn.length + 8 >> 6) + 1,
      blks = new Array(nblk * 16);
    for (var i = 0; i < nblk * 16; i++) blks[i] = 0;
    for (i = 0; i < sIn.length; i++) blks[i >> 2] |= sIn.charCodeAt(i) << 24 - (i & 3) * 8;
    blks[i >> 2] |= 0x80 << 24 - (i & 3) * 8;
    blks[nblk * 16 - 1] = sIn.length * 8;
    return blks;
  }
  ;
  function rol(num, cnt) {
    return num << cnt | num >>> 32 - cnt;
  }
  ;
  function ft(t, b, c, d) {
    if (t < 20) return b & c | ~b & d;
    if (t < 40) return b ^ c ^ d;
    if (t < 60) return b & c | b & d | c & d;
    return b ^ c ^ d;
  }
  ;
  function kt(t) {
    return t < 20 ? 1518500249 : t < 40 ? 1859775393 : t < 60 ? -1894007588 : -899497514;
  }
  ;
  this.sha1 = function sha1(sIn) {
    var x = AlignSHA1(sIn);
    var w = new Array(80);
    var a = 1732584193;
    var b = -271733879;
    var c = -1732584194;
    var d = 271733878;
    var e = -1009589776;
    for (var i = 0; i < x.length; i += 16) {
      var olda = a;
      var oldb = b;
      var oldc = c;
      var oldd = d;
      var olde = e;
      for (var j = 0; j < 80; j++) {
        if (j < 16) w[j] = x[i + j];else w[j] = rol(w[j - 3] ^ w[j - 8] ^ w[j - 14] ^ w[j - 16], 1);
        t = add(add(rol(a, 5), ft(j, b, c, d)), add(add(e, w[j]), kt(j)));
        e = d;
        d = c;
        c = rol(b, 30);
        b = a;
        a = t;
      }
      a = add(a, olda);
      b = add(b, oldb);
      c = add(c, oldc);
      d = add(d, oldd);
      e = add(e, olde);
    }
    SHA1Value = SHA1hex(a) + SHA1hex(b) + SHA1hex(c) + SHA1hex(d) + SHA1hex(e);
    return SHA1Value.toLowerCase();
  };
})(this);
(function () {
  const window_innerWidth = rdm(753, 985);
  const window_innerHeight = rdm(753, 985);
  const window_outerWidth = window_innerWidth + rdm(8, 16);
  const window_outerHeight = window_innerHeight + rdm(25, 32);
  const window_screenX = rdm(248, 432);
  const window_screenY = rdm(35, 112);
  const navigator_hardwareConcurrency = [4, 8, 12, 16, 24, 32][Math.floor(Math.random() * 6)];
  const navigator_languages = [["zh-CN"], ['zh-CN', 'zh'], ["zh-CN", "en", "en-GB", "en-US"]][Math.floor(Math.random() * 3)];
  const navigator_userAgent = ['Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4842.51 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4845.51 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4845.51 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4845.51 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4845.51 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4845.51 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4845.51 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4845.51 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4845.51 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.54 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.82 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.82 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.182 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Safari/537.36 QIHU 360SE', 'Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Safari/537.36 QIHU 360EE', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.110 Safari/537.36 QIHU 360EE', 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.94 Safari/537.36 QIHU 360EE', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36 SE 2.X MetaSr 1.0', 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/44.0.2403.157 Safari/537.36 360Browser', 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/45.0.2454.85 Safari/537.36 360SE', 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2311.90 Safari/537.36 SE 2.X MetaSr 1.0', 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.65 Safari/537.36 360SE', 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2062.120 Safari/537.36 360SE', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 QBCore/3.70.1111.400 QQBrowser/10.5.3863.400', 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.75 Safari/537.36 QBCore/3.43.547.400 QQBrowser/9.7.12880.400', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.75 Safari/537.36 QBCore/4.0.1304.400 QQBrowser/10.4.3350.400', 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36 QBCore/3.40.1084.400 QQBrowser/9.6.12478.400', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.2564.116 Safari/537.36 QBCore/3.30.1515.400 QQBrowser/9.5.10990.400', 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.87 Safari/537.36 QBCore/3.43.549.400 QQBrowser/9.7.13059.400', 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.2661.102 Safari/537.36 QBCore/2.32.1475.400 QQBrowser/9.4.8388.400', 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.2840.99 Safari/537.36 QBCore/3.37.1823.400 QQBrowser/9.6.12513.400', 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.103 Safari/537.36 QBCore/2.33.1495.400 QQBrowser/9.4.8466.400', 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.106 Safari/537.36 QBCore/2.33.1563.401 QQBrowser/9.4.8656.400'][Math.floor(Math.random() * 52)];
  const navigator_plugins = 'PDF Viewer::Portable Document Format::application/pdf~pdf,text/pdf~pdf;Chrome PDF Viewer::Portable Document Format::application/pdf~pdf,text/pdf~pdf;Chromium PDF Viewer::Portable Document Format::application/pdf~pdf,text/pdf~pdf;Microsoft Edge PDF Viewer::Portable Document Format::application/pdf~pdf,text/pdf~pdf;WebKit built-in PDF::Portable Document Format::application/pdf~pdf,text/pdf~pdf';
  const navigator_mimeTypes = {
    "0": {
      "suffixes": "pdf",
      "type": "application/pdf",
      "enabledPlugin": {
        "filename": "internal-pdf-viewer"
      }
    },
    "1": {
      "suffixes": "pdf",
      "type": "text/pdf",
      "enabledPlugin": {
        "filename": "internal-pdf-viewer"
      }
    },
    "application/pdf": {
      "suffixes": "pdf",
      "type": "application/pdf",
      "enabledPlugin": {
        "filename": "internal-pdf-viewer"
      }
    },
    "text/pdf": {
      "suffixes": "pdf",
      "type": "text/pdf",
      "enabledPlugin": {
        "filename": "internal-pdf-viewer"
      }
    }
  };
  const Navigator_prototype_Attr = [[["vendorSub", ""], ["productSub", ""], ["vendor", ""], ["maxTouchPoints", ""], ["scheduling", ""], ["userActivation", ""], ["doNotTrack", ""], ["geolocation", ""], ["connection", ""], ["plugins", ""], ["mimeTypes", ""], ["pdfViewerEnabled", ""], ["webkitTemporaryStorage", ""], ["webkitPersistentStorage", ""], ["hardwareConcurrency", ""], ["cookieEnabled", ""], ["appCodeName", ""], ["appName", ""], ["appVersion", ""], ["platform", ""], ["product", ""], ["userAgent", ""], ["language", ""], ["languages", ""], ["onLine", ""], ["webdriver", ""], ["getGamepads", ""], ["javaEnabled", ""], ["sendBeacon", ""], ["vibrate", ""], ["bluetooth", ""], ["clipboard", ""], ["credentials", ""], ["keyboard", ""], ["managed", ""], ["mediaDevices", ""], ["storage", ""], ["serviceWorker", ""], ["virtualKeyboard", ""], ["wakeLock", ""], ["deviceMemory", ""], ["cookieDeprecationLabel", ""], ["login", ""], ["ink", ""], ["hid", ""], ["locks", ""], ["gpu", ""], ["mediaCapabilities", ""], ["mediaSession", ""], ["permissions", ""], ["presentation", ""], ["usb", ""], ["xr", ""], ["serial", ""], ["windowControlsOverlay", ""], ["userAgentData", ""], ["canShare", ""], ["share", ""], ["clearAppBadge", ""], ["getBattery", ""], ["getUserMedia", ""], ["requestMIDIAccess", ""], ["requestMediaKeySystemAccess", ""], ["setAppBadge", ""], ["webkitGetUserMedia", ""], ["getInstalledRelatedApps", ""], ["registerProtocolHandler", ""], ["unregisterProtocolHandler", ""]], [["vendorSub", ""], ["productSub", ""], ["vendor", ""], ["maxTouchPoints", ""], ["userActivation", ""], ["doNotTrack", ""], ["geolocation", ""], ["connection", ""], ["plugins", ""], ["mimeTypes", ""], ["webkitTemporaryStorage", ""], ["webkitPersistentStorage", ""], ["hardwareConcurrency", ""], ["cookieEnabled", ""], ["appCodeName", ""], ["appName", ""], ["appVersion", ""], ["platform", ""], ["product", ""], ["userAgent", ""], ["language", ""], ["languages", ""], ["onLine", ""], ["webdriver", ""], ["getBattery", ""], ["getGamepads", ""], ["javaEnabled", ""], ["sendBeacon", ""], ["vibrate", ""], ["clipboard", ""], ["credentials", ""], ["keyboard", ""], ["mediaDevices", ""], ["storage", ""], ["serviceWorker", ""], ["wakeLock", ""], ["deviceMemory", ""], ["hid", ""], ["bluetooth", ""], ["mediaCapabilities", ""], ["userAgentData", ""], ["locks", ""], ["mediaSession", ""], ["presentation", ""], ["managed", ""], ["usb", ""], ["serial", ""], ["scheduling", ""], ["xr", ""], ["permissions", ""], ["canShare", ""], ["share", ""], ["registerProtocolHandler", ""], ["unregisterProtocolHandler", ""], ["getInstalledRelatedApps", ""], ["clearAppBadge", ""], ["setAppBadge", ""], ["getUserMedia", ""], ["requestMIDIAccess", ""], ["requestMediaKeySystemAccess", ""], ["webkitGetUserMedia", ""]], [["vendorSub", ""], ["productSub", ""], ["vendor", ""], ["maxTouchPoints", ""], ["userActivation", ""], ["doNotTrack", ""], ["geolocation", ""], ["connection", ""], ["plugins", ""], ["mimeTypes", ""], ["pdfViewerEnabled", ""], ["webkitTemporaryStorage", ""], ["webkitPersistentStorage", ""], ["hardwareConcurrency", ""], ["cookieEnabled", ""], ["appCodeName", ""], ["appName", ""], ["appVersion", ""], ["platform", ""], ["product", ""], ["userAgent", ""], ["language", ""], ["languages", ""], ["onLine", ""], ["webdriver", ""], ["getBattery", ""], ["getGamepads", ""], ["javaEnabled", ""], ["sendBeacon", ""], ["vibrate", ""], ["scheduling", ""], ["bluetooth", ""], ["clipboard", ""], ["credentials", ""], ["keyboard", ""], ["managed", ""], ["mediaDevices", ""], ["storage", ""], ["serviceWorker", ""], ["wakeLock", ""], ["deviceMemory", ""], ["ink", ""], ["hid", ""], ["locks", ""], ["mediaCapabilities", ""], ["mediaSession", ""], ["permissions", ""], ["presentation", ""], ["serial", ""], ["virtualKeyboard", ""], ["usb", ""], ["xr", ""], ["userAgentData", ""], ["canShare", ""], ["share", ""], ["clearAppBadge", ""], ["setAppBadge", ""], ["getInstalledRelatedApps", ""], ["getUserMedia", ""], ["requestMIDIAccess", ""], ["requestMediaKeySystemAccess", ""], ["webkitGetUserMedia", ""], ["registerProtocolHandler", ""], ["unregisterProtocolHandler", ""]]][Math.floor(Math.random() * 3)];
  Object.defineProperty(env, "screen", {
    value: {
      width: 1920,
      height: 1080,
      availWidth: 1920,
      availHeight: 1032,
      availLeft: 0,
      availTop: 0,
      pixelDepth: 24,
      orientation: {
        angle: 0,
        type: 'landscape-primary',
        onchange: null
      }
    }
  });
  Object.defineProperty(env, "window", {
    value: {
      devicePixelRatio: 1,
      innerWidth: window_innerWidth,
      innerHeight: window_innerHeight,
      outerWidth: window_outerWidth,
      outerHeight: window_outerHeight,
      screenX: window_screenX,
      screenY: window_screenY,
      visualViewport: {
        height: window_innerHeight,
        width: window_innerWidth,
        scale: 1
      }
    }
  });
  Object.defineProperty(env, "navigator", {
    value: {
      appCodeName: "Mozilla",
      product: "Gecko",
      productSub: "20030107",
      vendor: "Google Inc.",
      language: 'zh-CN',
      maxTouchPoints: 0,
      languages: navigator_languages,
      userAgent: navigator_userAgent,
      mimeTypes: navigator_mimeTypes,
      plugins: navigator_plugins,
      NavigatorAttr: Navigator_prototype_Attr,
      hardwareConcurrency: navigator_hardwareConcurrency,
      connection: {
        'rtt': Math.floor(Math.random() * 210) + 50
      }
    }
  });
})();
(function () {
  const public_canvas = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAAAXNSR0IArs4c6QAAAA9JREFUGFdjZEADjKQLAAAA7gAFLaYDxAAAAABJRU5ErkJggg==";
  const canvas_2d = {
    "short": public_canvas,
    "long": sha1(Math.random() + Date.now())
  };
  const canvas_webgl = {
    "short": public_canvas,
    "long": "b882035ae368bd0086e015e8c3cab6344b5d2eb8"
  };
  const webgl_extensions = ["ANGLE_instanced_arrays;EXT_blend_minmax;EXT_color_buffer_half_float;EXT_disjoint_timer_query;EXT_float_blend;EXT_frag_depth;EXT_shader_texture_lod;EXT_texture_compression_bptc;EXT_texture_compression_rgtc;EXT_texture_filter_anisotropic;WEBKIT_EXT_texture_filter_anisotropic;EXT_sRGB;KHR_parallel_shader_compile;OES_element_index_uint;OES_fbo_render_mipmap;OES_standard_derivatives;OES_texture_float;OES_texture_float_linear;OES_texture_half_float;OES_texture_half_float_linear;OES_vertex_array_object;WEBGL_color_buffer_float;WEBGL_compressed_texture_s3tc;WEBKIT_WEBGL_compressed_texture_s3tc;WEBGL_compressed_texture_s3tc_srgb;WEBGL_debug_renderer_info;WEBGL_debug_shaders;WEBGL_depth_texture;WEBKIT_WEBGL_depth_texture;WEBGL_draw_buffers;WEBGL_lose_context;WEBKIT_WEBGL_lose_context;WEBGL_multi_draw", "ANGLE_instanced_arrays;EXT_blend_minmax;EXT_color_buffer_half_float;EXT_disjoint_timer_query;EXT_float_blend;EXT_frag_depth;EXT_shader_texture_lod;EXT_texture_compression_bptc;EXT_texture_compression_rgtc;EXT_texture_filter_anisotropic;EXT_sRGB;KHR_parallel_shader_compile;OES_element_index_uint;OES_fbo_render_mipmap;OES_standard_derivatives;OES_texture_float;OES_texture_float_linear;OES_texture_half_float;OES_texture_half_float_linear;OES_vertex_array_object;WEBGL_color_buffer_float;WEBGL_compressed_texture_s3tc;WEBGL_compressed_texture_s3tc_srgb;WEBGL_debug_renderer_info;WEBGL_debug_shaders;WEBGL_depth_texture;WEBGL_draw_buffers;WEBGL_lose_context;WEBGL_multi_draw"][Math.floor(Math.random() * 2)];
  const webgl_unmasked = [{
    'vendor': 'Google Inc. (AMD)',
    'renderer': 'ANGLE (AMD, AMD Radeon(TM) Graphics Direct3D11 vs_5_0 ps_5_0, D3D11)'
  }, {
    'vendor': 'Google Inc. (Intel)',
    'renderer': 'ANGLE (Intel, Intel(R) HD Graphics Family Direct3D11 vs_5_0 ps_5_0, D3D11)'
  }, {
    'vendor': 'Google Inc. (Intel)',
    'renderer': 'ANGLE (Intel, Intel(R) HD Graphics 5300 Direct3D11 vs_5_0 ps_5_0, D3D11)'
  }, {
    'vendor': 'Google Inc. (Intel)',
    'renderer': 'ANGLE (Intel, Intel(R) HD Graphics 4600 Direct3D11 vs_5_0 ps_5_0, D3D11)'
  }, {
    'vendor': 'Google Inc. (Intel)',
    'renderer': 'ANGLE (Intel, Intel(R) UH Graphics 630  Direct3D11 vs_5_0 ps_5_0, D3D11)'
  }, {
    'vendor': 'Google Inc. (Intel)',
    'renderer': 'ANGLE (Intel, Intel(R) HD Graphics 4000 Direct3D11 vs_5_0 ps_5_0, D3D11)'
  }, {
    'vendor': 'Google Inc. (Intel)',
    'renderer': 'ANGLE (Intel, Intel(R) HD Graphics 530 Direct3D11 vs_5_0 ps_5_0, D3D11)'
  }, {
    'vendor': 'Google Inc. (NVIDIA)',
    'renderer': 'ANGLE (NVIDIA, NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0, D3D11)'
  }, {
    'vendor': 'Google Inc. (NVIDIA)',
    'renderer': 'ANGLE (NVIDIA, NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0, D3D11)'
  }, {
    'vendor': 'Google Inc. (NVIDIA)',
    'renderer': 'ANGLE (NVIDIA, NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0, D3D11)'
  }, {
    'vendor': 'Google Inc. (NVIDIA)',
    'renderer': 'ANGLE (NVIDIA, NVIDIA GeForce RTX 3090 Direct3D11 vs_5_0 ps_5_0, D3D11)'
  }][Math.floor(Math.random() * 11)];
  const webgl_data = {
    "YNkXWqxb": null,
    "PJuj3GdaWNvRm5vcGB1gWRdZrB4=": webgl_extensions,
    "BdnIWZZYGpuF1+Ochh04GnBaJxme1zLdVZtWWTwbo1pnGVjX0dybWRhaYFgXWKwb": [1, 1],
    "BdnIWZZYGpuF1+Ochp44WXDcJ1qeHTLXVVpWmzwco9tnGVjX0dybWRhaYFgXWKwb": [1, 1024],
    "PB2j3GeYWFrRWJvXGBxgGhdYrBs=": 8,
    "VZtW2Tzco1pnWlhY0VibGxgdYFoXWKyb": true,
    "o9xnWlgd0debmBhdYFkXmKwb": 8,
    "PB2j3GeYWFrRGpvXGBxgHRcZrFk=": 24,
    "PB2j3GeYWFrRm5vXGFlgWRfZrJw=": 8,
    "nhwyXlWcVts826MdZ1pY3NFYm5sYHmDXF1usWA==": 16,
    "YB0X3Kybk1oZ1yVdyNnPWQVbyFiW1xpahZzjWYYdOF1wWScentcyHVVZVhk8WqObZ1tYmNHYm9sYHmDXF1usWA==": 32,
    "z1kFWsieltca3IWc41mGHThdcFknHp7XMh1VWFYcPNejW2eYWFnR2JtdGB5g1xdbrFg=": 16384,
    "GZwl3Mgdz9sFWcjYltcanYWc41uGmTjbcJsnWp7XMl1Vm1YdPFujWWdYWNnRmZucGB5g1xdbrFg=": 1024,
    "lp4aWYXc41qGnDjXcJknWZ5dMplV11aYPFmjnGebWBnRnJtZGB5g1xdbrFg=": 16384,
    "yNyWWhodhV3jm4ZZONdwWCfZnloyW1VZVtc8XaOcZx5YHdEdm1kYHmDXF1usWA==": 16,
    "cJ4nWZ7cMlpVWVbXPF2jnGceWB3RHZtZGB5g1xdbrFg=": 16384,
    "49yG2ziccNgnHZ6dMllV2VbXPFqjm2ecWF7RnZtYGB5g1xdbrFg=": 30,
    "hpg43HCcJ1qeHTIdVddWWDxZox5nnFgd0Z2bWRgeYNcXW6xY": 16,
    "rB2T3BmbJVrI189dBdnIWZZbGliF1+Nahpw4WXAdJ12eWTIeVddWHTxZox5nnFgd0Z2bWRgeYNcXW6xY": 16,
    "yJzP3AUdyNuWWRrYhdfjnYacOFtwmSfbnpsyWlXXVl08WaMeZ5xYHdGdm1kYHmDXF1usWA==": 4096,
    "ONxwWidbntcyGVWcVh08HKPbZ1lY3dGdm1oYHmDXF1usWA==": [32767, 32767],
    "Zx1Y3NGYm1oYGWDXF5ysWQ==": 8,
    "Z1lYnNFZm5wYm2AZF5ysWQ==": "WebKit WebGL",
    "BdvIm5bcGlqFWeOchtc4nXDZJ1meXTJYVZtW2Twbo1hn2VjX0VqbmxhYYBkX3Kwa": "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)",
    "VR1W3DyYo1pnG1jX0dibWhhZYJsX3Kwd": 0,
    "0dubnBibYBkXnaxZ": "WebKit",
    "WJvRWpvbGJxg3BedrFk=": "WebGL 1.0 (OpenGL ES 2.0 Chromium)",
    "m9sYm2DcF1qs2JNaGZwlWcjXzxwFWMgdlhsa24XX45mG2TgacBonWp6cMtdVGVZZPBqjWGfXWNzRWZseGJxgHRedrFk=": 23,
    "J1qemzLXVVtW2TxZo1hnm1jX0Zyb2xibYNwXWqzYk1oZnCVZyNfPHAVYyB2WGxrbhdfjmYbZOBpwGidanpwy11UZVlk8GqNYZ9dY3NFZmx4YnGAdF52sWQ==": 127,
    "J1ieHjLXVVtW2TxZo1hnm1jX0Zyb2xibYNwXWqzYk1oZnCVZyNfPHAVYyB2WGxrbhdfjmYbZOBpwGidanpwy11UZVlk8GqNYZ9dY3NFZmx4YnGAdF52sWQ==": 127,
    "WNvRm5vcGFpg2BdarJyTWRnXJRzIWM8dBRvI25bXGpmFXeNbhhk4WnBbJ1menDLXVRlWWTwao1hn11jc0VmbHhicYB0XnaxZ": 23,
    "OFpwmyfXnlsy2VVZVlg8m6PXZ5xY29Gbm9wYWmDYF1qsnJNZGdclHMhYzx0FG8jbltcamYVd41uGGThacFsnWZ6cMtdVGVZZPBqjWGfXWNzRWZseGJxgHRedrFk=": 127,
    "OFhwHifXnlsy2VVZVlg8m6PXZ5xY29Gbm9wYWmDYF1qsnJNZGdclHMhYzx0FG8jbltcamYVd41uGGThacFsnWZ6cMtdVGVZZPBqjWGfXWNzRWZseGJxgHRedrFk=": 127,
    "GJtgWhfbrFqT3BlZJdjIHM+cBR3I15bbGliFmeMbht0413AbJ9uenDLXVRlWWTwao1hn11jc0VmbHhicYB0XnaxZ": 23,
    "npsyW1VaVlk816ObZ9lYnNFYm5sY12BaF9usWpPcGVkl2Mgcz5wFHcjXltsaWIWZ4xuG3TjXcBsn256cMtdVGVZZPBqjWGfXWNzRWZseGJxgHRedrFk=": 127,
    "nh4yW1VYVlk816ObZ9lYnNFYm5sY12BaF9usWpPcGVkl2Mgcz5wFHcjXltsaWIWZ4xuG3TjXcBsn256cMtdVGVZZPBqjWGfXWNzRWZseGJxgHRedrFk=": 127,
    "WNvRm5vcGFpg2BdarJyTWRnXJRzIWM8dBRvI25bXGpmF2eMahho4WnCcJ9eeGTJZVRpWWDzXo9xnm1gd0VubWRhYYNkXmayc": 23,
    "OFpwmyfXnlsy2VVZVlg8m6PXZ5xY29Gbm9wYWmDYF1qsnJNZGdclHMhYzx0FG8jbltcamYXZ4xqGGjhacJwn154ZMllVGlZYPNej3GebWB3RW5tZGFhg2ReZrJw=": 127,
    "OFhwHifXnlsy2VVZVlg8m6PXZ5xY29Gbm9wYWmDYF1qsnJNZGdclHMhYzx0FG8jbltcamYXZ4xqGGjhacJwn154ZMllVGlZYPNej3GebWB3RW5tZGFhg2ReZrJw=": 127,
    "o9tnm1jc0Vqb2BhaYJwXWazXkxwZWCUdyBvP2wXXyJmWXRpbhRnjWoZbOFlwnCfXnhkyWVUaVlg816PcZ5tYHdFbm1kYWGDZF5msnA==": 23,
    "41qGmzjXcFsn2Z5ZMlhVm1bXPJyj22ebWNzRWpvYGFpgnBdZrNeTHBlYJR3IG8/bBdfImZZdGluFGeNahls4WXCcJ9eeGTJZVRpWWDzXo9xnm1gd0VubWRhYYNkXmayc": 127,
    "41iGHjjXcFsn2Z5ZMlhVm1bXPJyj22ebWNzRWpvYGFpgnBdZrNeTHBlYJR3IG8/bBdfImZZdGluFGeNahls4WXCcJ9eeGTJZVRpWWDzXo9xnm1gd0VubWRhYYNkXmayc": 127,
    "0ZubWhjbYFoX3KxZk9gZHCWcyB3P1wXbyFiWmRobhd3j14YbONtwnCfXnhkyWVUaVlg816PcZ5tYHdFbm1kYWGDZF5msnA==": 23,
    "cJsnW55aMllV11abPNmjnGdYWJvR15taGNtgWhfcrFmT2BkcJZzIHc/XBdvIWJaZGhuF3ePXhhs423CcJ9eeGTJZVRpWWDzXo9xnm1gd0VubWRhYYNkXmayc": 127,
    "cB4nW55YMllV11abPNmjnGdYWJvR15taGNtgWhfcrFmT2BkcJZzIHc/XBdvIWJaZGhuF3ePXhhs423CcJ9eeGTJZVRpWWDzXo9xnm1gd0VubWRhYYNkXmayc": 127,
    "YNsXm6zck1oZ2CVayJzPWQXXyByWmxodhdfjWobZOBpwGidanpwy11UZVlk8GqNYZ9dY3NFZmx4YnGAdF52sWQ==": 0,
    "MlpVm1bXPFuj2WdZWFjRm5vXGJxg2xebrNyTWhnYJVrInM9ZBdfIHJabGh2F1+Nahtk4GnAaJ1qenDLXVRlWWTwao1hn11jc0VmbHhicYB0XnaxZ": 31,
    "MlhVHlbXPFuj2WdZWFjRm5vXGJxg2xebrNyTWhnYJVrInM9ZBdfIHJabGh2F1+Nahtk4GnAaJ1qenDLXVRlWWTwao1hn11jc0VmbHhicYB0XnaxZ": 30,
    "m9sYm2DcF1qs2JNaGZwlWcjXzxwFm8gdltcaWoVd41uGGThacFsnWZ6cMtdVGVZZPBqjWGfXWNzRWZseGJxgHRedrFk=": 0,
    "J1qemzLXVVtW2TxZo1hnm1jX0Zyb2xibYNwXWqzYk1oZnCVZyNfPHAWbyB2W1xpahV3jW4YZOFpwWydZnpwy11UZVlk8GqNYZ9dY3NFZmx4YnGAdF52sWQ==": 31,
    "J1ieHjLXVVtW2TxZo1hnm1jX0Zyb2xibYNwXWqzYk1oZnCVZyNfPHAWbyB2W1xpahV3jW4YZOFpwWydZnpwy11UZVlk8GqNYZ9dY3NFZmx4YnGAdF52sWQ==": 30,
    "F5usWpPbGVol3MhZz9gFHMiclh0a14Va45uG3TjXcBsn256cMtdVGVZZPBqjWGfXWNzRWZseGJxgHRedrFk=": 0,
    "VZtWWzxao1ln11ib0dmbnBhYYJsX16xak9sZWiXcyFnP2AUcyJyWHRrXhVrjm4bdONdwGyfbnpwy11UZVlk8GqNYZ9dY3NFZmx4YnGAdF52sWQ==": 31,
    "VR5WWzxYo1ln11ib0dmbnBhYYJsX16xak9sZWiXcyFnP2AUcyJyWHRrXhVrjm4bdONdwGyfbnpwy11UZVlk8GqNYZ9dY3NFZmx4YnGAdF52sWQ==": 30,
    "m9sYm2DcF1qs2JNaGZwlWcjXzxwFm8gdltcaWoXZ4xqGGjhacJwn154ZMllVGlZYPNej3GebWB3RW5tZGFhg2ReZrJw=": 0,
    "J1qemzLXVVtW2TxZo1hnm1jX0Zyb2xibYNwXWqzYk1oZnCVZyNfPHAWbyB2W1xpahdnjGoYaOFpwnCfXnhkyWVUaVlg816PcZ5tYHdFbm1kYWGDZF5msnA==": 31,
    "J1ieHjLXVVtW2TxZo1hnm1jX0Zyb2xibYNwXWqzYk1oZnCVZyNfPHAWbyB2W1xpahdnjGoYaOFpwnCfXnhkyWVUaVlg816PcZ5tYHdFbm1kYWGDZF5msnA==": 30,
    "WNvRm5vcGFpg2BdarJyTWRnXJRzIm88dBdfIWpZdGluFGeNahls4WXCcJ9eeGTJZVRpWWDzXo9xnm1gd0VubWRhYYNkXmayc": 0,
    "OFpwmyfXnlsy2VVZVlg8m6PXZ5xY29Gbm9wYWmDYF1qsnJNZGdclHMibzx0F18hall0aW4UZ41qGWzhZcJwn154ZMllVGlZYPNej3GebWB3RW5tZGFhg2ReZrJw=": 31,
    "OFhwHifXnlsy2VVZVlg8m6PXZ5xY29Gbm9wYWmDYF1qsnJNZGdclHMibzx0F18hall0aW4UZ41qGWzhZcJwn154ZMllVGlZYPNej3GebWB3RW5tZGFhg2ReZrJw=": 30,
    "GJtgWhfbrFqT3BlZJdjIHM+cBR3I15ZaGpuF3ePXhhs423CcJ9eeGTJZVRpWWDzXo9xnm1gd0VubWRhYYNkXmayc": 0,
    "npsyW1VaVlk816ObZ9lYnNFYm5sY12BaF9usWpPcGVkl2Mgcz5wFHcjXlloam4Xd49eGGzjbcJwn154ZMllVGlZYPNej3GebWB3RW5tZGFhg2ReZrJw=": 31,
    "nh4yW1VYVlk816ObZ9lYnNFYm5sY12BaF9usWpPcGVkl2Mgcz5wFHcjXlloam4Xd49eGGzjbcJwn154ZMllVGlZYPNej3GebWB3RW5tZGFhg2ReZrJw=": 30,
    "J5yeGTLbVVlWmzzXo51nWVgZ0dyb2hhbYFgXXayb": webgl_unmasked['vendor'],
    "OJxwnCdZnhkyWVVZVps816OcZ1lYGdHcm9oYW2BYF12smw==": webgl_unmasked['renderer']
  };
  Object.defineProperty(env, "canvas", {
    value: {
      canvas_2d: canvas_2d,
      canvas_webgl: canvas_webgl,
      webgl_data: webgl_data
    }
  });
})();
function get_reese84(reese84_js_url, reese84_aih, userAgent) {
  if (userAgent !== undefined) {
    env.navigator.userAgent = userAgent;
  }
  ;
  window.location = {
    protocol: reese84_js_url.match(/^(http:|https:)\/\//)?.[1] || "https:"
  };
  window.own_property_names = ['WritableStreamDefaultController;;;VirtualKeyboardGeometryChangeEvent;;;TransformStreamDefaultController;;;SVGComponentTransferFunctionElement;;;SVGAnimatedPreserveAspectRatio;;;ReadableStreamDefaultController;;;RTCPeerConnectionIceErrorEvent;;;OffscreenCanvasRenderingContext2D;;;NavigationCurrentEntryChangeEvent;;;MediaStreamAudioDestinationNode;;;WebTransportBidirectionalStream;;;WebTransportDatagramDuplexStream;;;AuthenticatorAssertionResponse;;;AuthenticatorAttestationResponse;;;BluetoothCharacteristicProperties;;;BluetoothRemoteGATTCharacteristic;;;PresentationConnectionAvailableEvent;;;PresentationConnectionCloseEvent;;;USBIsochronousInTransferPacket;;;USBIsochronousInTransferResult;;;USBIsochronousOutTransferPacket;;;USBIsochronousOutTransferResult;;;WindowControlsOverlayGeometryChangeEvent;;;oncontentvisibilityautostatechange;;;BrowserCaptureMediaStreamTrack;;;ContentVisibilityAutoStateChangeEvent;;;webkitResolveLocalFileSystemURL', 'WritableStreamDefaultController;;;VirtualKeyboardGeometryChangeEvent;;;TransformStreamDefaultController;;;SVGComponentTransferFunctionElement;;;SVGAnimatedPreserveAspectRatio;;;ReadableStreamDefaultController;;;RTCPeerConnectionIceErrorEvent;;;OffscreenCanvasRenderingContext2D;;;MediaStreamAudioDestinationNode;;;WebTransportBidirectionalStream;;;WebTransportDatagramDuplexStream;;;AuthenticatorAssertionResponse;;;AuthenticatorAttestationResponse;;;BrowserCaptureMediaStreamTrack;;;PresentationConnectionAvailableEvent;;;PresentationConnectionCloseEvent;;;USBIsochronousInTransferPacket;;;USBIsochronousInTransferResult;;;USBIsochronousOutTransferPacket;;;USBIsochronousOutTransferResult;;;WindowControlsOverlayGeometryChangeEvent;;;NavigationCurrentEntryChangeEvent;;;webkitResolveLocalFileSystemURL', 'WritableStreamDefaultController;;;SVGComponentTransferFunctionElement;;;SVGAnimatedPreserveAspectRatio;;;ReadableStreamDefaultController;;;RTCPeerConnectionIceErrorEvent;;;OffscreenCanvasRenderingContext2D;;;MediaStreamAudioDestinationNode;;;AuthenticatorAssertionResponse;;;AuthenticatorAttestationResponse;;;BluetoothCharacteristicProperties;;;BluetoothRemoteGATTCharacteristic;;;PresentationConnectionAvailableEvent;;;PresentationConnectionCloseEvent;;;USBIsochronousInTransferPacket;;;USBIsochronousInTransferResult;;;USBIsochronousOutTransferPacket;;;USBIsochronousOutTransferResult;;;WebTransportBidirectionalStream;;;WebTransportDatagramDuplexStream;;;VirtualKeyboardGeometryChangeEvent;;;webkitResolveLocalFileSystemURL'][Math.floor(Math.random() * 3)];
  var xor_key = Math["random"]() * 1073741824 | 0;
  var Az = 0 ^ -1;
  var bB = String["fromCharCode"](55296);
  var U6 = String["fromCharCode"](56319);
  var q1 = String["fromCharCode"](56320);
  var FK = String["fromCharCode"](57343);
  var gV = String["fromCharCode"](65533);
  var gi = new RegExp("(^|[^" + bB + "-" + U6 + "])[" + q1 + "-" + FK + "]", "g");
  var NS = new RegExp("[" + bB + "-" + U6 + "]([^" + q1 + "-" + FK + "]|$)", "g");
  var Eb = new RegExp("[\\u007F-\\uFFFF]", "g");
  var AV = [0, 1996959894, 3993919788, 2567524794, 124634137, 1886057615, 3915621685, 2657392035, 249268274, 2044508324, 3772115230, 2547177864, 162941995, 2125561021, 3887607047, 2428444049, 498536548, 1789927666, 4089016648, 2227061214, 450548861, 1843258603, 4107580753, 2211677639, 325883990, 1684777152, 4251122042, 2321926636, 335633487, 1661365465, 4195302755, 2366115317, 997073096, 1281953886, 3579855332, 2724688242, 1006888145, 1258607687, 3524101629, 2768942443, 901097722, 1119000684, 3686517206, 2898065728, 853044451, 1172266101, 3705015759, 2882616665, 651767980, 1373503546, 3369554304, 3218104598, 565507253, 1454621731, 3485111705, 3099436303, 671266974, 1594198024, 3322730930, 2970347812, 795835527, 1483230225, 3244367275, 3060149565, 1994146192, 31158534, 2563907772, 4023717930, 1907459465, 112637215, 2680153253, 3904427059, 2013776290, 251722036, 2517215374, 3775830040, 2137656763, 141376813, 2439277719, 3865271297, 1802195444, 476864866, 2238001368, 4066508878, 1812370925, 453092731, 2181625025, 4111451223, 1706088902, 314042704, 2344532202, 4240017532, 1658658271, 366619977, 2362670323, 4224994405, 1303535960, 984961486, 2747007092, 3569037538, 1256170817, 1037604311, 2765210733, 3554079995, 1131014506, 879679996, 2909243462, 3663771856, 1141124467, 855842277, 2852801631, 3708648649, 1342533948, 654459306, 3188396048, 3373015174, 1466479909, 544179635, 3110523913, 3462522015, 1591671054, 702138776, 2966460450, 3352799412, 1504918807, 783551873, 3082640443, 3233442989, 3988292384, 2596254646, 62317068, 1957810842, 3939845945, 2647816111, 81470997, 1943803523, 3814918930, 2489596804, 225274430, 2053790376, 3826175755, 2466906013, 167816743, 2097651377, 4027552580, 2265490386, 503444072, 1762050814, 4150417245, 2154129355, 426522225, 1852507879, 4275313526, 2312317920, 282753626, 1742555852, 4189708143, 2394877945, 397917763, 1622183637, 3604390888, 2714866558, 953729732, 1340076626, 3518719985, 2797360999, 1068828381, 1219638859, 3624741850, 2936675148, 906185462, 1090812512, 3747672003, 2825379669, 829329135, 1181335161, 3412177804, 3160834842, 628085408, 1382605366, 3423369109, 3138078467, 570562233, 1426400815, 3317316542, 2998733608, 733239954, 1555261956, 3268935591, 3050360625, 752459403, 1541320221, 2607071920, 3965973030, 1969922972, 40735498, 2617837225, 3943577151, 1913087877, 83908371, 2512341634, 3803740692, 2075208622, 213261112, 2463272603, 3855990285, 2094854071, 198958881, 2262029012, 4057260610, 1759359992, 534414190, 2176718541, 4139329115, 1873836001, 414664567, 2282248934, 4279200368, 1711684554, 285281116, 2405801727, 4167216745, 1634467795, 376229701, 2685067896, 3608007406, 1308918612, 956543938, 2808555105, 3495958263, 1231636301, 1047427035, 2932959818, 3654703836, 1088359270, 936918000, 2847714899, 3736837829, 1202900863, 817233897, 3183342108, 3401237130, 1404277552, 615818150, 3134207493, 3453421203, 1423857449, 601450431, 3009837614, 3294710456, 1567103746, 711928724, 3020668471, 3272380065, 1510334235, 755167117];
  function mb(pq) {
    return "\\u" + ("0000" + pq.charCodeAt(0).toString(16)).substr(-4);
  }
  ;
  const zO = sha1("rFmTXhnXJdrI28+cBdfIHpZZGh2FHOMchps4WnDXJ9yemTJeVdlWWjxao5tnHVic0deb3BjbYJsXmqzc" + xor_key)["match"](new window["RegExp"]("..", "g"))["map"](function (dh) {
    return parseInt(dh, 16);
  });
  function qu() {
    return String["fromCharCode"]["apply"](null, Array["from"](""["replace"]["call"](JSON["stringify"], new window["RegExp"]("[\\u0080-\\uFFFF]", "g"), ""))["slice"](-21)["map"](function (H3, Dz) {
      return H3["charCodeAt"](0) ^ zO[Dz % zO["length"]] & 127;
    }));
  }
  ;
  function xorShift128(r1, r2) {
    var rI = r1;
    var Vh = r2;
    return function () {
      var di = rI;
      di ^= di << 23;
      di ^= di >> 17;
      var Wu = Vh;
      rI = Wu;
      di ^= Wu;
      di ^= Wu >> 26;
      Vh = di;
      return (rI + Vh) % 4294967296;
    };
  }
  ;
  function xoredToByteArr(r1, r2, rounds) {
    const xored = xorShift128(r1, r2);
    const bytesArr = [];
    for (let i = 0; i < rounds; i++) {
      bytesArr[i] = xored() & 255;
    }
    ;
    return bytesArr;
  }
  ;
  function jsondataToChaCodeArr(data) {
    let arr = [];
    let jsonStr = JSON.stringify(data, (a, b) => a === undefined ? null : b);
    let encodStr = jsonStr.replace(Eb, mb);
    for (let i = 0; i < encodStr.length; i++) {
      arr.push(encodStr.charCodeAt(i));
    }
    ;
    return arr;
  }
  ;
  function new_arr_256_128(byteArr, arr, before, after) {
    const slicedArr = byteArr["slice"](before, after);
    const arrLen = arr.length;
    const slicedArrLen = slicedArr.length;
    let newArr = [];
    for (let i = 0; i < arrLen; i++) {
      let num = arr[i];
      let step = slicedArr[i % slicedArrLen] & 127;
      newArr.push((num + step) % 256 ^ 128);
    }
    ;
    return newArr;
  }
  ;
  function new_arr_113_0(byteArr, arr, before, after) {
    const slicedArr = byteArr["slice"](before, after);
    const arrLen = arr.length;
    const slicedArrLen = slicedArr.length;
    let newArr = [];
    let value = 113;
    for (let i = 0; i < arrLen; i++) {
      let num = arr[i];
      let step = slicedArr[i % slicedArrLen];
      let vvv = num ^ step ^ value;
      newArr.push(vvv);
      value = vvv;
    }
    ;
    return newArr;
  }
  ;
  function new_arr_2_push(byteArr, arr, before, after) {
    let arrLen = arr.length;
    let sliceArr = byteArr["slice"](before, after);
    let sliceArrLen = sliceArr.length;
    let newArr = [];
    for (let i = 0; i < arrLen; i++) {
      newArr.push(arr[i]);
      newArr.push(sliceArr[i % sliceArrLen]);
    }
    ;
    return newArr;
  }
  ;
  function new_arr_8_225(arr, byteArr, after) {
    const step = byteArr[after] % 7 + 1;
    return arr.map(elem => (elem << step | elem >> 8 - step) & 255);
  }
  ;
  function new_arr_none(arr, byteArr, after) {
    const newArr = [];
    const len = arr.length;
    for (let i = 0; i < len; i++) {
      newArr.push(arr[(i + byteArr[after]) % len]);
    }
    ;
    return newArr;
  }
  function swapAdjacentArryElements(arr) {
    for (let i = 0; i < arr.length - 1; i += 2) {
      [arr[i], arr[i + 1]] = [arr[i + 1], arr[i]];
    }
    ;
    return arr;
  }
  ;
  function reverse_arr(arr) {
    return [...arr].reverse();
  }
  ;
  function to_btoa(_obj) {
    let str = [];
    Object.values(_obj).forEach(value => {
      str += String.fromCharCode(value);
    });
    return btoa(str);
  }
  ;
  const re84 = {};
  re84["YNsXmKxa"] = {
    "VR1W3DxZo5tnWVid0Vmb1xhdYNwXW6zb": [],
    "J9yeGjJZVV1W2Dwdo9tnGVjX0dmbWRhYYJsX2Kwa": []
  };
  re84["PNujm2cdWFrRW5tYGB1g2xdYrF0="] = {};
  re84["JxqeWDLcVddWGjxaoxlnHVjX0V2bmxjYYNsXWKzY"] = reese84_aih;
  re84["WB3RW5scGB1gWRdYrB0="] = function () {
    const byteArr = xoredToByteArr(2328399149, xor_key, 77);
    const jsonArr = jsondataToChaCodeArr(1);
    const step1 = new_arr_113_0(byteArr, jsonArr, 0, 31);
    const step2 = new_arr_256_128(byteArr, step1, 31, 59);
    const step3 = new_arr_113_0(byteArr, step2, 59, 76);
    return to_btoa(step3);
  }();
  re84["GtyF2+Ochhw4HXCcJ1qe3DLYVRlWWTyco9dnHVjb0dmbWBidYFoXm6xY"] = env.navigator['NavigatorAttr'];
  re84["PJujHWfZWFnR15tYGFlgnBddrNw="] = env.navigator.userAgent;
  re84["Z9lYWdFdm1gYm2DZFxusWA=="] = env.navigator.language;
  re84["o9xn2VhZ0V2bWBibYNkXG6xY"] = {
    "45yGHTjbcFonHJ7YMpxVWVbcPNejGWcdWF7RWZucGNtgHBccrJw=": false,
    "m14YnGBYF1isnA==": env.navigator.languages
  };
  re84["VVtWWTwdo1pnHVjX0VmbmxicYJwX2Kxd"] = function () {
    const time_Date_now = Date.now();
    const time_new_File_lastModified = time_Date_now + rdm(1, 3);
    const time_performance_now = rdm(2875, 3521);
    const time_performance_timing_navigationStart = time_Date_now - time_performance_now;
    const time_new_DocumentTimeline_currentTime = time_performance_now / 1.000000533020838;
    const times = {};
    times["GB1gWRcZrFg="] = function (data) {
      const byteArr = xoredToByteArr(4293051610, xor_key, 51);
      const jsonArr = jsondataToChaCodeArr(data.toString());
      const step1 = new_arr_256_128(byteArr, jsonArr, 0, 19);
      const step2 = new_arr_2_push(byteArr, step1, 19, 50);
      const setp3 = swapAdjacentArryElements(step2);
      return to_btoa(setp3);
    }(time_Date_now);
    times["GBtgWReZrFo="] = function (data) {
      const byteArr = xoredToByteArr(1624825960, xor_key, 2);
      const jsonArr = jsondataToChaCodeArr(data.toString());
      const step1 = reverse_arr(jsonArr);
      const setp2 = swapAdjacentArryElements(step1);
      const step3 = new_arr_8_225(setp2, byteArr, 0);
      const step4 = swapAdjacentArryElements(step3);
      return to_btoa(step4);
    }(time_new_File_lastModified);
    times["Vlk8m6PYZ1tYWNHbm5wYnGCZFxysWQ=="] = function (data) {
      const byteArr = xoredToByteArr(2781904740, xor_key, 52);
      const jsonArr = jsondataToChaCodeArr(data.toString());
      const step1 = new_arr_256_128(byteArr, jsonArr, 0, 20);
      const step2 = reverse_arr(step1);
      const step3 = new_arr_113_0(byteArr, step2, 20, 50);
      const step4 = new_arr_8_225(step3, byteArr, 50);
      return to_btoa(step4);
    }(time_performance_now);
    times["Z5tYWdEbm1oYW2BZFx2sWg=="] = function (data) {
      const byteArr = xoredToByteArr(3391494669, xor_key, 26);
      const jsonArr = jsondataToChaCodeArr(data.toString());
      const step1 = new_arr_8_225(jsonArr, byteArr, 0);
      const step2 = swapAdjacentArryElements(step1);
      const step3 = new_arr_2_push(byteArr, step2, 1, 24);
      const newArr = new_arr_none(step3, byteArr, 24);
      return to_btoa(newArr);
    }(time_new_DocumentTimeline_currentTime);
    times["cJwnHZ4dMlhV11bcPNujm2cdWFrR2ZtYGJ1gWhebrFg="] = function (data) {
      const byteArr = xoredToByteArr(1887139459, xor_key, 24);
      const jsonArr = jsondataToChaCodeArr(data.toString());
      const step1 = reverse_arr(jsonArr);
      const step2 = new_arr_8_225(step1, byteArr, 0);
      const step3 = new_arr_8_225(step2, byteArr, 1);
      const step4 = new_arr_2_push(byteArr, step3, 2, 23);
      return to_btoa(step4);
    }(time_performance_timing_navigationStart);
    const byteArr = xoredToByteArr(3591488435, xor_key, 68);
    const jsonArr = jsondataToChaCodeArr(times);
    const step1 = new_arr_2_push(byteArr, jsonArr, 0, 21);
    const step2 = new_arr_113_0(byteArr, step1, 21, 50);
    const step3 = new_arr_256_128(byteArr, step2, 50, 66);
    const step4 = new_arr_8_225(step3, byteArr, 66);
    return to_btoa(step4);
  }();
  re84['hVnj3IZeOBxw1ycdnlsyWVVbVlo8nKPXZx1Y29HZm1gYnWBaF5usWA=='] = function () {
    const publicEnc = function (data) {
      const byteArr = xoredToByteArr(3736749910, xor_key, 34);
      const jsonArr = jsondataToChaCodeArr(data);
      const step1 = new_arr_2_push(byteArr, jsonArr, 0, 16);
      const step2 = swapAdjacentArryElements(step1);
      const step3 = new_arr_113_0(byteArr, step2, 16, 33);
      return to_btoa(step3);
    };
    const mimeTypes = env.navigator.mimeTypes;
    let keys = Object.keys(mimeTypes),
      fp = [];
    for (var key of keys) {
      let mimetype = mimeTypes[key],
        obj = {};
      obj["Z1lY3NFamx4YmWCZF9ysXQ=="] = mimetype["suffixes"];
      obj["GBxgWRcdrF4="] = mimetype["type"];
      obj["yFmWWBpbhVnjm4ZaOBtw1yeZnloym1VdVtk8HKMbZxlY19Ebm1kYWGCYF1msmw=="] = mimetype["enabledPlugin"]["filename"];
      const value = publicEnc(obj);
      fp[fp["length"]] = [key, value];
    }
    ;
    const ret = publicEnc(fp);
    return ret;
  }();
  re84["0VmbmxicYFkX3KzY"] = function () {
    const obj = {};
    obj["mxoYGWAdF92sWg=="] = env.screen["width"];
    obj["0RqbHRhaYNkXGqxZ"] = env.screen["height"];
    obj.VRpWHTxao9lnGlhZ0Rub1xhYYFoXWKyd = env.screen["availHeight"];
    obj["PJmjHWcbWFnRG5vXGFhgWhdYrJ0="] = env.screen["availLeft"];
    obj.oxxnHVjb0Rub1xhYYFoXWKyd = env.screen["availTop"];
    obj["Vho8GaMdZ91YWtEbm9cYWGBaF1isnQ=="] = env.screen["availWidth"];
    obj["Vho8HKMdZxlYWdEbm9cYHmBZFxysWg=="] = env.screen["pixelDepth"];
    obj["Vho8GaMdZ91YWtGcm9cYm2BZF1qsmw=="] = env.window["innerWidth"];
    obj.VRpWHTxao9lnGlhZ0Zyb1xibYFkXWqyb = env.window["innerHeight"];
    obj["Vho8GaMdZ91YWtGcm9cYHWBZF9usXQ=="] = env.window["outerWidth"];
    obj.VRpWHTxao9lnGlhZ0Zyb1xgdYFkX26xd = env.window["outerHeight"];
    obj.hlo423BYJx2e1zKcVVlWGzxaox5n11gc0dibWRidYFoXGaxZ = env.window["devicePixelRatio"];
    obj["cBwnWZ4dMl5Vm1bXPFqj22dYWB3Rm5sdGFpgWRfbrJw="] = env.screen["orientation"]["type"];
    obj["Z9dYHtFZm5sYnGBZF9ys2A=="] = env.window["screenX"];
    obj["Z9dYXtFZm5sYnGBZF9ys2A=="] = env.window["screenY"];
    const byteArr = xoredToByteArr(612538604, xor_key, 81);
    const jsonArr = jsondataToChaCodeArr(obj);
    const step1 = new_arr_113_0(byteArr, jsonArr, 0, 29);
    const step2 = new_arr_2_push(byteArr, step1, 29, 59);
    const step3 = new_arr_256_128(byteArr, step2, 59, 80);
    return to_btoa(step3);
  }();
  re84["Z5tYWdGem9sYW2BZFx2sWg=="] = 8;
  re84["PBmjmGcZWNfRHptZGBlgWRdarJs="] = true;
  re84["VdtWnDydo1pnGlhY0ZibWRgZYNcXWKwZ"] = false;
  re84["JVnIXs8cBdfIHZYbGlmFWOOYhpw4WnCdJ1ieHTLXVV1WGzxZo9xn11ic0VibGxjbYJgX2awb"] = null;
  re84["MllVWFbcPFijmGdYWB3R15sZGFlgmxfbrBw="] = true;
  re84["o9xnWFjc0dibGxhdYNcX2Kwc"] = "unknown";
  re84["Z5xYW9GZm9sYWGAdFxysGw=="] = "Win32";
  re84["VdhW2jyco1hn11gd0dubHRjXYJsXGazb"] = "unknown";
  re84["WNzRWpubGF1g2RccrBs="] = env.navigator.plugins;
  re84["VR1WWDxbo1ln3FjX0VqbmxhdYNkXHKwb"] = {
    "J1meWDJbVddWmzxZo1tnWlgd0Rmb1xhbYFkXm6xY": "namedItem",
    "o1lnWFhb0debmxhZYFsXWqwd": "item",
    "VVtWWTybo1hnGljX0Vmb3BiZYJwXnKxZ": "refresh"
  };
  re84['0Vib3BibYJ0X2KxY'] = function () {
    function get_value(data) {
      const byteArr = xoredToByteArr(638959349, xor_key, 48);
      const jsonArr = jsondataToChaCodeArr(data);
      const step1 = new_arr_8_225(jsonArr, byteArr, 0);
      const step2 = new_arr_256_128(byteArr, step1, 1, 31);
      const step3 = new_arr_256_128(byteArr, step2, 31, 47);
      const step4 = reverse_arr(step3);
      return to_btoa(step4);
    }
    ;
    const enc_sha1_canvas2d = get_value(env.canvas['canvas_2d']['long']);
    const obj = {
      "WNnRWpubGJtgGRfdrFo=": true,
      "0ZibHBjdYFkXHazb": true,
      "Z5tY2dEZm1oYWWCbF5isGw==": true
    };
    obj["YNkXWqxb"] = enc_sha1_canvas2d;
    const byteArr = xoredToByteArr(2284030616, xor_key, 18);
    const jsonArr = jsondataToChaCodeArr(obj);
    const step1 = new_arr_113_0(byteArr, jsonArr, 0, 16);
    const newArr = new_arr_none(step1, byteArr, 16);
    return to_btoa(newArr);
  }();
  re84['MhpVG1YdPFmjWGfXWBrRWJvcGJtgnRfYrFg='] = function () {
    const substredCanvas = env.canvas['canvas_2d']['short']["substr"](33, 227);
    const byteArr = xoredToByteArr(1079950851, xor_key, 75);
    const jsonArr = jsondataToChaCodeArr(substredCanvas);
    const step1 = new_arr_113_0(byteArr, jsonArr, 0, 24);
    const newArr = new_arr_none(step1, byteArr, 24);
    const step2 = new_arr_2_push(byteArr, newArr, 25, 47);
    const step3 = new_arr_113_0(byteArr, step2, 47, 74);
    const value = to_btoa(step3);
    return {
      'npwyG1XXVl08HaNYZxlYWNHam9cYXWCbF9isGg==': value
    };
  }();
  re84["0dmbGxiYYNcX3axZ"] = function () {
    function enc_sha1_canvas_webgl(data) {
      const byteArr = xoredToByteArr(4143207636, xor_key, 57);
      const jsonArr = jsondataToChaCodeArr(data);
      const step1 = new_arr_256_128(byteArr, jsonArr, 0, 24);
      const step2 = swapAdjacentArryElements(step1);
      const step3 = new_arr_2_push(byteArr, step2, 24, 55);
      const step4 = new_arr_8_225(step3, byteArr, 55);
      return to_btoa(step4);
    }
    ;
    env.canvas['webgl_data']["YNkXWqxb"] = enc_sha1_canvas_webgl(env.canvas['canvas_webgl']['long']);
    const byteArr = xoredToByteArr(430797680, xor_key, 31);
    const jsonArr = jsondataToChaCodeArr(env.canvas['webgl_data']);
    const step1 = new_arr_113_0(byteArr, jsonArr, 0, 29);
    const step2 = swapAdjacentArryElements(step1);
    const step3 = new_arr_8_225(step2, byteArr, 29);
    return to_btoa(step3);
  }();
  re84["MhpVG1YdPFmjWGfXWBrR2ZsbGJhg1xfdrFk="] = function () {
    const substredCanvas = env.canvas['canvas_webgl']['short']["substr"](33, 227);
    const byteArr = xoredToByteArr(781766443, xor_key, 24);
    const jsonArr = jsondataToChaCodeArr(substredCanvas);
    const step1 = reverse_arr(jsonArr);
    const step2 = new_arr_8_225(step1, byteArr, 0);
    const step3 = new_arr_256_128(byteArr, step2, 1, 23);
    const value = to_btoa(step3);
    return {
      'npwyG1XXVl08HaNYZxlYWNHam9cYXWCbF9isGg==': value
    };
  }();
  re84["Vlg8WaMdZ9dYW9HZmxsYmGDXF92sWQ=="] = {
    "hls4WXCbJ1ienDLXVR1WWTxbo1lnnFhY0RybWBgdYNcX2axZ": "getParameter",
    "hZ3jWYYdOFpwmydYnpwy11UdVlk8W6NZZ5xYWNEcm1gYHWDXF9msWQ==": true
  };
  re84["mxoYXWDYFx2s2w=="] = function () {
    const obj = {};
    obj["cB0n3J5aMptVHFbbPBqj12ddWNjRHZvbGB5g1xdbrFg="] = env.navigator["maxTouchPoints"];
    obj["cB0n3J5aMptVHFbbPBqj12ddWNjRHZvbGB5g1xdbrFg="] = 0;
    obj["Vh08WaObZ1lYndEam9cYXWDYFx2s2w=="] = false;
    obj["Vh08WKOcZ9xYHdEam9cYXWDYFx2s2w=="] = false;
    const byteArr = xoredToByteArr(764395007, xor_key, 57);
    const jsonArr = jsondataToChaCodeArr(obj);
    const step1 = new_arr_256_128(byteArr, jsonArr, 0, 30);
    const step2 = new_arr_2_push(byteArr, step1, 30, 56);
    const step3 = swapAdjacentArryElements(step2);
    return to_btoa(step3);
  }();
  re84["m9sYGWBZF52sWg=="] = function () {
    const video_canPlay = {
      "YNkX26zZ": "probably",
      "GI1gDRcarIw=": "probably",
      "GJhgWxfdrFk=": "probably"
    };
    const byteArr = xoredToByteArr(2514653307, xor_key, 29);
    const jsonArr = jsondataToChaCodeArr(video_canPlay);
    const step1 = new_arr_8_225(jsonArr, byteArr, 0);
    const step2 = new_arr_256_128(byteArr, step1, 1, 28);
    return to_btoa(step2);
  }();
  re84["m9sYGWBaF1isXQ=="] = function () {
    const video_canPlay = {
      "YNkX26zZ": "probably",
      "YMwXW6wc": "probably",
      "YJ0X3axY": "probably",
      "YFgXW6wN": "maybe",
      "Vl48nKNYZ1hYnNFem9cYHGAdF1msWw==": "nope",
      "GlmFDONMhhk4DXDXJw2e2DJMVVhWnTwNo9dnW1gc0dub1xgZYFkXnaxa": "probably"
    };
    const byteArr = xoredToByteArr(836013910, xor_key, 67);
    const jsonArr = jsondataToChaCodeArr(video_canPlay);
    const step1 = new_arr_2_push(byteArr, jsonArr, 0, 21);
    const step2 = new_arr_256_128(byteArr, step1, 21, 46);
    const step3 = new_arr_256_128(byteArr, step2, 46, 65);
    const step4 = new_arr_8_225(step3, byteArr, 65);
    return to_btoa(step4);
  }();
  re84["0dubnBibYBkXnaxZ"] = env.navigator["vendor"];
  re84["WB3RXZvYGNtgGRccrJw="] = env.navigator["product"];
  re84["Vpg83KNdZx1Y19Fdm9gY22AZFxysnA=="] = env.navigator["productSub"];
  re84["WJzR3JtZGNtg3ReYrJw="] = function () {
    const data = {
      "F1qsWQ==": false,
      "0VubWRicYNsX2Kwa": {
        "OFlwWiednlgyHVXXVps8WaPcZ1pYW9HXmx0YWGAZFxus2w==": true,
        "YBwXWKwc": [["isInstalled", "vwec"], ["getDetails", "vwec"], ["getIsInstalled", "vwec"], ["installState", "vwec"], ["runningState", "vwec"], ["InstallState", "vwec"], ["RunningState", "vwec"]],
        "PFmj3GcdWFrRWZucGNtgHBccrJw=": [["loadTimes.length", 0], ["loadTimes.name", 1], ["loadTimes.prototype", 1], ["csi.length", 0], ["csi.name", 1], ["csi.prototype", 1], ["app.isInstalled", 0], ["app.getDetails", 2], ["app.getIsInstalled", 2], ["app.installState", 2], ["app.runningState", 2], ["app.InstallState", 3], ["app.RunningState", 3], ["runtime.connect", 2], ["runtime.sendMessage", 2], ["runtime.OnInstalledReason", 4], ["runtime.OnRestartRequiredReason", 3], ["runtime.PlatformArch", 6], ["runtime.PlatformNaclArch", 5], ["runtime.PlatformOs", 6], ["runtime.RequestUpdateCheckStatus", 3]]
      },
      "o5xnnVhZ0ZybWhiYYBkX3axZ": false,
      "OB1wWSfYnpgymlXXVts8W6NZZ5xY29HYmxoY3GDXFxqsWA==": true,
      "nh0yHVXXVpw826ObZx1YWtFZm9gYm2CbF9is2w==": env.navigator.connection.rtt,
      "OBpw2Scdnlkym1XXVhs82aPbZ9hY2tEZm10Y2GDaFxmsXQ==": null
    };
    const byteArr = xoredToByteArr(694216168, xor_key, 103);
    const jsonArr = jsondataToChaCodeArr(data);
    const step1 = new_arr_113_0(byteArr, jsonArr, 0, 20);
    const step2 = new_arr_256_128(byteArr, step1, 20, 51);
    const step3 = new_arr_2_push(byteArr, step2, 51, 76);
    const step4 = new_arr_113_0(byteArr, step3, 76, 102);
    return to_btoa(step4);
  }();
  re84["0dub3RibYBkX3axa"] = function () {
    const data = {
      "nh0yGlWbVtk8G6NZZ15Y19Hbm5wY3GAdFxqsWg==": rdm(2, 3),
      "hdjjXoZZOJtwnCecntgyXVXbVps816PYZ5xYWdHdm1gYnGAZFxqsWA==": env.navigator.hardwareConcurrency,
      "0VubWRicYFgXWqyZ": false,
      "WF7RWZucGB1gHReYrFg=": true,
      "hls4WXCbJ1ie2TLXVZhWXTwZo1lnWVjX0dubGxibYNwX2Kzb": "debug",
      "hZ3jWYYdOFpwmydYntky11WYVl08GaNZZ1lY19HbmxsYm2DcF9is2w==": true,
      "ltsaW4Wb4x2GGjhYcNcnHJ6cMllV2FbbPJyj3GcZWFnRXZubGNxg1xcarFg=": false,
      "cNsnW56bMh1VGlZYPNejHGcbWBvR2JtYGNxg1xcarFg=": false,
      "hZvj3IZaONtw2Ccdnl0ym1XXVpk8naNZZx1YWtGbm1gYm2DXF5us2w==": [],
      "PJujHWcdWFnRWpvcGJxg3BccrFk=": 1,
      "o15nWFic0dubnBhbYBwXHaxZ": 0,
      "hVnjnIacOJ1w3CdZntsymFVZVtc8m6PYZ1tYWNHbm5wYnGCZFxysWQ==": {
        "GtyFHONZhh04XnBeJ9eeHTKcVVlWmzwZo9dnHVhZ0dubnBgcYBwX3Kxd": ["element", "event", "first-input", "largest-contentful-paint", "layout-shift", "longtask", "mark", "measure", "navigation", "paint", "resource"]
      },
      "PJyjXmebWB3R3JtZGNxg1xcarFg=": false
    };
    const byteArr = xoredToByteArr(1513031664, xor_key, 27);
    const jsonArr = jsondataToChaCodeArr(data);
    const step1 = new_arr_8_225(jsonArr, byteArr, 0);
    const step2 = swapAdjacentArryElements(step1);
    const newArr = new_arr_none(step2, byteArr, 1);
    const step3 = new_arr_113_0(byteArr, newArr, 2, 26);
    return to_btoa(step3);
  }();
  re84["Z9tYm9Edm1oY2GBYFxus2w=="] = {
    "Z9tYG9Hbm9gY22AdFxysnA==": location.protocol
  };
  re84["Vl48nKNYZ1hYnNHcm9cYm2AdF5ms2w=="] = [["Calibri", "Marlett"], ["Calibri", "Marlett", "SimHei"], ["Calibri", "Century", "Marlett", "SimHei"], ["Calibri", "Haettenschweiler", "Marlett", "SimHei"], ["Calibri", "Century", "Haettenschweiler", "Marlett", "SimHei"], ["Calibri", "Marlett", "Pristina", "SimHei"], ["Calibri", "Century", "Haettenschweiler", "Marlett", "Pristina", "SimHei"]][Math.floor(Math.random() * 7)];
  re84["WNzRHJsdGJxgWhfcrNg="] = {
    "cJsnHZ7bMl1V11bYPByjHWecWFrR3JvYGNhg1xfcrJw=": 1,
    "4x2GXTibcNgn254dMtdVWlYcPNijnGfXWNzRm5tZGBtgWhdarJs=": 0,
    "cJsnHZ5bMllVG1ZZPNejWWebWB3RW5tZGNhgXRcZrNs=": [],
    "GFhgGRcarFk=": [{
      'src': reese84_js_url
    }],
    "GBlgXheYrNs=": []
  };
  re84["Vh08WaObZ5tYW9Gcm9sYnWBaF1msmw=="] = function () {
    const data = {
      "GF5g2xcerByTnBnZJdfIWs+bBR3InJbXGtyFHePbhho413AbJx2eWTJYVdxWHTyco9dnWVhZ0VmbHRgcYBwXHKxd": false,
      "PJujWmfbWFjR3JtZGJ1gnBfXrBmT2xlZJZvI18+dBdnIG5aYGteF3eNZhho413AbJx2eWTJYVdxWHTyco9dnWVhZ0VmbHRgcYBwXHKxd": false,
      "llkaHYUc4xyGmzhacNcn3J6ZMl5V2VZaPFqjm2cdWJzR15vcGNtgmxearNw=": qu()
    };
    const byteArr = xoredToByteArr(*********, xor_key, 41);
    const jsonArr = jsondataToChaCodeArr(data);
    const step1 = reverse_arr(jsonArr);
    const step2 = new_arr_2_push(byteArr, step1, 0, 16);
    const step3 = new_arr_256_128(byteArr, step2, 16, 40);
    return to_btoa(step3);
  }();
  re84["llka3IUd41qGWTiccNsnHJ4cMpxV3VbXPBmj22daWJvR15vdGJtg2RcbrNs="] = function () {
    const data = window.own_property_names;
    const byteArr = xoredToByteArr(1172444063, xor_key, 21);
    const jsonArr = jsondataToChaCodeArr(data);
    const step1 = new_arr_113_0(byteArr, jsonArr, 0, 20);
    const step2 = swapAdjacentArryElements(step1);
    return to_btoa(step2);
  }();
  re84["BZzI3JYdGtuFWuMchtg4nHBZJ9ye1zIZVZtWHTydo1ln11hZ0dub3RibYBkX3axa"] = function () {
    const data = [["onbeforeinstallprompt", "gsec"], ["onbeforexrselect", "gsec"], ["onblur", "gsec"], ["onbeforeprint", "gsec"], ["onbeforeunload", "gsec"], ["onunhandledrejection", "gsec"], ["onunload", "gsec"]];
    const byteArr = xoredToByteArr(231443536, xor_key, 54);
    const jsonArr = jsondataToChaCodeArr(data);
    const newArr = new_arr_none(jsonArr, byteArr, 0);
    const step1 = new_arr_2_push(byteArr, newArr, 1, 23);
    const step2 = new_arr_256_128(byteArr, step1, 23, 53);
    return to_btoa(step2);
  }();
  re84["ONxwWSdbnloyHVUdVtc8WKPcZ9dYG9Hbm90Ym2AZF92sWg=="] = function () {
    const data = ["Scheduler", "TaskControll$", "TaskPriority$", "TaskSignal", "SharedWorker", "SpeechSynthe$", "SpeechSynthe$", "SpeechSynthe$", "TrustedHTML", "TrustedScrip$", "TrustedScrip$", "TrustedTypeP$", "TrustedTypeP$", "URLPattern", "VideoPlaybac$", "VirtualKeybo$", "XSLTProcesso$", "webkitSpeech$", "webkitSpeech$", "webkitSpeech$", "webkitSpeech$", "webkitSpeech$", "openDatabase", "webkitReques$", "webkitResolv$", "reese84", "reese84inter$", "initializePr$", "protectionSu$", "protectionLo$"];
    const byteArr = xoredToByteArr(2886650022, xor_key, 26);
    const jsonArr = jsondataToChaCodeArr(data);
    const step1 = swapAdjacentArryElements(jsonArr);
    const step2 = new_arr_256_128(byteArr, step1, 0, 25);
    const step3 = reverse_arr(step2);
    return to_btoa(step3);
  }();
  re84["Jx2e2zKcVd1WHDxao1ln11id0VibGxjcYF0Xnaxa"] = function () {
    const data = {
      "mxoYGWAdF92sWg==": env.window["visualViewport"]["width"],
      "0RqbHRhaYNkXGqxZ": env.window["visualViewport"]["height"],
      "m1kYWGAbF9ys2A==": env.window["visualViewport"]["scale"]
    };
    const byteArr = xoredToByteArr(4271953189, xor_key, 52);
    const jsonArr = jsondataToChaCodeArr(data);
    const step1 = reverse_arr(jsonArr);
    const step2 = new_arr_256_128(byteArr, step1, 0, 28);
    const step3 = new_arr_256_128(byteArr, step2, 28, 50);
    const step4 = new_arr_8_225(step3, byteArr, 50);
    return to_btoa(step4);
  }();
  re84["hZvjHYZbOFlw2Cddnhky21UbVtc8HaNbZ9dYGtEdm1kYWWBYF9isnA=="] = function () {
    const list = [function createAttribute() {}, function createElement() {}, function createElementNS() {}, undefined, undefined, undefined];
    const arr = [];
    for (i in list) {
      let name = list[i] ? list[i]["name"] : undefined;
      const data = [parseInt(i), name];
      const byteArr = xoredToByteArr(2047203916, xor_key, 30);
      const jsonArr = jsondataToChaCodeArr(data);
      const step1 = swapAdjacentArryElements(jsonArr);
      const step2 = new_arr_113_0(byteArr, step1, 0, 29);
      const value = to_btoa(step2);
      arr.push(value);
    }
    ;
    return arr;
  }();
  re84["llka3IUd41qGWTiccNsnHJ4cMpxVWVbXPNujnGfcWNjRWZucGJtgGRdarF0="] = function () {
    const byteArr = xoredToByteArr(2417636879, xor_key, 58);
    const jsonArr = jsondataToChaCodeArr([]);
    const step1 = new_arr_2_push(byteArr, jsonArr, 0, 31);
    const step2 = new_arr_256_128(byteArr, step1, 31, 57);
    return to_btoa(step2);
  }();
  re84["ntgy2lUaVlk816PYZx5YHNHXm1kYWmAcF9ys2g=="] = false;
  re84["cNsnm54dMlpVG1ZdPNujG2fXWBzR25vdGJtgGRfdrFo="] = function () {
    const data = {
      "0dub2BjYYBkX3Kyc": [["hdjjXoZZOJtwnCecntgyXVXbVps816PYZ5xYWdHdm1gYnGAZFxqsWA==", "n", "n", true], ["cNsnnJ6bMhlVnVZZPJyj12cdWNvR2ZtYGJ1gWhebrFg=", "s", "s", true], ["49yG2ThZcF0nWJ6bMtlVG1ZYPJyj12cdWNvR2ZtYGJ1gWhebrFg=", "s", "s", true], ["ONxwWiebnl0y2VUcVhs8nKPXZx1Y29HZm1gYnWBaF5usWA==", "n", "n", true], ["PNijTGdYWJ3R25vXGBlgWhdYrF0=", "s", "s", true], ["PByjHGfXWFjRW5tZGJxg2xfYrBo=", "o", "u", false]],
      "PByjHWecWFrR3JvYGJ1gWBearFg=": [["hdjjXoZZOJtwnCecntgyXVXbVps816PYZ5xYWdHdm1gYnGAZFxqsWA==", "n", "n", true], ["cNsnnJ6bMhlVnVZZPJyj12cdWNvR2ZtYGJ1gWhebrFg=", "s", "s", true], ["49yG2ThZcF0nWJ6bMtlVG1ZYPJyj12cdWNvR2ZtYGJ1gWhebrFg=", "s", "s", true], ["ONxwWiebnl0y2VUcVhs8nKPXZx1Y29HZm1gYnWBaF5usWA==", "n", "n", true], ["PNijTGdYWJ3R25vXGBlgWhdYrF0=", "s", "s", true], ["PByjHGfXWFjRW5tZGJxg2xfYrBo=", "o", "u", false]]
    };
    const byteArr = xoredToByteArr(1506186811, xor_key, 34);
    const jsonArr = jsondataToChaCodeArr(data);
    const step1 = new_arr_113_0(byteArr, jsonArr, 0, 31);
    const step2 = new_arr_8_225(step1, byteArr, 31);
    const step3 = new_arr_8_225(step2, byteArr, 32);
    return to_btoa(step3);
  }();
  re84["ONxwGydZnpkyWlWcVts816McZ9tYm9Edm1oYm2DYF5msXQ=="] = function () {
    function encToString(data) {
      const byteArr = xoredToByteArr(215464049, xor_key, 56);
      const jsonArr = jsondataToChaCodeArr(data);
      const step1 = new_arr_2_push(byteArr, jsonArr, 0, 29);
      const step2 = reverse_arr(step1);
      const step3 = new_arr_113_0(byteArr, step2, 29, 55);
      return to_btoa(step3);
    }
    ;
    function getToStringRet(func) {
      const list = {};
      list["YBwXHaxe"] = encToString('function');
      list["MptVG1ZZPNmj12daWJvRHZucGNdg3BcdrNs="] = encToString(func['toStr']["replace"](func["name"], "")["length"]);
      list["hVnjm4bXOBtwmyfZnpwyWlXcVh0826PXZ9dYHdFbm1kYnGBYF1qsmQ=="] = list["MptVG1ZZPNmj12daWJvRHZucGNdg3BcdrNs="];
      list["o9lnWlib0R2bnBjXYNwXHazb"] = encToString(func["toStr"]["replace"](func["name"], "")["slice"](-21)["replace"](gi, "$1" + gV)["replace"](NS, gV + "$1"));
      list["cJsn2Z6cMlpV3FYdPNuj12fXWB3RW5tZGJxgWBdarJk="] = list["o9lnWlib0R2bnBjXYNwXHazb"];
      list["GFtgWRebrFg="] = encToString(func["name"]["slice"](-21)["replace"](gi, "$1" + gV)["replace"](NS, gV + "$1"));
      return encToString(list);
    }
    ;
    let obj = {};
    obj.VZtW2Tyco1pn3Fgd0dub1xjXYB0Xmayb = getToStringRet({
      'toStr': 'function toString() { [native code] }',
      'name': 'toString'
    });
    obj["npkyXlXZVlo8WqObZx1YnNHXm9wY22CbF5qs3A=="] = getToStringRet({
      'toStr': 'function stringify() { [native code] }',
      'name': 'stringify'
    });
    obj["JZzIHc/bBVrIHJbYGpyFWePchtc4GXAdJ16eWTKcVdtWHDwco5xnm1jX0dub3RgdYNcX2axZ"] = getToStringRet({
      'toStr': 'function getOwnPropertyDescriptor() { [native code] }',
      'name': 'getOwnPropertyDescriptor'
    });
    obj["WBvRWJsbGNdg2BeZrJs="] = getToStringRet({
      'toStr': 'function call() { [native code] }',
      'name': 'call'
    });
    obj["ZxtYXtEcmxwY12BYF5msmw=="] = getToStringRet({
      'toStr': 'function apply() { [native code] }',
      'name': 'apply'
    });
    obj["WBnRWpubGNdgmBeZrJs="] = getToStringRet({
      'toStr': 'function bind() { [native code] }',
      'name': 'bind'
    });
    obj.J1uenDJYVRxWWDwdo9dn2VhZ0Rub1xiYYNkX3axZ = getToStringRet({
      'toStr': 'function getParameter() { [native code] }',
      'name': 'getParameter'
    });
    obj["Vl48WaOcZx1YHdGYm1gYHWDXF9msWQ=="] = getToStringRet({
      'toStr': 'function getBattery() { [native code] }',
      'name': 'getBattery'
    });
    obj["MtlVmFZdPBmjWWdZWNfR25sbGJtg3BfYrNs="] = getToStringRet({
      'toStr': 'function debug() { [native code] }',
      'name': 'debug'
    });
    obj["ONxwWydZnh0yWlUZVtc826NYZ9dYG9Fbm1kYnGDbF9isGg=="] = getToStringRet({
      'toStr': 'function () { [native code] }',
      'name': ''
    });
    return encToString(obj);
  }();
  re84["hZvj3IZaONtwXScdnhsyG1UcVts8WaPXZ15YHNHbmx0Y22AdFxysnA=="] = function () {
    const data = [["cNsnnJ6bMhlVnVZZPJyj12cdWNvR2ZtYGJ1gWhebrFg=", "WNnRWpubGB1gGhebrNs="], ["49yGHDhZcBUnXp5bMllVW1ZaPJyj12cdWNvR2ZtYGJ1gWhebrFg=", "WNnRWpubGB1gGhebrNs="], ["49yG2ThZcF0nWJ6bMtlVG1ZYPJyj12cdWNvR2ZtYGJ1gWhebrFg=", "WNnRWpubGB1gGhebrNs="], ["npsy2VWcVlo81KMdZx1Y29Ebm9cYmGDZF92sWQ==", "WNnRWpubGB1gGhebrNs="], ["nhsyXlUcVhw816NYZ5tY2dGcm1oY3GAdFx2s2w==", "WNnRWpubGB1gGhebrNs="], ["k14ZmyXYyJzPWQVdyJyWmxrYhdDj24acOFlw3SdYnpwyGVUaVlg8nKPXZx1Y29HZm1gYnWBaF5usWA==", "WNnRWpubGB1gGhebrNs="], ["hlk4nHBZJx2eWDJbVVhWnDwdoxRn2VhZ0Rub1xiYYNkX3axZ", "WNnRWpubGB1gGhebrNs="], ["lpwaXoVb49uGUzhZcNgnWZ6dMlpVGVZZPJyj12cdWNvR2ZtYGJ1gWhebrFg=", "WNnRWpubGB1gGhebrNs="], ["GtyF2+Obhtw4WnBaJ9yenDJbVRxWWTyco9dnHVjb0dmbWBidYFoXm6xY", "WNnRWpubGB1gGhebrNs="]];
    const byteArr = xoredToByteArr(**********, xor_key, 3);
    const jsonArr = jsondataToChaCodeArr(data);
    const newArr = new_arr_none(jsonArr, byteArr, 0);
    const step1 = new_arr_8_225(newArr, byteArr, 1);
    return to_btoa(step1);
  }();
  re84["Z11YW9Ham9wYWWDYF9isGg=="] = function () {
    function increase(data) {
      var dataStr = "" + data;
      for (let i = 0; i < dataStr["length"]; i++) {
        Az = Az >>> 8 ^ AV[(Az ^ dataStr["charCodeAt"](i)) & 255];
      }
      ;
    }
    ;
    increase(xor_key);
    increase('**********');
    increase(env.navigator["userAgent"]);
    increase(env.navigator["language"]);
    increase(env.screen["width"]);
    increase(env.screen["height"]);
    increase(env.navigator["plugins"]);
    increase(window.own_property_names);
    const num = (Az ^ -1) >>> 0;
    const byteArr = xoredToByteArr(3231912067, xor_key, 49);
    const jsonArr = jsondataToChaCodeArr(num);
    const step1 = new_arr_256_128(byteArr, jsonArr, 0, 22);
    const step2 = swapAdjacentArryElements(step1);
    const step3 = new_arr_2_push(byteArr, step2, 22, 48);
    const step4 = swapAdjacentArryElements(step3);
    return to_btoa(step4);
  }();
  re84["WJvRWpvbGJxg3BedrFk="] = function () {
    const byteArr = xoredToByteArr(3510753592, xor_key, 71);
    const jsonArr = jsondataToChaCodeArr("beta");
    const step1 = swapAdjacentArryElements(jsonArr);
    const step2 = new_arr_113_0(byteArr, step1, 0, 28);
    const step3 = new_arr_2_push(byteArr, step2, 28, 45);
    const step4 = new_arr_113_0(byteArr, step3, 45, 70);
    return to_btoa(step4);
  }();
  re84["Z9tYm9Hcm1oYnWBaF5ysWQ=="] = function () {
    const byteArr = xoredToByteArr(1273776091, xor_key, 21);
    const jsonArr = jsondataToChaCodeArr("UTEmXAIttq9q71l5NIq91zXK8vjo5jO5x1Qo4wt49EZvG0nuf008BQ==");
    const step1 = new_arr_8_225(jsonArr, byteArr, 0);
    const step2 = reverse_arr(step1);
    const step3 = new_arr_256_128(byteArr, step2, 1, 20);
    return to_btoa(step3);
  }();
  function encrypt_reese84() {
    const byteArr = xoredToByteArr(1740574759, xor_key, 34);
    const jsonArr = jsondataToChaCodeArr(re84);
    const step1 = new_arr_113_0(byteArr, jsonArr, 0, 31);
    const step2 = new_arr_8_225(step1, byteArr, 31);
    const step3 = new_arr_8_225(step2, byteArr, 32);
    const p = to_btoa(step3);
    return JSON.stringify({
      'ua': env.navigator.userAgent,
      "p": p,
      "st": 1698421389,
      "sr": **********,
      "cr": xor_key,
      "og": 1
    });
  }
  ;
  return encrypt_reese84();
}
;