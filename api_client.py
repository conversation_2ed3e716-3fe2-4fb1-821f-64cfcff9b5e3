from curl_cffi import requests
# from config import load_config # Removed import
import json
from datetime import datetime
import random # 新增导入
import string # 新增导入
import time # 新增导入
import os # 导入os模块用于文件路径操作
import threading # Import threading for Lock

# 新增：getBookingCart GraphQL 查询
GET_BOOKING_CART_QUERY = """
query getBookingCart {
  getBookingCart {
    originalResponse
    __typename
  }
}
"""

class VAApiClient:
    def __init__(self):
        # print("[VAApiClient DEBUG] Initializing...") # Early debug
        # self.config = load_config() # Ensure this doesn't cause issues if config is missing
        self.session = None # Initialize session attribute
        self.proxies = []
        self.use_proxy = False
        self.current_proxy_index = -1
        self.token = None
        self.execution_id = None
        self.user_agent_override = None # For specific UA needs beyond impersonation
        # print("[*] VAApiClient: Attempting to initialize curl_cffi session...")
        try:
            # Use curl_cffi session with impersonate
            self.session = requests.Session(impersonate="chrome101")
            # print(f"[*] VAApiClient: Successfully initialized curl_cffi session. Type: {type(self.session)}, Impersonation: chrome101")
            # Optionally, set a default User-Agent if not relying solely on impersonate or if UA override is not yet set.
            # self.session.headers['User-Agent'] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36"
            # print(f"[*] VAApiClient User-Agent on init: {self.session.headers.get('User-Agent')}")
        except Exception as e_init_session:
            error_message = f"[!!!] VAApiClient: CRITICAL - Failed to initialize curl_cffi session: {e_init_session}"
            print(error_message)
            # Potentially re-raise or handle so the instance is not used if session is critical
            # self.session = None # Ensure session is None if init fails - No longer needed if we raise
            raise RuntimeError(error_message) from e_init_session

        self.session_lock = threading.Lock()
        self.base_url = "https://book.virginaustralia.com/api/graphql"
        self.initial_ibeopentoken_value = None # DEPRECATED: Superseded by explicit_cookies/auth_data model
        self.saved_dcsessionid = None # DEPRECATED: Superseded by explicit_cookies/auth_data model
        self.saved_dcsessionid_domain = None # DEPRECATED
        self.saved_dcsessionid_path = None # DEPRECATED

        self.shopping_basket_hash_code = None # 在 book_flight 中设置
        self.current_passengers_info = None # 在 book_flight 中设置
        self.confirmed_cash_amount = None
        self.confirmed_cash_currency = None
        self.confirmed_award_amount = None
        self.device_fingerprint_id = None

        self.default_user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36"
        if self.session: # Set default UA based on impersonation if successful
             self.session.headers["User-Agent"] = self.default_user_agent

        self.load_proxies()
        if self.proxies:
            # print(f"[*] VAApiClient: Proxies loaded ({len(self.proxies)}). Initial proxy: {self.proxies[self.current_proxy_index].get('http') if self.current_proxy_index != -1 else 'None Set'}")
            pass # Logging already done by load_proxies
        else:
            # print("[*] VAApiClient: No proxies loaded or proxy file not found.")
            pass
        print(f"[*] VAApiClient User-Agent on init: {self.session.headers.get('User-Agent', 'Default Requests UA')}")
        if self.user_agent_override: print(f"[*] VAApiClient User-Agent will be overridden by: {self.user_agent_override}")

    def load_proxies(self):
        print("[*] VAApiClient: Attempting to load proxies...")
        self.proxies = [] # Ensure proxies list is reset before loading
        try:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            possible_paths = [
                os.path.join(current_dir, "proxy_config.json"),
                os.path.join(current_dir, "..", "proxy_config.json"),
                "proxy_config.json"
            ]
            proxy_config_path = next((path for path in possible_paths if os.path.exists(path)), None)
            if not proxy_config_path:
                print("[!] VAApiClient: Proxy config file not found.")
                return

            with open(proxy_config_path, 'r', encoding='utf-8') as f:
                proxy_list_from_file = json.load(f)

            if not isinstance(proxy_list_from_file, list):
                print(f"[!] VAApiClient: proxy_config.json is not a JSON list. Loaded type: {type(proxy_list_from_file)}")
                return

            for idx, proxy_entry_str in enumerate(proxy_list_from_file):
                if not (proxy_entry_str and isinstance(proxy_entry_str, str)):
                    print(f"    [PROXY LOAD WARN] Entry {idx} is not a valid string: {proxy_entry_str}")
                    continue
                parts = proxy_entry_str.strip().split(':') # Added strip()
                proxy_url = None
                if len(parts) == 2:
                    host, port = parts[0], parts[1]
                    proxy_url = f"http://{host}:{port}"
                    # print(f"    [PROXY LOAD] Parsed host:port (Entry {idx}): {proxy_url} from '{proxy_entry_str}'")
                elif len(parts) == 4:
                    host, port, user, passwd = parts[0], parts[1], parts[2], parts[3]
                    # Log parsed credentials carefully
                    # print(f"    [PROXY LOAD] (Entry {idx}) Original: '{proxy_entry_str}' -> Parsed User: '{user}', Pass: '{passwd[:2]}***{passwd[-2:] if len(passwd)>3 else ''}', Host: {host}, Port: {port}")
                    proxy_url = f"http://{user}:{passwd}@{host}:{port}"
                else:
                    print(f"    [PROXY LOAD WARN] Entry {idx} '{proxy_entry_str}' has unexpected format (parts: {len(parts)}). Skipping.")
                    continue
                if proxy_url: self.proxies.append({"http": proxy_url, "https": proxy_url})

            if self.proxies:
                print(f"[*] VAApiClient: Successfully loaded {len(self.proxies)} proxies from {proxy_config_path}.")
            else:
                print("[!] VAApiClient: No valid proxies parsed from config file or file was empty/invalid.")
        except FileNotFoundError:
            print("[!] VAApiClient: proxy_config.json not found (FileNotFoundError).")
        except json.JSONDecodeError as e_json:
            print(f"[!] VAApiClient: Error decoding proxy_config.json: {e_json}")
        except Exception as e:
            print(f"[!] VAApiClient: Unexpected error loading proxies: {e}")
            import traceback; traceback.print_exc()

    def set_proxy(self, index=None):
        if not self.proxies:
            # print("[VAApiClient] Proxy list is empty, cannot set proxy.") # Can be noisy if called often
            self.session.proxies = {}
            self.current_proxy_index = -1
            return False

        if index is not None:
            if 0 <= index < len(self.proxies):
                self.current_proxy_index = index
            else:
                print(f"[!] VAApiClient: Proxy index {index} out of range. Using random.")
                self.current_proxy_index = random.randint(0, len(self.proxies) - 1)
        else: # Random selection if no index
            if len(self.proxies) == 1:
                self.current_proxy_index = 0
            else: # More than one proxy, try to pick a different one if one was already set
                if self.current_proxy_index != -1 and len(self.proxies) > 1:
                    new_idx = self.current_proxy_index
                    while new_idx == self.current_proxy_index: # Ensure it's different
                        new_idx = random.randint(0, len(self.proxies) - 1)
                    self.current_proxy_index = new_idx
                else: # First time or only one proxy
                    self.current_proxy_index = random.randint(0, len(self.proxies) - 1)

        self.session.proxies = self.proxies[self.current_proxy_index]
        active_proxy_url = list(self.session.proxies.values())[0]
        print(f"[*] VAApiClient: Proxy set to index {self.current_proxy_index} - {active_proxy_url}")
        return True

    def rotate_proxy(self):
        """随机轮换代理"""
        if not self.proxies:
            print("[!] 代理列表为空，无法轮换代理")
            return False

        # 随机选择一个不同的代理
        if len(self.proxies) > 1:
            # 如果有多个代理，确保选择一个不同的代理
            new_index = self.current_proxy_index
            while new_index == self.current_proxy_index:
                new_index = random.randint(0, len(self.proxies) - 1)
            self.current_proxy_index = new_index
        else:
            # 如果只有一个代理，仍然使用它
            self.current_proxy_index = 0

        # 设置代理
        self.session.proxies = self.proxies[self.current_proxy_index]
        print(f"[*] 已随机轮换到新代理: {list(self.session.proxies.values())[0]}")
        return True

    def set_cookies_from_list(self, cookies_list):
        """直接从列表设置 session cookies"""
        with self.session_lock: # Acquire lock for the whole method
            self.session.cookies.clear() # 清空旧 cookies
            if not cookies_list:
                print("[!] 传入的 cookie 列表为空。")
                return False
            try:
                # 先收集所有cookie，按名称分组，确保每个名称只有一个cookie
                cookie_dict = {}
                for cookie in cookies_list:
                    cookie_name = cookie['name']
                    # 如果有多个同名cookie，使用最后一个
                    cookie_dict[cookie_name] = cookie

                # 设置去重后的cookies
                for cookie_name, cookie in cookie_dict.items():
                    # requests session 需要 domain 参数，如果 cookie 中没有，尝试从 URL 推断或省略
                    cookie_domain = cookie.get('domain')
                    # 确保 domain 不为空字符串
                    if cookie_domain == "":
                        cookie_domain = None
                    # 如果 domain 以 '.' 开头，requests 可能不需要它
                    if cookie_domain and cookie_domain.startswith('.'):
                        cookie_domain = cookie_domain[1:]

                    self.session.cookies.set(
                        cookie['name'],
                        cookie['value'],
                        domain=cookie_domain,
                        path=cookie.get('path', '/') # 提供默认 path
                    )

                print(f"[*] 已从列表加载 {len(cookie_dict)} 个唯一 cookies 到 session (原始列表长度: {len(cookies_list)})。")
                return True
            except Exception as e:
                import traceback
                print(f"[!] 从列表设置 cookie 失败: {e}")
                print(traceback.format_exc()) # 打印详细的错误堆栈信息
                return False

    def set_device_fingerprint_id(self, device_fingerprint_id):
        """设置设备指纹ID，用于支付请求"""
        if not device_fingerprint_id:
            print("[!] 设备指纹ID不能为空")
            return False

        self.device_fingerprint_id = device_fingerprint_id
        print(f"[*] 已设置设备指纹ID: {device_fingerprint_id[:10]}...")
        return True

    # 移除 reload_cookies 方法
    # def reload_cookies(self): ...

    def _graphql_request(self, operation_name, variables, query,
                         task_token=None, task_execution_id=None,
                         explicit_cookies_for_request: dict = None,
                         task_user_agent=None,
                         use_specific_proxy=None, # This is a proxy dict for self.session.proxies
                         return_headers=False):
        # 简化调试输出
        with self.session_lock:
            current_token_for_auth_header = task_token if task_token is not None else self.token
            current_execution_id_for_header = task_execution_id if task_execution_id is not None else self.execution_id

            effective_user_agent = task_user_agent or self.user_agent_override or self.default_user_agent
            if self.session and not effective_user_agent:
                effective_user_agent = self.session.headers.get("User-Agent")
            elif not effective_user_agent:
                effective_user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.0.0 Safari/537.36"

            # Store original cookies to restore later if needed
            try:
                original_session_cookies = dict(self.session.cookies)
            except:
                original_session_cookies = {}
            original_session_proxies = self.session.proxies.copy()
            self.session.cookies.clear() # Crucial: start with a clean slate for cookies for this request

            applied_cookies_names = []
            if explicit_cookies_for_request and isinstance(explicit_cookies_for_request, dict):
                for name, value in explicit_cookies_for_request.items():
                    domain_for_cookie = None; path_for_cookie = '/'
                    if name.lower() == 'reese84': domain_for_cookie = "book.virginaustralia.com"
                    # For bookingAirSearch, we ONLY want reese84 from explicit_cookies.
                    # Other cookies like ibeopentoken, dcsessionid should NOT be added here if it's a search.
                    # This logic is implicitly handled if explicit_cookies_for_request *only* contains reese84 for search.

                    cookie_value_to_set = value
                    cookie_domain_to_set = domain_for_cookie
                    cookie_path_to_set = path_for_cookie

                    if isinstance(value, dict):
                        cookie_value_to_set = value.get('value')
                        cookie_domain_to_set = value.get('domain', domain_for_cookie)
                        cookie_path_to_set = value.get('path', path_for_cookie)

                    if cookie_value_to_set is not None:
                        # 确保domain和path不为None，curl_cffi不接受None值
                        safe_domain = cookie_domain_to_set if cookie_domain_to_set is not None else domain_for_cookie
                        safe_path = cookie_path_to_set if cookie_path_to_set is not None else path_for_cookie

                        # 如果仍然为None，使用默认值
                        if safe_domain is None:
                            safe_domain = ".virginaustralia.com"
                        if safe_path is None:
                            safe_path = "/"

                        self.session.cookies.set(name, cookie_value_to_set, domain=safe_domain, path=safe_path)
                        applied_cookies_names.append(name)

                if applied_cookies_names: print(f"[*] Applied {len(applied_cookies_names)} explicit cookies for {operation_name}. Applied: {applied_cookies_names}")
            else:
                print(f"[*] No explicit_cookies_for_request for {operation_name}.")

            # Standard headers, attempt to match demo.py as closely as possible for bookingAirSearch
            standard_headers = {
                "accept": "*/*",
                "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
                "adrum": "isAjax:true",
                "cache-control": "no-cache",
                "content-type": "application/json",
                "origin": "https://book.virginaustralia.com",
                "pragma": "no-cache",
                "priority": "u=1, i",
                "referer": "https://book.virginaustralia.com/dx/VADX/",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "x-sabre-storefront": "VADX",
                # Removed "application-id" for now to align closer with demo.py successful call structure
            }

            if operation_name == "bookingAirSearch":
                # For bookingAirSearch, use headers and UA identical to demo.py successful standalone call
                headers = standard_headers.copy()
                headers["user-agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"
                headers["sec-ch-ua"] = '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"'
                headers["sec-ch-ua-mobile"] = "?0"
                headers["sec-ch-ua-platform"] = '"Windows"'
                # print(f"[*] bookingAirSearch: Using fixed demo.py User-Agent and sec-ch-ua headers.")
                # Authorization and execution headers are correctly omitted for bookingAirSearch by not adding them here.
            else:
                # For other operations, use the effective_user_agent logic (from reese or default)
                headers = standard_headers.copy()
                headers["user-agent"] = effective_user_agent
                # Use sec-ch-ua consistent with impersonate chrome101 if not bookingAirSearch
                headers["sec-ch-ua"] = '" Not A;Brand";v="99", "Chromium";v="101", "Google Chrome";v="101"'
                headers["sec-ch-ua-mobile"] = "?0"
                headers["sec-ch-ua-platform"] = '"Windows"'

                # Add Authorization and execution headers ONLY for non-search operations and if available
                if current_token_for_auth_header:
                    headers["Authorization"] = f"Bearer {current_token_for_auth_header}"
                if current_execution_id_for_header:
                    headers["execution"] = current_execution_id_for_header

            if operation_name == "bookingAirSearch" and "airSearchInput" in variables: # Date headers for search
                try:
                    s_date = datetime.strptime(variables["airSearchInput"]["itineraryParts"][0]["when"]["date"], "%Y-%m-%d")
                    headers["activeMonth"] = s_date.strftime("%m-%d-%Y")
                    headers["date"] = s_date.strftime("%m-%d-%Y")
                except Exception: pass

            payload_dict = {"operationName": operation_name, "variables": variables, "query": query}
            if "extensions" not in payload_dict: payload_dict["extensions"] = {}

            # Convert payload to string for `data` param if `json` param is not used by curl_cffi for this content-type
            # curl_cffi's `json` param works like requests, handles content-type and serialization.
            # However, demo.py used `data=json.dumps(...)`. Let's stick to `json=` for curl_cffi.requests.
            # If issues, can switch to `data=json.dumps(payload_dict, separators=(',', ':'))` and ensure `content-type` is `application/json`.

            timeout = 60 if operation_name == "bookingPurchase" else 30
            active_proxy_for_log = "None (Direct)" # Default log message for proxy

            if use_specific_proxy is not None and isinstance(use_specific_proxy, dict):
                # Caller explicitly provided a proxy setting (can be {} for no proxy)
                self.session.proxies = use_specific_proxy
                if use_specific_proxy: # If not an empty dict
                    active_proxy_for_log = use_specific_proxy.get('http') or use_specific_proxy.get('https') or "SpecificProxyInvalidFormat"
                # If use_specific_proxy is {}, active_proxy_for_log remains "None (Direct)"
            elif operation_name == "bookingAirSearch": # For bookingAirSearch, explicitly NO proxy unless specified by use_specific_proxy
                # This means high_concurrency_crawler should pass {} if it wants direct for search_flights
                # which it is currently doing.
                # If use_specific_proxy was None (default), then no proxy here.
                self.session.proxies = {} # Default to no proxy for search if not specified
                active_proxy_for_log = "None (Direct) - Search Default"
            elif self.use_proxy and self.proxies: # For non-search operations, use internal rotation if enabled
                self.set_proxy() # Rotate proxy from internal list
                if self.session.proxies:
                    active_proxy_for_log = self.session.proxies.get('http') or self.session.proxies.get('https') or "RotatedProxyInvalidFormat"
            else:
                # Default to no proxy if no specific instruction and not a search op eligible for rotation
                self.session.proxies = {}

            # 发送请求
            resp = None
            try:
                print(f"[API] 发送 {operation_name} 请求到 {self.base_url}...")
                resp = self.session.post(self.base_url, headers=headers, json=payload_dict, timeout=timeout)
                print(f"[API] {operation_name} 响应状态: {resp.status_code if resp else 'None'}")
            except Exception as e:
                print(f"[!!!] _graphql_request: Exception during {operation_name} (Proxy: {active_proxy_for_log or 'None'}): {type(e).__name__} - {e}")
                # Ensure original state is restored even if post fails, then re-raise
                self.session.cookies.clear()
                self.session.cookies.update(original_session_cookies)
                self.session.proxies = original_session_proxies
                raise

            # Process response if request was successful (no exception from post)
            response_json = None
            http_error_raised = None # Flag to see if http_err was raised by raise_for_status
            try:
                if resp is None: # Should only happen if post() itself failed and raised, caught above
                    raise requests.errors.RequestException("Response object is None after POST, indicates prior error.")
                resp.raise_for_status() # Check for HTTP errors 4xx/5xx
                response_json = resp.json() # Try to parse JSON

            except Exception as http_err:  # curl_cffi uses different exception structure
                http_error_raised = http_err # Store the error
                err_content = resp.text if resp is not None else "No response object for HTTPError"
                status_code = resp.status_code if resp is not None else "N/A"
                print(f"[!!!] _gql HTTP error for {operation_name} ({status_code}): {http_err}. Response: {err_content[:300]}...")
                try:
                    response_json = resp.json() if resp is not None else {"errors": [{"message": f"HTTP error {status_code}"}]}
                except json.JSONDecodeError:
                    response_json = {"errors": [{"message": f"HTTP error {status_code}", "details": err_content}]}
                # We will re-raise http_error_raised later, after finally block

            except json.JSONDecodeError as json_err:
                status_code_json = resp.status_code if resp is not None else "N/A"
                resp_text_json = resp.text if resp is not None else "No response object for JSONDecodeError"
                print(f"[!!!] _gql JSON decode error for {operation_name} (Status: {status_code_json}): {json_err}. Text: {resp_text_json[:300]}...")
                response_json = {"errors": [{"message": "JSON decode error", "details": resp_text_json[:300]}]}
                # This is a successful HTTP status but bad JSON. We might not want to raise an exception for the caller here,
                # but return the error structure so caller can decide.

            except Exception as generic_resp_err: # Catch other unexpected errors during response processing
                 status_code_gen = resp.status_code if resp is not None else "N/A"
                 resp_text_gen = resp.text if resp is not None else "No response object for generic error"
                 print(f"[!!!] _gql Generic error processing response for {operation_name} (Status: {status_code_gen}): {generic_resp_err}. Text: {resp_text_gen[:300]}...")
                 response_json = {"errors": [{"message": f"Generic response processing error: {generic_resp_err}", "details": resp_text_gen[:300]}]}
                 # This could be more severe, consider re-raising or specific handling.

            # The session_lock is for the entire _graphql_request method due to `with self.session_lock:` at the start.
            # The original_session_cookies and original_session_proxies are restored by the `with` statement's exit.
            # So, no explicit finally block needed here just for restoring those.

            # 恢复原始session状态
            self.session.cookies.clear()
            self.session.cookies.update(original_session_cookies)
            self.session.proxies = original_session_proxies

            if http_error_raised: # If raise_for_status() triggered an HTTPError, re-raise it now
                raise http_error_raised

            if return_headers:
                return response_json, resp.headers if resp is not None else None
            return response_json

    def _call_gql(self, operation_name, variables, query, auth_data, explicit_cookies=None, use_specific_proxy=None, return_headers=False):
        return self._graphql_request(operation_name, variables, query,
                                     task_token=auth_data.get('token'),
                                     task_execution_id=auth_data.get('execution_id'),
                                     explicit_cookies_for_request=explicit_cookies,
                                     task_user_agent=auth_data.get('user_agent'),
                                     use_specific_proxy=use_specific_proxy, # Pass it down
                                     return_headers=return_headers)

    def login(self):
        # token已通过playwright获取，此处无需登录
        print("[!] login 方法已弃用。Token和Execution ID应通过外部方式设置。")
        return False

    def search_flights(self, origin, destination, date, cabin="Business", award_booking=True,
                       auth_data=None, explicit_cookies=None, use_specific_proxy=None):
        auth_data = auth_data or {}; explicit_cookies = explicit_cookies or {}
        query = """query bookingAirSearch($airSearchInput: CustomAirSearchInput) { bookingAirSearch(airSearchInput: $airSearchInput) { originalResponse __typename } }"""
        variables = {"airSearchInput": {"cabinClass": cabin, "awardBooking": award_booking, "promoCodes": [], "searchType": "BRANDED", "itineraryParts": [{"from": {"useNearbyLocations": False, "code": origin}, "to": {"useNearbyLocations": False, "code": destination}, "when": {"date": date}}], "passengers": {"ADT": 1}}}
        return self._call_gql("bookingAirSearch", variables, query, auth_data,
                              explicit_cookies=explicit_cookies, use_specific_proxy=use_specific_proxy)

    def book_flight(self, selected_offer, passengers_info_list: list,
                    award_amount_ref=None, cash_amount_ref=None, cash_currency_ref=None,
                    auth_data=None, explicit_cookies=None, use_specific_proxy=None):
        """
        选择航班、获取购物车、添加行程、更新乘客信息，并准备支付。
        返回: (bool: success, dict_or_str: data_for_payment or error_message, str: error_code or None)
        """
        auth_data = auth_data or {}; explicit_cookies = explicit_cookies or {}

        try:
            # 存储传入的金额参考
            self.confirmed_award_amount = int(award_amount_ref) if award_amount_ref is not None else None
            self.confirmed_cash_amount = float(cash_amount_ref) if cash_amount_ref is not None else None
            self.confirmed_cash_currency = cash_currency_ref
            print(f"[*] book_flight: 接收到的参考金额 - Award: {self.confirmed_award_amount}, Cash: {self.confirmed_cash_amount} {self.confirmed_cash_currency}")

            if not isinstance(selected_offer, dict):
                print("[!] selected_offer 不是一个有效的字典类型。")
                return False, "selected_offer is not a valid dictionary.", "INVALID_OFFER"

            # 步骤 1: 提取 shoppingBasketHashCode
            current_shopping_basket_hash_code = selected_offer.get('shoppingBasketHashCode')
            if not current_shopping_basket_hash_code:
                try:
                    brand_offers = selected_offer.get('brandedResults', {}).get('itineraryPartBrands', [{}])[0].get('brandOffers', [])
                    if brand_offers and isinstance(brand_offers, list) and len(brand_offers) > 0:
                        current_shopping_basket_hash_code = brand_offers[0].get('shoppingBasketHashCode')
                        if current_shopping_basket_hash_code:
                            print(f"[*] 从 brandOffers 中提取到 shoppingBasketHashCode: {current_shopping_basket_hash_code}")
                except Exception as e_extract:
                     print(f"[!] 尝试从 brandOffers 提取 shoppingBasketHashCode 时出错: {e_extract}")

            if not current_shopping_basket_hash_code:
                err_msg = "无法从 selected_offer 中提取 'shoppingBasketHashCode'"
                print(f"[!] {err_msg}")
                print(f"[*] selected_offer (partial): {str(selected_offer)[:500]}...")
                return False, err_msg, "NO_HASH_CODE"

            self.shopping_basket_hash_code = str(current_shopping_basket_hash_code)
            print(f"[*] 选定航班的 shoppingBasketHashCode: {self.shopping_basket_hash_code}")

            # 步骤 2: 添加行程
            print(f"[*] 准备调用 add_itinerary with hash: {self.shopping_basket_hash_code}")
            add_success, add_data_or_msg, add_err_code = self.add_itinerary(self.shopping_basket_hash_code, auth_data, explicit_cookies, use_specific_proxy)
            if not add_success:
                print(f"[!] 添加行程失败: {add_data_or_msg}")
                return False, f"Add itinerary failed: {add_data_or_msg}", add_err_code

            # add_itinerary 成功时返回的是 bookingAddItinerary 的数据部分
            if isinstance(add_data_or_msg, dict):
                original_response_itinerary = add_data_or_msg.get("originalResponse", {})
                updated_hash_code = original_response_itinerary.get("shoppingBasketHashCode")
                if updated_hash_code and str(updated_hash_code) != self.shopping_basket_hash_code:
                    print(f"[*] shoppingBasketHashCode 从 {self.shopping_basket_hash_code} 更新为 {updated_hash_code} (来自 addItinerary 响应)")
                    self.shopping_basket_hash_code = str(updated_hash_code)

            # 步骤 3: 获取购物车状态
            print(f"[*] 添加行程后，准备调用 _get_booking_cart")
            cart_success, cart_msg, cart_err_code = self._get_booking_cart(auth_data, explicit_cookies, use_specific_proxy)
            if not cart_success:
                print(f"[!] _get_booking_cart (after add_itinerary) 失败: {cart_msg}")
                return False, f"Get cart (after add_itinerary) failed: {cart_msg}", cart_err_code
            print(f"[*] _get_booking_cart (after add_itinerary) 成功.")

            # 步骤 4: 更新乘客信息
            print("[*] 更新乘客信息中...")
            update_success, update_msg, update_err_code = self.update_passengers(passengers_info_list, auth_data, explicit_cookies, use_specific_proxy)
            if not update_success:
                print(f"[!] 更新乘客信息失败: {update_msg}")
                return False, f"Update passengers failed: {update_msg}", update_err_code

            # 步骤 5: 确认支付详情
            if self.confirmed_award_amount is None:
                err_msg = "book_flight: 确认支付详情前，积分金额 (self.confirmed_award_amount) 未设置。"
                print(f"[!] {err_msg}")
                return False, err_msg, "CONFIRM_AWARD_NOT_SET_PRE_CALL"

            print(f"[*] 调用 _confirm_award_payment_details 确认 {self.confirmed_award_amount} 积分 (预设现金 {self.confirmed_cash_amount} {self.confirmed_cash_currency})...")
            confirm_success, confirm_data_or_msg, confirm_err_code = self._confirm_award_payment_details(auth_data, explicit_cookies, use_specific_proxy)
            if not confirm_success:
                print(f"[!] _confirm_award_payment_details 失败: {confirm_data_or_msg}")
                return False, f"Confirm payment details failed: {confirm_data_or_msg}", confirm_err_code

            print(f"[*] 航班选择、乘客信息更新、支付详情确认完成。")
            print(f"[*] 最终确认的支付组合: {self.confirmed_award_amount} 积分 + {self.confirmed_cash_amount} {self.confirmed_cash_currency}")

            self.current_passengers_info = passengers_info_list

            return True, {"message": "Pre-payment steps completed successfully.", "confirmed_payment": {"award": self.confirmed_award_amount, "cash": self.confirmed_cash_amount, "currency": self.confirmed_cash_currency}}, None

        except Exception as e:
            print(f"[!] book_flight 执行过程中发生意外异常: {e}")
            import traceback
            traceback.print_exc()
            return False, f"Exception during pre-payment steps: {str(e)}", "EXCEPTION"

    def add_itinerary(self, flight_selection_id_val: str, auth_data=None, explicit_cookies=None, use_specific_proxy=None):
        """
        将选定的航班添加到行程中。
        参数 flight_selection_id_val: 纯哈希码字符串。
        返回: (bool: success, dict_or_str: response_data or error_message, str: error_code or None)
        """
        auth_data = auth_data or {}; explicit_cookies = explicit_cookies or {}
        print(f"[DEBUG APIClient] add_itinerary called. execution_id: {auth_data.get('execution_id')}, token: {auth_data.get('token')}")
        operation_name = "bookingAddItinerary"
        # 格式化为 "selectFlights=HASHCODE"
        select_flights_payload_str = f"selectFlights={flight_selection_id_val}"
        variables = {"selectFlights": select_flights_payload_str}

        query = """
            query bookingAddItinerary($selectFlights: String!) {
              bookingAddItinerary(selectFlights: $selectFlights) {
                originalResponse
                __typename
              }
            }
        """
        print(f"[*] 发送 bookingAddItinerary 请求, selectFlights: '{select_flights_payload_str}'")
        try:
            response_data = self._call_gql(operation_name, variables, query, auth_data, explicit_cookies, use_specific_proxy)

            # 详细检查响应内容
            booking_add_itinerary_data = response_data.get("data", {}).get("bookingAddItinerary")
            if booking_add_itinerary_data and isinstance(booking_add_itinerary_data, dict):
                original_response_itinerary = booking_add_itinerary_data.get("originalResponse", {})
                itinerary_messages = original_response_itinerary.get("messages", [])
                itinerary_errors = [msg for msg in itinerary_messages if isinstance(msg, dict) and msg.get("type", "").upper() == "ERROR"]

                if itinerary_errors:
                    error_details_itinerary = itinerary_errors[0].get("message", "添加行程操作返回了错误消息")
                    print(f"[!] 添加行程业务逻辑错误 (来自messages): {error_details_itinerary}")
                    return False, error_details_itinerary, "ADD_ITIN_BUSINESS_ERROR"
                elif not original_response_itinerary:
                    print("[!] 添加行程失败，响应中缺少 originalResponse。")
                    return False, "Add itinerary failed, missing originalResponse.", "ADD_ITIN_NO_ORIGINAL_RESPONSE"

                print("[*] 添加行程成功 (基于messages检查)。")
                # 返回原始的 bookingAddItinerary 数据部分，因为 book_flight 可能需要用它更新 shoppingBasketHashCode
                return True, booking_add_itinerary_data, None

            elif response_data.get("errors"):
                gql_errors = response_data.get("errors")
                error_msg = f"bookingAddItinerary GQL errors: {gql_errors}"
                print(f"[!] {error_msg}")
                return False, error_msg, "ADD_ITIN_GQL_ERROR"
            else:
                error_msg = f"'bookingAddItinerary' in response data was null or not a dict. Response: {json.dumps(response_data)}"
                print(f"[!] {error_msg}")
                return False, error_msg, "ADD_ITIN_UNEXPECTED_RESPONSE"

        except Exception as e:
            error_msg = f"调用 bookingAddItinerary 时发生异常: {e}"
            print(f"[!] {error_msg}")
            return False, error_msg, "ADD_ITIN_EXCEPTION"

    def _get_booking_cart(self, auth_data=None, explicit_cookies=None, use_specific_proxy=None):
        """
        发送 getBookingCart 请求以在会话中建立或确认购物车状态。
        返回: (bool: success, str: message_or_error_details, str: error_code or None)
        """
        auth_data = auth_data or {}; explicit_cookies = explicit_cookies or {}
        operation_name = "getBookingCart"
        variables = {}

        query = """
        query getBookingCart {
          getBookingCart {
            originalResponse
            __typename
          }
        }
        """

        print("[*] 发送 getBookingCart 请求...")
        try:
            response_data = self._call_gql(operation_name, variables, query, auth_data, explicit_cookies, use_specific_proxy)

            if response_data and response_data.get("data") and response_data["data"].get("getBookingCart"):
                original_response = response_data["data"]["getBookingCart"].get("originalResponse", {})
                messages = original_response.get("messages", [])

                if not messages:
                    print("[+] getBookingCart 请求成功，购物车状态已更新/确认。")
                    return True, "GetBookingCart successful.", None
                else:
                    error_msg = f"getBookingCart 请求返回了消息: {messages}"
                    print(f"[!] {error_msg}")
                    return False, error_msg, "GET_CART_MESSAGES"
            elif response_data and response_data.get("errors"):
                 gql_errors = response_data.get('errors')
                 error_msg = f"getBookingCart 请求失败 (GraphQL errors): {gql_errors}"
                 print(f"[!] {error_msg}")
                 return False, error_msg, "GET_CART_GQL_ERROR"
            else:
                error_msg = "getBookingCart 请求失败: 未知错误或响应格式不正确"
                print(f"[!] {error_msg}")
                return False, error_msg, "GET_CART_UNEXPECTED_RESPONSE"

        except Exception as e:
            error_msg = f"调用 getBookingCart 时发生异常: {e}"
            print(f"[!] {error_msg}")
            import traceback
            traceback.print_exc()
            return False, error_msg, "GET_CART_EXCEPTION"

    def update_passengers(self, passengers_info_list: list, auth_data=None, explicit_cookies=None, use_specific_proxy=None):
        """
        更新乘客信息。
        返回: (bool: success, str: message_or_error_details, str: error_code or None)
        """
        auth_data = auth_data or {}; explicit_cookies = explicit_cookies or {}

        if not isinstance(passengers_info_list, list):
            passengers_info_list = [passengers_info_list] # Ensure it's a list

        required_keys = ["email", "phone", "first_name", "last_name", "title", "gender", "dob"]
        for i, p_info in enumerate(passengers_info_list):
            if not all(key in p_info for key in required_keys):
                missing = [key for key in required_keys if key not in p_info]
                err_msg = f"乘客 {i+1} 信息缺失以下字段: {', '.join(missing)}"
                print(f"[!] {err_msg}")
                return False, err_msg, "UPDATE_PAX_MISSING_FIELDS"

        contact_email = passengers_info_list[0].get("email")
        contact_phone = passengers_info_list[0].get("phone")
        passengers_gql_list = []
        for i, p_info in enumerate(passengers_info_list):
            passengers_gql_list.append({
                "passengerIndex": i + 1,
                "passengerDetails": {"firstName": p_info.get("first_name"), "lastName": p_info.get("last_name"), "prefix": p_info.get("title")},
                "passengerInfo": {"gender": p_info.get("gender"), "dateOfBirth": p_info.get("dob"), "type": "ADT", "emails": [contact_email], "phones": [{"type": "MOBILE", "number": contact_phone}]},
                "preferences": {"specialPreferences": {"specialRequests": []}, "frequentFlyer": []}
            })
        variables = {
            "contact": {"emails": [contact_email], "phones": [{"type": "MOBILE", "number": contact_phone}]},
            "passengers": passengers_gql_list
        }
        query = """
        mutation bookingUpdatePassengers($contact: ContactDetailsInput, $passengers: [PassengerInput]) {
          bookingUpdatePassengers(contact: $contact, passengers: $passengers) {
            originalResponse
            __typename
          }
        }
        """
        operation_name = "bookingUpdatePassengers"
        print(f"[*] 发送 {operation_name} 请求...")
        try:
            response_data = self._call_gql(operation_name, variables, query, auth_data, explicit_cookies, use_specific_proxy)
            # 详细检查响应内容
            update_pass_data = response_data.get("data", {}).get("bookingUpdatePassengers")
            if update_pass_data and isinstance(update_pass_data, dict):
                original_response_update = update_pass_data.get("originalResponse", {})
                update_messages = original_response_update.get("messages", [])
                update_errors = [msg for msg in update_messages if isinstance(msg, dict) and msg.get("type", "").upper() == "ERROR"]

                if update_errors:
                    error_details_update = update_errors[0].get("message", "更新乘客信息操作返回了错误消息")
                    print(f"[!] 更新乘客信息业务逻辑错误 (来自messages): {error_details_update}")
                    return False, error_details_update, "UPDATE_PAX_BUSINESS_ERROR"
                elif not original_response_update:
                    print("[!] 更新乘客信息失败，响应中缺少 originalResponse。")
                    return False, "Update passengers failed, missing originalResponse.", "UPDATE_PAX_NO_ORIGINAL_RESPONSE"

                print("[*] 更新乘客信息成功 (基于messages检查)。")
                return True, "Update passengers successful.", None

            elif response_data.get("errors"):
                error_msg = f"update_passengers GQL errors: {response_data.get('errors')}"
                print(f"[!] {error_msg}")
                return False, error_msg, "UPDATE_PAX_GQL_ERROR"
            else:
                error_msg = f"update_passengers: 'bookingUpdatePassengers' was null or not dict. Response: {json.dumps(response_data)}"
                print(f"[!] {error_msg}")
                return False, error_msg, "UPDATE_PAX_UNEXPECTED_RESPONSE"

        except Exception as e:
            error_msg = f"调用 bookingUpdatePassengers 时发生异常: {e}"
            print(f"[!] {error_msg}")
            return False, error_msg, "UPDATE_PAX_EXCEPTION"

    def _confirm_award_payment_details(self, auth_data=None, explicit_cookies=None, use_specific_proxy=None):
        """
        调用 bookingPaymentDetails mutation 来确认用户选择的积分支付金额。
        更新 self.confirmed_cash_amount 和 self.confirmed_cash_currency。
        返回: (bool: success, str_or_dict: message or data_with_amounts, str: error_code or None)
        """
        auth_data = auth_data or {}; explicit_cookies = explicit_cookies or {}

        award_amount_to_use_in_gql = self.confirmed_award_amount
        if award_amount_to_use_in_gql is None:
            print("[!] _confirm_award_payment_details: self.confirmed_award_amount 未设置! 这是一个关键错误。")
            return False, "Internal error: Award amount not set for confirmation.", "CONFIRM_AWARD_NOT_SET"

        print(f"[*] --- 开始执行 _confirm_award_payment_details (确认积分支付: {award_amount_to_use_in_gql}) ---")
        operation_name = "bookingPaymentDetails"
        query = """
        mutation bookingPaymentDetails($paymentType: String!, $paymentDetailsInput: PaymentDetailsInput) {
          b2cPaymentDetails: bookingPaymentDetails(
            paymentType: $paymentType
            paymentDetailsInput: $paymentDetailsInput
          ) {
            originalResponse
            __typename
          }
        }
        """
        variables = {
            "paymentType": "AWARD",
            "paymentDetailsInput": {
                "awardDetailsSelection": {
                    "fareAmount": {
                        "amount": int(award_amount_to_use_in_gql),
                        "currency": "FFCURRENCY"
                    },
                    "ancillaryToggleSelections": []
                }
            }
        }
        try:
            print(f"[*] bookingPaymentDetails 请求 variables: {json.dumps(variables, indent=2)}")
            response_data = self._call_gql(operation_name, variables, query, auth_data, explicit_cookies, use_specific_proxy)

            b2c_payment_details = response_data.get("data", {}).get("b2cPaymentDetails", {})
            original_response = b2c_payment_details.get("originalResponse", {})

            if not original_response:
                err_msg = "确认支付详情失败：响应中缺少 originalResponse。"
                print(f"[!] {err_msg} Response: {json.dumps(response_data)}")
                return False, err_msg, "CONFIRM_PAYMENT_NO_ORIGINAL_RESPONSE"

            # 简化处理，假设成功
            print(f"[*] 支付详情确认成功")

            # 尝试从响应中更新现金部分
            try:
                price_info = original_response.get("price", {})
                total_price_alternatives = price_info.get("total", {}).get("alternatives", [])
                if total_price_alternatives and isinstance(total_price_alternatives[0], list) and total_price_alternatives[0]:
                    for item_price in total_price_alternatives[0]:
                        if item_price.get("currency") != "FFCURRENCY":
                            self.confirmed_cash_amount = float(item_price.get("amount"))
                            self.confirmed_cash_currency = item_price.get("currency")
                            break
            except Exception as e_parse:
                print(f"[!] 解析支付详情响应时出错: {e_parse}。将使用预设值。")

            return True, {"message": "Payment details confirmed.", "award": self.confirmed_award_amount, "cash": self.confirmed_cash_amount, "currency": self.confirmed_cash_currency}, None

        except Exception as e:
            err_msg = f"_confirm_award_payment_details 执行失败: {e}"
            print(f"[!] {err_msg}")
            import traceback
            traceback.print_exc()
            return False, err_msg, "CONFIRM_EXCEPTION"

    def make_payment(self, payment_config_dict: dict, passengers_info_list: list,
                     award_amount_ref: int, cash_amount_ref: float, cash_currency_ref: str="AUD",
                     use_points_for_tax_val: bool=False, auth_data=None, explicit_cookies=None, use_specific_proxy=None):
        auth_data = auth_data or {}; explicit_cookies = explicit_cookies or {}
        actual_award = self.confirmed_award_amount if self.confirmed_award_amount is not None else award_amount_ref
        actual_cash = self.confirmed_cash_amount if self.confirmed_cash_amount is not None else cash_amount_ref
        actual_currency = self.confirmed_cash_currency if self.confirmed_cash_currency else cash_currency_ref
        card_number = payment_config_dict.get("card_number")
        if not use_points_for_tax_val and actual_cash > 0.0 and not card_number: return False, "Card info missing for cash payment", "CARD_MISSING"
        payment_objects_list = [{
            "@type": "AWARD", "amount": {"amount": int(actual_award), "currency": "FFCURRENCY"}, "paymentCode": "AWARD"
        }]
        if not use_points_for_tax_val and actual_cash > 0.0:
            card_code = "VI"; expiry_month = payment_config_dict.get("expiry_month", "01"); expiry_year = payment_config_dict.get("expiry_year", "1900")
            if card_number.startswith("5"): card_code = "CA"
            elif card_number.startswith("3"): card_code = "AX"
            payment_objects_list.append({
                "@type": "CREDIT_CARD", "number": card_number, "cvc": payment_config_dict.get("cvv","000"),
                "holderName": payment_config_dict.get("billing_address",{}).get("holder_name", f"{passengers_info_list[0].get('first_name','F')} {passengers_info_list[0].get('last_name','L')}"),
                "expirationDate": f"{expiry_year}-{int(expiry_month):02d}", "cardCode": card_code,
                "amount": {"amount": float(actual_cash), "currency": actual_currency}, "paymentCode": card_code
            })
        billing_data = {
            "street1": payment_config_dict.get("billing_address",{}).get("street", "1 Street"),
            "city": payment_config_dict.get("billing_address",{}).get("city", "City"),
            "country": payment_config_dict.get("billing_address",{}).get("country_code", "AU"),
            "phone": {"countryCode": "61", "number": payment_config_dict.get("billing_address",{}).get("phone_number", passengers_info_list[0].get('phone','0400000000'))}
        }
        variables = {"payment": payment_objects_list, "billingData": billing_data, "paymentRequired": True if actual_cash > 0.0 and not use_points_for_tax_val else False, "languageForBooking": "en_GB", "fraudNetData": {"deviceFingerPrintId": self.device_fingerprint_id or f"va-ph-{int(time.time())}"}, "remarksAndSSRs": {"remarks": ["h-dxr/flow/b2c-desktop", "h-dxr/channel/none"]}}
        query = """mutation BookingPurchase($payment: [JSONObject!]!, $billingData: JSONObject, $paymentRequired: Boolean, $languageForBooking: String, $fraudNetData: JSONObject, $remarksAndSSRs: JSONObject) { bookingPurchase(payment: $payment, billingData: $billingData, paymentRequired: $paymentRequired, languageForBooking: $languageForBooking, fraudNetData: $fraudNetData, remarksAndSSRs: $remarksAndSSRs) { originalResponse __typename } }"""
        gql_response = self._call_gql("BookingPurchase", variables, query, auth_data, explicit_cookies=explicit_cookies, use_specific_proxy=use_specific_proxy)
        final_purchase_result = gql_response.get("data", {}).get("bookingPurchase")
        if not final_purchase_result:
            err_msg = gql_response.get("errors",[{}])[0].get("message") if gql_response.get("errors") else "Purchase GQL error"
            return False, err_msg, "PURCHASE_GQL_ERROR"
        original_resp_content = final_purchase_result.get("originalResponse", {})
        messages = original_resp_content.get("messages", [])
        biz_errors = [m for m in messages if isinstance(m, dict) and m.get("type", "").upper() == "ERROR"]
        if biz_errors:
            return False, biz_errors[0].get("text", "Payment business error"), "PAYMENT_BIZ_ERROR"
        booking_ref = original_resp_content.get("bookingReference")
        return True, {"purchase_response": gql_response, "booking_reference": booking_ref}, None

    def _log_request_details(self, url, headers, cookies, payload, operation_name, proxy):
        if operation_name not in ["bookingAirSearch"] or getattr(self, 'debug_mode', False):
             print(f"[API REQ] Op: {operation_name}, Proxy: {proxy}, Token: {headers.get('Authorization','N/A')[:20]}..., ExecID: {headers.get('execution','N/A')}")
