import time
import threading
import queue
import concurrent.futures
from datetime import datetime
import json
import os
import sys
import signal
import copy
from api_client import VAApiClient
import random
from bestV8.demo import Reese84Resolve, session as demo_session
from proxy_manager import get_proxy_manager
import requests

# Removed direct import from ui.py to break circular dependency
# from ui import RedeemApp, get_redis_client
# REDIS_AVAILABLE = False
# try:
#    from ui import RedeemApp, get_redis_client
#    REDIS_AVAILABLE = True
# except ImportError as e:
#    print(f"[抓取器 WARNING] 无法从 ui.py 导入 RedeemApp 或 get_redis_client: {e}. Redis功能将受限或不可用。")
#    def get_redis_client(): return None
#    class RedeemApp:
#        @staticmethod
#        def get_instance(): return None

AKAMAI_CHALLENGE_URL_FOR_REESE84_CRAWLER = 'https://book.virginaustralia.com/side-you-ares-may-Exit-sition-Alaruern-Naugmen-G?d=book.virginaustralia.com'

def search_flights_with_demo(origin, destination, date, cabin="Business", proxy_dict=None):
    """
    使用demo.py的方法进行航班搜索
    这是高频搜索阶段使用的方法，不使用任何Cookie以避免被检测为风险行为
    """
    try:
        # 1. 生成reese84令牌
        base_url = 'https://book.virginaustralia.com/side-you-ares-may-Exit-sition-Alaruern-Naugmen-G?d=book.virginaustralia.com'
        resolver = Reese84Resolve(base_url)
        reese84_result = resolver.resolve(proxy_to_use=proxy_dict)

        if not reese84_result['status']:
            return {"errors": [{"message": f"Reese84生成失败: {reese84_result['data']}"}]}

        reese84_token = reese84_result["data"]["token"]
        user_agent = reese84_result.get("ua", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

        # 2. 准备搜索请求 - 只使用reese84，不使用其他Cookie
        cookies = {'reese84': reese84_token}

        headers = {
            "accept": "*/*",
            "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "adrum": "isAjax:true",
            "cache-control": "no-cache",
            "content-type": "application/json",
            "dc-url;": "",
            "origin": "https://book.virginaustralia.com",
            "pragma": "no-cache",
            "priority": "u=1, i",
            "referer": "https://book.virginaustralia.com/dx/VADX/",
            "sec-ch-ua": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "ssgtoken": "undefined",
            "ssotoken": "undefined",
            "user-agent": user_agent,
            "x-sabre-storefront": "VADX"
        }

        url = "https://book.virginaustralia.com/api/graphql"
        data = {
            "operationName": "bookingAirSearch",
            "variables": {
                "airSearchInput": {
                    "cabinClass": cabin,
                    "awardBooking": True,
                    "promoCodes": [],
                    "searchType": "BRANDED",
                    "itineraryParts": [
                        {
                            "from": {
                                "useNearbyLocations": False,
                                "code": origin
                            },
                            "to": {
                                "useNearbyLocations": False,
                                "code": destination
                            },
                            "when": {
                                "date": date
                            }
                        }
                    ],
                    "passengers": {
                        "ADT": 1
                    }
                }
            },
            "extensions": {},
            "query": "query bookingAirSearch($airSearchInput: CustomAirSearchInput) {\n  bookingAirSearch(airSearchInput: $airSearchInput) {\n    originalResponse\n    __typename\n  }\n}"
        }

        # 3. 设置代理
        original_proxies = demo_session.proxies.copy()
        if proxy_dict:
            demo_session.proxies = proxy_dict
        else:
            demo_session.proxies = {}

        try:
            # 4. 发送请求
            response = demo_session.post(url, headers=headers, cookies=cookies, json=data, timeout=30)

            if response.status_code == 200:
                return response.json()
            else:
                return {"errors": [{"message": f"HTTP {response.status_code}: {response.text[:200]}"}]}

        finally:
            # 恢复原始代理设置
            demo_session.proxies = original_proxies

    except Exception as e:
        return {"errors": [{"message": f"搜索异常: {type(e).__name__} - {e}"}]}


class HighConcurrencyCrawler:
    """高并发航班抓取器，支持50个并发任务，任务报错立即重试，每次请求使用随机IP"""

    def __init__(self, max_workers=50, max_retries=3, app_instance_ref=None):
        """
        初始化高并发抓取器
        :param max_workers: 最大并发任务数，默认50
        :param max_retries: 单个任务最大重试次数，默认3
        """
        self.max_workers = max_workers
        self.max_retries = max_retries
        self.app_instance_ref = app_instance_ref # Store reference to RedeemApp instance
        self.running = False
        self.targets = []  # 抓取目标列表 (will be singular for a task-specific crawler)
        self.api_client_config_template = None # Will store the VAApiClient used for config
        self.task_id_for_redis = None # Task ID to fetch cookies from Redis
        self.executor = None  # 线程池执行器
        self.callback = None  # 找到匹配航班时的回调函数
        self.debug_mode = True # Or get from active_api_client if it has such a setting
        self.debug_dir = "debug_logs" # Should be configured externally or made instance specific
        self.found_match = False  # 是否找到匹配航班
        self.lock = threading.Lock()  # 用于线程安全操作
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "retried_requests": 0,
            "start_time": None,
            "end_time": None
        }
        self.worker_thread = None # Initialize worker_thread attribute
        self.stats_thread = None # Added initialization for stats_thread

        # 使用全局代理管理器
        self.proxy_manager = get_proxy_manager()

        # 创建调试目录 - consider if this should be done by the main app
        if self.debug_mode and not os.path.exists(self.debug_dir):
            try:
                os.makedirs(self.debug_dir)
            except OSError as e:
                self._safe_print(f"[抓取器] 创建调试目录失败: {e} (可能已被其他实例创建)")

    def _safe_print(self, message):
        """安全的打印方法，避免Windows控制台编码问题"""
        try:
            print(message)
        except (OSError, UnicodeEncodeError, UnicodeDecodeError) as e:
            try:
                # 尝试使用ASCII编码
                print(message.encode('ascii', 'ignore').decode('ascii'))
            except:
                try:
                    # 最后尝试简化消息
                    print(f"[LOG] {len(message)} chars message (encoding error)")
                except:
                    pass  # 完全静默失败

    def add_target(self, origin, destination, date, flight_nos_str):
        """
        添加抓取目标 (For a task-specific crawler, this will likely be one target)
        :param origin: 出发地
        :param destination: 目的地
        :param date: 日期
        :param flight_nos_str: 航班号字符串，多个航班号用+分隔
        """
        target = {
            "origin": origin,
            "destination": destination,
            "date": date,
            "flight_nos": flight_nos_str.upper().split('+')
        }
        self.targets.append(target) # Should ideally only be one for a task's crawler
        print(f"[抓取器] 添加目标: {origin}-{destination} {date} 航班: {'+'.join(target['flight_nos'])}")
        return len(self.targets) - 1

    def set_callback(self, callback_function):
        """设置找到目标航班时的回调函数"""
        self.callback = callback_function

    def _get_healthy_proxy(self, exclude_proxy=None):
        """获取一个健康的代理"""
        # 检查是否有代理管理器和代理配置
        if not hasattr(self, 'proxy_manager') or not self.proxy_manager:
            return {}

        # 检查代理管理器是否有可用代理
        proxy_stats = self.proxy_manager.get_proxy_stats()
        if proxy_stats.get('total', 0) == 0:
            return {}

        exclude_proxies = [exclude_proxy] if exclude_proxy else []
        proxy = self.proxy_manager.get_healthy_proxy(exclude_proxies=exclude_proxies)
        return proxy or {}

    def _record_proxy_result(self, proxy, success, response_time=None):
        """记录代理使用结果"""
        if proxy:
            self.proxy_manager.record_proxy_result(proxy, success, response_time)

    def start(self, task_id_for_redis=None, initial_api_client_template=None):
        """
        开始高并发抓取
        :param task_id_for_redis: The task ID to fetch cookies from Redis
        :param initial_api_client_template: The VAApiClient instance for this task.
        """
        if self.running:
            print(f"[HCC TaskMapID:{self.task_id_for_redis or 'N/A'}] 已在运行")
            return False

        if not self.targets:
            print(f"[HCC TaskMapID:{self.task_id_for_redis or 'N/A'}] 错误: 无抓取目标")
            return False

        if task_id_for_redis is None:
            print(f"[HCC TaskMapID:N/A] 错误: 需提供 task_id_for_redis")
            return False

        # app_instance_ref 现在是可选的，因为我们使用demo.py方法
        if not self.app_instance_ref:
            print(f"[HCC TaskMapID:{task_id_for_redis}] 注意: 独立运行模式，无app_instance_ref。")

        self.task_id_for_redis = task_id_for_redis
        self.api_client_config_template = initial_api_client_template if initial_api_client_template else VAApiClient()
        self.found_match = False
        self.stats = {k: (0 if isinstance(v, int) else None) for k, v in self.stats.items()}
        self.stats["start_time"] = datetime.now()
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers)
        self.running = True
        self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
        self.worker_thread.start()
        self.stats_thread = threading.Thread(target=self._stats_loop, daemon=True)
        self.stats_thread.start()
        self._safe_print(f"[HCC TaskMapID:{self.task_id_for_redis}] 开始抓取 (并发: {self.max_workers})")
        return True

    def stop(self):
        """停止抓取"""
        if not self.running:
            # print("[抓取器] 抓取器未在运行") # Can be noisy if called multiple times
            return

        self._safe_print(f"[抓取器 TaskMapID:{self.task_id_for_redis}] 正在停止抓取器...")
        self.running = False # Signal threads to stop

        # 关闭线程池 - use try-except as executor might be None if start failed early
        try:
            if self.executor:
                # 首先尝试取消所有未开始的任务
                try:
                    self.executor.shutdown(wait=False, cancel_futures=True) # Python 3.9+
                    self._safe_print(f"[抓取器 TaskMapID:{self.task_id_for_redis}] 线程池已关闭，等待任务完成...")
                    # 给一些时间让正在运行的任务检查self.running状态并退出
                    time.sleep(2)
                except TypeError:
                    # Python < 3.9 不支持 cancel_futures 参数
                    self.executor.shutdown(wait=False)
                    self._safe_print(f"[抓取器 TaskMapID:{self.task_id_for_redis}] 线程池已关闭（旧版Python）...")
                    time.sleep(2)
        except Exception as e_shutdown:
            self._safe_print(f"[抓取器] 关闭线程池时发生错误: {e_shutdown}")


        # 等待工作线程结束 (worker_thread might not have started if start failed early)
        if self.worker_thread and self.worker_thread.is_alive():
            try:
                self.worker_thread.join(timeout=5)
                if self.worker_thread.is_alive():
                    self._safe_print("[抓取器] 警告: 工作线程未在5秒内结束。")
            except Exception as e_join_worker:
                 self._safe_print(f"[抓取器] 等待工作线程结束时出错: {e_join_worker}")


        # 等待统计线程结束
        if hasattr(self, 'stats_thread') and self.stats_thread.is_alive():
            try:
                self.stats_thread.join(timeout=2)
            except Exception as e_join_stats:
                self._safe_print(f"[抓取器] 等待统计线程结束时出错: {e_join_stats}")


        # 记录结束时间
        self.stats["end_time"] = datetime.now()

        # 打印统计信息
        self._print_final_stats()

        self._safe_print(f"[抓取器 TaskMapID:{self.task_id_for_redis}] 已停止抓取器")

    def _worker_loop(self):
        """工作线程循环，负责提交任务到线程池"""
        futures = []
        if not self.targets:
            self.running = False
            self._safe_print(f"[HCC TaskMapID:{self.task_id_for_redis}] Worker: 无目标。")
            return

        target_info = self.targets[0]
        target_idx = 0
        log_prefix = f"[HCC TaskMapID:{self.task_id_for_redis}]"

        self._safe_print(f"{log_prefix} 工作线程开始运行...")

        try:
            while self.running and not self.found_match:
                # 更频繁地检查停止状态
                if not self.running or self.found_match:
                    self._safe_print(f"{log_prefix} 工作循环检测到停止信号，退出")
                    break

                active_futures_count = sum(1 for f in futures if not f.done())
                num_to_submit = self.max_workers - active_futures_count

                for _ in range(num_to_submit):
                    if not self.running or self.found_match:
                        break
                    try:
                        future = self.executor.submit(self._search_task, target_idx, target_info["origin"], target_info["destination"], target_info["date"], target_info["flight_nos"])
                        futures.append(future)
                    except Exception as e_submit:
                        self._safe_print(f"{log_prefix} 提交搜索任务失败: {e_submit}")

                temp_futures = []
                for f_idx, f_val in enumerate(futures):
                    if f_val.done():
                        try:
                            f_val.result()
                        except Exception as e_f:
                            self._safe_print(f"{log_prefix} Future {f_idx} 结果异常: {e_f}")
                    else:
                        temp_futures.append(f_val)
                futures = temp_futures

                # 增加休息时间，降低请求频率以避免403错误
                for _ in range(50):  # 0.5秒总休息时间，分成50个0.01秒检查
                    if not self.running or self.found_match:
                        break
                    time.sleep(0.01)

        except Exception as e_worker:
            self._safe_print(f"{log_prefix} 工作线程异常: {e_worker}")
            import traceback
            traceback.print_exc()
        finally:
            # 取消所有剩余的futures
            for future in futures:
                if not future.done():
                    future.cancel()

            self._safe_print(f"{log_prefix} 工作线程结束，已取消 {len([f for f in futures if not f.done()])} 个未完成任务")
            self.running = False

    def _search_task(self, target_idx, origin, destination, date, target_flight_nos, retry_count=0):
        log_prefix = f"[HCC TaskMapID:{self.task_id_for_redis}]"
        if self.found_match or not self.running: return

        # 为demo.py搜索方法选择代理
        api_proxy_dict = self._get_healthy_proxy() if hasattr(self, '_get_healthy_proxy') else {}

        self._safe_print(f"{log_prefix} (Tgt {target_idx}) Attempt {retry_count + 1}: 使用demo.py方法, 代理: {api_proxy_dict.get('http', 'Direct') if api_proxy_dict else 'Direct'}")

        # 再次检查运行状态
        if self.found_match or not self.running: return

        # 注意：reese84生成现在已经集成到search_flights_with_demo函数中，无需单独生成

        # 最后一次检查运行状态（搜索前）
        if self.found_match or not self.running: return

        try:
            with self.lock: self.stats["total_requests"] += 1
            self._safe_print(f"{log_prefix} 搜索航班 {origin}→{destination} {date} | 使用demo.py方法 (代理: {api_proxy_dict.get('http', 'Direct') if api_proxy_dict else 'Direct'})")

            # 使用demo.py的方法进行高频搜索
            api_start_time = time.time()
            search_results = search_flights_with_demo(
                origin=origin,
                destination=destination,
                date=date,
                cabin=self.targets[target_idx].get("cabin", "Business"),
                proxy_dict=api_proxy_dict
            )
            api_response_time = time.time() - api_start_time

            # 简化搜索结果日志
            if isinstance(search_results, dict):
                if search_results.get("errors"):
                    error_msg = str(search_results['errors'])[:100]
                    self._safe_print(f"{log_prefix} ❌ 搜索失败: {error_msg}... (耗时: {api_response_time:.1f}s)")
                elif search_results.get("data"):
                    # 检查是否有航班数据
                    offers_data = search_results.get("data", {}).get("bookingAirSearch", {}).get("originalResponse", {})
                    unbundled_offers = offers_data.get("unbundledOffers", [])
                    if unbundled_offers and unbundled_offers[0]:
                        offer_count = len(unbundled_offers[0])
                        self._safe_print(f"{log_prefix} ✅ 搜索成功: 找到 {offer_count} 个航班选项 (耗时: {api_response_time:.1f}s)")
                    else:
                        self._safe_print(f"{log_prefix} ⚠️  搜索成功但无航班数据 (耗时: {api_response_time:.1f}s)")
                else:
                    self._safe_print(f"{log_prefix} ⚠️  搜索返回空数据 (耗时: {api_response_time:.1f}s)")
            else:
                self._safe_print(f"{log_prefix} ❌ 搜索返回异常格式 (耗时: {api_response_time:.1f}s)")


            # 记录API代理成功
            self._record_proxy_result(api_proxy_dict, True, api_response_time)

            with self.lock: self.stats["successful_requests"] += 1
            if self.debug_mode and random.random() < 0.05: self._save_debug_data(search_results, target_idx)

            current_target_info = self.targets[target_idx]
            found_this_call, match_info = self._check_search_results(search_results, target_flight_nos, current_target_info)

            if found_this_call:
                self._safe_print(f"{log_prefix} 🎉 找到匹配航班: {'+'.join(target_flight_nos)} ({current_target_info.get('cabin', 'Business')})")
                with self.lock:
                    if not self.found_match:
                        self.found_match = True; self.running = False
                        if match_info and isinstance(match_info, dict):
                            match_info['proxy_used_for_success'] = api_proxy_dict # 记录成功使用的API代理
                        if self.callback:
                            self._safe_print(f"{log_prefix} 触发回调函数...")
                            self.callback(match_info, self.task_id_for_redis)
            else:
                self._safe_print(f"{log_prefix} 未找到匹配航班: {'+'.join(target_flight_nos)} ({current_target_info.get('cabin', 'Business')})")
            return
        except Exception as e:
            # 记录API代理失败
            self._record_proxy_result(api_proxy_dict, False)

            with self.lock: self.stats["failed_requests"] += 1
            # Log detailed error for search_flights failure
            log_message_detail = f"(Tgt {target_idx}) Search flights attempt {retry_count + 1} FAILED. API代理: {api_proxy_dict.get('http', 'Direct')}. Error: {type(e).__name__} - {e}"
            task_for_log = self.app_instance_ref.get_task_by_id(self.task_id_for_redis) if self.app_instance_ref else None
            if task_for_log:
                task_for_log.log_task_message(log_message_detail, "red", include_traceback=True)
            else:
                self._safe_print(f"{log_prefix} {log_message_detail}")
                if self.debug_mode: # Fallback for traceback if no task logger
                    import traceback
                    traceback.print_exc()

            if retry_count < self.max_retries and self.running and not self.found_match:
                with self.lock: self.stats["retried_requests"] += 1
                # 如果是403错误，使用更长的等待时间并切换代理
                if "HTTP 403" in str(e):
                    wait_time = random.uniform(10.0, 20.0 + retry_count * 5.0)
                    self._safe_print(f"{log_prefix} 检测到403错误，等待 {wait_time:.1f}s 后重试...")
                    # 记录代理失败，下次重试时会选择新代理
                    self._record_proxy_result(api_proxy_dict, False)
                else:
                    wait_time = random.uniform(1.5, 3.5 + retry_count * 0.5)
                time.sleep(wait_time)
                return self._search_task(target_idx, origin, destination, date, target_flight_nos, retry_count + 1)
            return

    def _check_search_results(self, search_results, target_flight_nos, current_target_params):
        """
        检查搜索结果中是否有匹配的航班
        :param search_results: 搜索结果
        :param target_flight_nos: 目标航班号列表 (用户输入的，如 ['NH123'])
        :param current_target_params: 当前挂单目标的参数 (字典，包含 'origin', 'destination', 'date')
        :return: (是否找到匹配, 匹配信息字典 或 None)
        """
        log_prefix = f"[HCC TaskMapID:{self.task_id_for_redis}]"
        try:
            if not search_results: # Basic check for empty response
                task_obj = self.app_instance_ref.get_task_by_id(self.task_id_for_redis) if self.app_instance_ref else None
                log_msg = f"{log_prefix} 检查结果：search_flights 返回了空结果 (None or empty)."
                if task_obj: task_obj.log_task_message(log_msg, "yellow")
                else: self._safe_print(log_msg)
                return False, None

            if search_results.get("errors") and not search_results.get("data"):
                task_obj = self.app_instance_ref.get_task_by_id(self.task_id_for_redis) if self.app_instance_ref else None
                error_summary = str(search_results.get("errors"))[:250] # Log a summary of errors
                log_msg = f"{log_prefix} 检查结果：search_flights 返回 GraphQL 错误: {error_summary}..."
                if task_obj: task_obj.log_task_message(log_msg, "yellow")
                else: self._safe_print(log_msg)
                return False, None

            if not search_results.get("data"): # Check for missing data key if not errors
                task_obj = self.app_instance_ref.get_task_by_id(self.task_id_for_redis) if self.app_instance_ref else None
                log_msg = f"{log_prefix} 检查结果：search_flights 返回结果中无 'data' 键。结果: {str(search_results)[:200]}..."
                if task_obj: task_obj.log_task_message(log_msg, "yellow")
                else: self._safe_print(log_msg)
                return False, None

            offers_data = search_results.get("data", {}).get("bookingAirSearch", {}).get("originalResponse", {})
            if not offers_data:
                task_obj = self.app_instance_ref.get_task_by_id(self.task_id_for_redis) if self.app_instance_ref else None
                log_msg = f"{log_prefix} 检查结果：未在 search_results.data.bookingAirSearch 中找到 originalResponse。Data: {str(search_results.get('data'))[:200]}..."
                if task_obj: task_obj.log_task_message(log_msg, "yellow")
                else: self._safe_print(log_msg)
                return False, None

            unbundled_offers_list = offers_data.get("unbundledOffers")
            if not unbundled_offers_list or not isinstance(unbundled_offers_list, list) or not unbundled_offers_list[0]:
                # print("[抓取器] 检查结果：未找到 unbundledOffers 或列表为空。") # Noisy
                return False, None

            offers = unbundled_offers_list[0] # This is a list of offer objects

            # 获取 cabin class to search from current_target_params if specified, else default to Business
            # This is important if user can specify cabin in the input task.
            # The search_flights in VAApiClient might already filter by a default cabin.
            # Here, we ensure the *found* offer matches what we *expect* for this task.
            expected_cabin_class = current_target_params.get("cabin", "Business").lower()


            for offer in offers:
                itinerary_part = offer.get("itineraryPart", [{}])[0]
                if not itinerary_part: continue

                segments = itinerary_part.get("segments", [])
                if not segments: continue

                offer_flight_nos_found = []
                offer_cabin_classes_found = []
                all_segments_match_expected_cabin = True

                for seg in segments:
                    flight = seg.get("flight", {})
                    flight_no = flight.get("flightNumber")
                    airline = flight.get("airlineCode")
                    if flight_no and airline:
                        offer_flight_nos_found.append(f"{airline}{flight_no}")

                    cabin_class_segment = seg.get("cabinClass", "").lower()
                    offer_cabin_classes_found.append(seg.get("cabinClass", "")) # Store original casing for signature

                    # Check if this segment's cabin matches the expected cabin for the task
                    if cabin_class_segment != expected_cabin_class:
                        all_segments_match_expected_cabin = False
                        # print(f"[抓取器 DEBUG] Offer segment cabin '{cabin_class_segment}' != expected '{expected_cabin_class}'")
                        break # This offer is not a match for the expected cabin class for all segments

                if not all_segments_match_expected_cabin:
                    continue # Move to the next offer

                # Compare flight numbers (case-insensitive for matching)
                # target_flight_nos comes from user input, could be e.g., ["VA9", "VA123"]
                # offer_flight_nos_found comes from API, e.g., ["VA009", "VA0123"]
                # We need a flexible match.

                # Normalize both lists for comparison: uppercase and remove leading zeros from flight number part
                def normalize_flight_list(fl_list):
                    normalized = []
                    for fn_full in fl_list:
                        fn_full_upper = fn_full.upper()
                        # Separate airline code (non-digits at start) from number part
                        airline_code = "".join(filter(str.isalpha, fn_full_upper))
                        number_part_str = "".join(filter(str.isdigit, fn_full_upper))
                        if number_part_str: # Convert to int to remove leading zeros, then back to str
                             normalized.append(f"{airline_code}{int(number_part_str)}")
                        else: # Should not happen for valid flight numbers
                             normalized.append(fn_full_upper)
                    return sorted(normalized)

                normalized_target_flights = normalize_flight_list(target_flight_nos)
                normalized_offer_flights = normalize_flight_list(offer_flight_nos_found)

                if normalized_target_flights == normalized_offer_flights:
                    # Flight numbers and cabin classes match expectations.

                    flight_signature_segments = []
                    for seg_detail in segments:
                        flight_data = seg_detail.get("flight", {})
                        flight_signature_segments.append({
                            "origin": seg_detail.get("origin"),
                            "destination": seg_detail.get("destination"),
                            "departure": seg_detail.get("departure"),
                            "arrival": seg_detail.get("arrival"),
                            "airline": flight_data.get("airlineCode"),
                            "flightNumber": flight_data.get("flightNumber")
                        })

                    flight_signature = {
                        "origin": current_target_params.get("origin"),
                        "destination": current_target_params.get("destination"),
                        "date": current_target_params.get("date"), # YYYY-MM-DD
                        "cabinClass": current_target_params.get("cabin", "Business"), # Cabin used for this task's search
                        "flight_numbers_queried": target_flight_nos,
                        "offer_flight_numbers_found": offer_flight_nos_found, # Actual from API
                        "offer_cabin_classes_found": offer_cabin_classes_found, # Actual from API
                        "offer_segments_details": flight_signature_segments
                    }

                    match_info = {
                        "original_offer_data": offer,
                        "original_shoppingBasketHashCode": offer.get("shoppingBasketHashCode"),
                        "flight_signature": flight_signature,
                        "found_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    }
                    self._safe_print(f"\033[92m[抓取器] 匹配成功! Offer Hash: {match_info['original_shoppingBasketHashCode']}\033[0m")
                    self._safe_print(f"\033[92m    Flight Sig: O={flight_signature['origin']}, D={flight_signature['destination']}, Date={flight_signature['date']}, Cabin={flight_signature['cabinClass']}\033[0m")
                    self._safe_print(f"\033[92m    Offer Flights: {flight_signature['offer_flight_numbers_found']}\033[0m")
                    return True, match_info

            return False, None

        except Exception as e:
            self._safe_print(f"{log_prefix} Check results error: {e}")
            # import traceback
            # print(traceback.format_exc()) # For deeper debug
            return False, None

    def log_green(self, msg):
        self._safe_print(f"\033[92m{msg}\033[0m")

    def _save_debug_data(self, data, target_idx):
        """
        保存调试数据
        :param data: 要保存的数据
        :param target_idx: 目标索引 (may not be very relevant if crawler is for one target)
        """
        try:
            # Use a generic name or include task-specific ID if available through active_api_client
            task_identifier = "unknown_task"
            if self.api_client_config_template and hasattr(self.api_client_config_template, 'task_id_for_logging'): # Hypothetical
                task_identifier = self.api_client_config_template.task_id_for_logging
            elif self.targets and target_idx < len(self.targets):
                 target = self.targets[target_idx]
                 task_identifier = f"{target.get('origin','NA')}_{target.get('destination','NA')}"


            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f") # Added microseconds for uniqueness
            file_path = os.path.join(
                self.debug_dir,
                f"crawler_debug_{task_identifier}_{timestamp}.json"
            )

            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            # print(f"[抓取器] 调试数据已保存: {file_path}") # Can be noisy

        except Exception as e:
            self._safe_print(f"[抓取器] 保存调试数据失败: {e}")

    def _stats_loop(self):
        """统计线程循环，定期打印统计信息"""
        log_prefix = f"[HCC TaskMapID:{self.task_id_for_redis} Stats]"
        last_print_time = time.time()
        last_total_requests = 0

        try:
            while self.running: # Loop as long as the crawler is running
                current_time = time.time()
                if current_time - last_print_time >= 10: # Print stats every 10 seconds
                    with self.lock: # Ensure thread-safe access to stats
                        total_req = self.stats["total_requests"]
                        successful_req = self.stats["successful_requests"]
                        failed_req = self.stats["failed_requests"]
                        retried_req = self.stats["retried_requests"]

                    # Calculate requests per second since last print
                    elapsed_time_stats = current_time - last_print_time
                    requests_this_interval = total_req - last_total_requests
                    rps = requests_this_interval / elapsed_time_stats if elapsed_time_stats > 0 else 0

                    last_total_requests = total_req
                    last_print_time = current_time

                    self._safe_print(f"{log_prefix} 总请求: {total_req}, 成功: {successful_req}, "
                                   f"失败: {failed_req}, 重试: {retried_req}, "
                                   f"速率: {rps:.2f} 请求/秒")

                time.sleep(1) # Check self.running more frequently than printing stats
        except Exception as e:
            self._safe_print(f"{log_prefix} 统计线程出错: {e}")
        finally:
            self._safe_print(f"{log_prefix} 统计线程结束")


    def _print_final_stats(self):
        """打印最终统计信息"""
        log_prefix = f"[HCC TaskMapID:{self.task_id_for_redis} FinalStats]"
        if not self.stats["start_time"] or self.stats["total_requests"] == 0 : # Avoid division by zero if no requests
            self._safe_print(f"{log_prefix} 无请求或未开始。")
            return

        end_time = self.stats["end_time"] or datetime.now()
        duration = (end_time - self.stats["start_time"]).total_seconds()
        if duration <= 0: duration = 0.01 # Avoid division by zero if duration is too short

        with self.lock: # Ensure thread-safe access
            total_requests = self.stats["total_requests"]
            successful_requests = self.stats["successful_requests"]
            failed_requests = self.stats["failed_requests"]
            retried_requests = self.stats["retried_requests"]

        success_rate = (successful_requests / total_requests * 100) if total_requests > 0 else 0
        failure_rate = (failed_requests / total_requests * 100) if total_requests > 0 else 0
        requests_per_second = total_requests / duration

        self._safe_print("\n" + "="*50)
        self._safe_print(f"{log_prefix}")
        self._safe_print(f"运行时间: {duration:.2f} 秒")
        self._safe_print(f"总请求数: {total_requests}")
        self._safe_print(f"成功请求: {successful_requests} ({success_rate:.2f}%)")
        self._safe_print(f"失败请求: {failed_requests} ({failure_rate:.2f}%)")
        self._safe_print(f"重试请求: {retried_requests}")
        self._safe_print(f"平均速率: {requests_per_second:.2f} 请求/秒")
        self._safe_print("="*50 + "\n")

    def is_alive_internal(self):
        """
        检查爬虫是否存活
        返回True表示爬虫正在运行或正在启动
        返回False表示爬虫已停止或启动失败
        """
        # 如果明确设置为不运行，则认为已死亡
        if not self.running:
            return False

        # 如果设置为运行状态，检查工作线程
        if self.worker_thread is None:
            # 线程还未创建，可能正在启动过程中，给一些时间
            return True

        # 检查工作线程是否存活
        if self.worker_thread.is_alive():
            return True

        # 工作线程已死亡但running标志仍为True，这表示可能的异常情况
        # 检查是否是正常完成（找到匹配）
        if hasattr(self, 'found_match') and self.found_match:
            # 如果找到了匹配，线程正常结束，应该停止监控
            return False

        # 检查启动时间，如果刚启动不久，可能是正常的初始化过程
        if hasattr(self, 'stats') and self.stats.get('start_time'):
            time_since_start = (datetime.now() - self.stats['start_time']).total_seconds()
            if time_since_start < 15:  # 给15秒的启动缓冲时间
                return True

        # 如果工作线程死亡且不是因为找到匹配，且已经启动超过15秒，认为是异常
        return False

# 命令行直接运行时的入口
if __name__ == "__main__":
    # This main block is for testing the crawler independently.
    # It will need a valid token and execution_id to run.
    print("高并发抓取器测试")

    # --- 配置 ---
    # 重要: 请替换为有效的测试token和execution_id
    TEST_TOKEN = "YOUR_VALID_IBEOPENTOKEN_HERE"  # 替换!
    TEST_EXECUTION_ID = "YOUR_VALID_EXECUTION_ID_HERE"  # 替换!

    if TEST_TOKEN == "YOUR_VALID_IBEOPENTOKEN_HERE" or TEST_EXECUTION_ID == "YOUR_VALID_EXECUTION_ID_HERE":
        print("错误: 请在 high_concurrency_crawler.py 的 __main__ 部分设置有效的 TEST_TOKEN 和 TEST_EXECUTION_ID。")
        sys.exit(1)

    # 创建一个临时的API客户端实例用于测试
    test_api_client = VAApiClient()
    test_api_client.token = TEST_TOKEN
    test_api_client.execution_id = TEST_EXECUTION_ID
    # test_api_client.proxies = ["*********************:port"] # 可选: 配置代理
    # test_api_client.use_proxy = True


    crawler = HighConcurrencyCrawler(max_workers=5, max_retries=2) # 低并发测试

    # 添加测试目标 (示例: PVG-SYD)
    # crawler.add_target("PVG", "SYD", "2025-03-10", "VA5008+VA825") # 示例，可能无此航班
    crawler.add_target("SYD", "MEL", "2024-09-25", "VA811") # 替换为你想测试的实际存在的航班和日期


    # 设置回调函数
    def found_callback(match_info, task_id_cbk):
        print("\n" + "*"*20 + " 测试回调: 找到匹配航班! " + "*"*20)
        print(json.dumps(match_info, indent=2, ensure_ascii=False))
        print("*"*60 + "\n")
        # 在回调中停止爬虫，因为我们只关心第一次匹配
        # crawler.stop() # Crawler now stops itself upon first match if callback is successful

    crawler.set_callback(found_callback)

    # 启动抓取 (传入测试API客户端)
    if crawler.start(task_id_for_redis="test_hcc_task", initial_api_client_template=test_api_client):
        print("测试抓取器已启动。按 Ctrl+C 停止...")

        # 保持主线程运行，直到找到匹配或手动停止
        try:
            while crawler.running and not crawler.found_match:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n测试抓取器被用户中断...")
        finally:
            if crawler.running:
                crawler.stop()
    else:
        print("测试抓取器启动失败。")

    print("测试结束。")
